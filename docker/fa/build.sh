#!/bin/bash

set -e

IMAGE_NAME="fav3"

echo "🚀 Building factor-analysis images for both architectures..."

# 构建ARM64镜像  
echo "📦 Building ARM64 image..."
docker build --build-arg TARGET_ARCH=arm64 --platform=linux/arm64 -t ${IMAGE_NAME}:arm64 --no-cache  .
echo "✅ ARM64 build completed: ${IMAGE_NAME}:arm64"

# 构建AMD64镜像
echo "📦 Building AMD64 image..."
docker build --build-arg TARGET_ARCH=amd64 --platform=linux/amd64 -t ${IMAGE_NAME}:amd64 .
echo "✅ AMD64 build completed: ${IMAGE_NAME}:amd64"
