# 多阶段构建 - TA-Lib编译阶段
ARG TARGET_ARCH=amd64
FROM py312:${TARGET_ARCH} AS talib-builder

WORKDIR /build
COPY rootfs/ta-lib-0.6.4-src.tar.gz ./

# 安装TA-Lib编译依赖并编译
RUN apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
    build-essential \
    python3-dev \
    autoconf \
    automake \
    libtool \
    && \
    # 编译TA-Lib
    tar -xf ta-lib-0.6.4-src.tar.gz && \
    cd ta-lib-0.6.4 && \
    ./configure --prefix=/usr/local && \
    make && \
    make install && \
    # 安装Python TA-Lib包
    export TA_LIBRARY_PATH=/usr/local/lib && \
    export TA_INCLUDE_PATH=/usr/local/include && \
    pip install --no-cache-dir TA-Lib~=0.6 && \
    # 清理编译文件
    cd .. && \
    rm -rf ta-lib-0.6.4 ta-lib-0.6.4-src.tar.gz && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 最终运行时阶段
FROM py312:${TARGET_ARCH}

WORKDIR /

# 从编译阶段复制TA-Lib库文件和Python包（使用正确的文件名）
COPY --from=talib-builder /usr/local/lib/libta-lib* /usr/local/lib/
COPY --from=talib-builder /usr/local/include/ta-lib /usr/local/include/ta-lib
COPY --from=talib-builder /opt/venv/lib/python3.12/site-packages/talib /opt/venv/lib/python3.12/site-packages/talib
COPY --from=talib-builder /opt/venv/lib/python3.12/site-packages/TA_Lib* /opt/venv/lib/python3.12/site-packages/

# 安装运行时依赖和其他Python包
RUN apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
    graphviz \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    fontconfig \
    && \
    # 刷新字体缓存
    fc-cache -fv && \
    # 更新动态链接库缓存
    ldconfig && \
    # 安装其他Python包
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir \
    scikit-learn==1.6.1 \
    plotly==5.24.1 \
    plotly-express==0.4.1 \
    optuna \
    pyfolio-reloaded \
    alphalens-reloaded==0.4.5 \
    freezegun \
    backtrader \
    lightgbm==4.5.0 \
    tushare \
    polars \
    pyarrow \
    akshare~=1.16.81 \
    akracer[py_mini_racer]==0.0.13 \
    tqdm==4.67.1 \
    jsonpath==0.82.2 \
    openpyxl==3.1.5 \
    xlrd==2.0.1 \
    ipywidgets==8.1.6 \
    && \
    # 清理缓存
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    pip cache purge

EXPOSE 8888
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
