logging:
  version: 1
  disable_existing_loggers: false
  formatters:
    default:
      format: '%(asctime)s %(levelname)-1.1s %(process)d %(name)s:%(funcName)s:%(lineno)s
        | %(message)s'
    backtest: 
      format: '[回测] %(bt_date)s | %(message)s'
  handlers:
    console:
      class: logging.StreamHandler
      formatter: default
    file:
      class: logging.handlers.RotatingFileHandler
      formatter: default
      filename: /tmp/course.log
      maxBytes: 10485760
      backupCount: 10
      encoding: utf-8
    backtest:
      class: logging.StreamHandler
      formatter: backtest
  loggers:
    apscheduler:
      level: INFO
    pyemit:
      level: INFO
    omicron.strategy:
      level: INFO 
      handlers: [backtest]
      propagate: false
  root:
    handlers:
      - console
    level: INFO
redis:
  #dsn: redis://172.17.0.1:6379
  dsn: redis://192.168.100.101:56379
influxdb:
  url: http://192.168.100.101:58086
  token: hwxHycJfp_t6bCOYe2MhEDW4QBOO4FDtgeBWnPR6bGZJGEZ_41m_OHtTJFZKyD2HsbVqkZM8rJNkMvjyoXCG6Q==
  org: zillionare
  bucket_name: zillionare
  enable_compress: true
  max_query_size: 5000
alpha:
    data_home: ~/zillionare/alpha/data
    tts_server: http://127.0.0.1:5002/api/tts?
notify:
    dingtalk_access_token: 58df072143b52368086736cb38236753073ccde6537650cad1d5567747803563
    keyword: trader
backtest:
    url: http://***************:7080/backtest/api/trade/v0.5/
    #url: http://localhost:7080/backtest/api/trade/v0.4/
tasks:
  pooling: false
  wr: false
pluto:
  store: ~/zillionare/pluto/store
