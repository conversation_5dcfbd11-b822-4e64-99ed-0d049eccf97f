FROM ubuntu:focal

ENV TZ=Asia/Shanghai
ARG DEBIAN_FRONTEND=noninteractive
WORKDIR /
COPY rootfs ./

RUN apt-get update \
    && apt-get install -qq --no-install-recommends -y wget \
    && apt-get install -qq --no-install-recommends -y python3.8 python3-pip build-essential vim iputils-ping libssl-dev libcurl4-openssl-dev python3.8-dev fontconfig \
    && ln -s /usr/bin/python3 /usr/bin/python \
    && (echo 'root:root' | chpasswd) \
    && pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ \
    && pip config set global.extra-index-url https://pypi.org/simple/ \
    && pip config set global.progress-bar off \
    && pip install pycurl Cython==0.29.33 \
    && cat /root/ta-lib-0.4.0-src.tar.gz | tar -xzv -C /tmp/ \
    && cd /tmp/ta-lib \
    && ./configure --prefix=/usr \
    && make \
    && make install \
    && pip install ta-lib==0.4.25 numpy==1.24.4 --no-deps \
    && pip install jqdatasdk==1.8.11 alphalens-reloaded quantstats akshare==1.16.72 aiohttp==3.8.4 akracer[py_mini_racer]==0.0.13 tqdm==4.67.1 jsonpath==0.82.2 openpyxl==3.1.5 xlrd==2.0.1 zigzag-reload==0.3.4 \
    && pip install jupyterlab==4.2.4 jupyterlab-myst typing-extensions==4.12.2 matplotlib ipywidgets==8.1.6 \
    && pip install /root/zillionare_omicron-2.0.0a79-py3-none-any.whl \
    && pip install /root/zillionare_trader_client-0.4.4-py3-none-any.whl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
    && mkdir -p /usr/share/fonts/truetype \
    && cp /root/WenQuanWeiMiHei.ttf  /usr/share/fonts/truetype/ \
    && cp /root/WenQuanWeiMiHei.ttf /usr/local/lib/python3.8/dist-packages/matplotlib/mpl-data/fonts \
    && cp /root/matplotlibrc /usr/local/lib/python3.8/dist-packages/matplotlib/mpl-data/ \
    && fc-cache -fv

EXPOSE 8888

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
# ENTRYPOINT ["/bin/bash"]
