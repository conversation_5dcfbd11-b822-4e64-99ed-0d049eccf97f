# Python 3.12 JupyterLab 多架构Docker镜像

这个目录包含了支持多架构（AMD64和ARM64）的Python 3.12 JupyterLab环境Docker镜像构建文件。

## 架构支持

- **linux/amd64** - Intel/AMD 64位处理器
- **linux/arm64** - ARM 64位处理器（如Apple Silicon M1/M2）

## 镜像内容

### 基础环境
- Ubuntu 22.04
- Miniconda3 (架构特定版本)
- Python 3.12

### 预装软件包
- JupyterLab
- JupyterLab-MyST
- NumPy
- Pandas
- Matplotlib
- Typing Extensions

### 系统工具
- Vim
- Wget
- 中文字体支持（文泉驿微米黑）

## 构建方法

### 前置要求

- **OrbStack用户**：buildx默认可用，无需额外配置
- **Docker Desktop用户**：需要启用buildx功能

### 1. 多架构构建（推荐）

使用提供的构建脚本：

```bash
# 构建并推送多架构镜像
./build-multiarch.sh

# 或者显式指定build命令
./build-multiarch.sh build
```

> **OrbStack提示**：OrbStack原生支持多架构构建，性能更优

### 2. 本地单架构构建

用于本地测试的单架构构建：

```bash
# 构建AMD64版本
./build-multiarch.sh local --arch amd64

# 构建ARM64版本
./build-multiarch.sh local --arch arm64
```

### 3. 手动构建

如果需要手动控制构建过程：

```bash
# 创建buildx builder
docker buildx create --name multiarch-builder --driver docker-container --use
docker buildx inspect --bootstrap

# 构建多架构镜像
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --tag py312:latest \
  --push \
  .

# 构建本地镜像（仅AMD64）
docker buildx build \
  --platform linux/amd64 \
  --tag py312:latest-amd64 \
  --load \
  .
```

## 使用方法

### 运行容器

```bash
# 基本运行
docker run -p 8888:8888 py312:latest

# 挂载工作目录
docker run -p 8888:8888 \
  -v /path/to/notebooks:/home/<USER>/notebooks \
  py312:latest

# 设置环境变量
docker run -p 8888:8888 \
  -e course=mycourse \
  -e user=myuser \
  py312:latest
```

### 环境变量

- `course` - 课程名称，用于设置JupyterLab的base URL
- `user` - 用户名称，用于设置notebook目录和base URL

## 文件结构

```
docker/py312/
├── Dockerfile              # 多架构Dockerfile
├── Dockerfile.amd64        # AMD64专用Dockerfile（已弃用）
├── Dockerfile.arm64        # ARM64专用Dockerfile（已弃用）
├── build-multiarch.sh      # 多架构构建脚本
├── README.md              # 本文档
└── rootfs/                # 根文件系统文件
    ├── etc/
    │   └── apt/
    │       └── sources.list
    ├── root/
    │   ├── WenQuanWeiMiHei.ttf
    │   ├── matplotlibrc
    │   └── ta-lib-0.6.4-src.tar.gz
    └── usr/
        └── local/
            └── bin/
                └── entrypoint.sh
```

## 架构差异处理

### Miniconda安装
- **AMD64**: `Miniconda3-latest-Linux-x86_64.sh`
- **ARM64**: `Miniconda3-latest-Linux-aarch64.sh`

### 包管理
- 所有Python包都通过pip安装，支持两种架构
- 使用阿里云PyPI镜像加速下载

## 故障排除

### 1. buildx不可用
```bash
# 检查buildx版本
docker buildx version

# OrbStack用户：buildx应该默认可用
# Docker Desktop用户：需要在设置中启用buildx
```

### 2. OrbStack特定问题
```bash
# 检查OrbStack状态
docker context ls

# 切换到OrbStack context（如果需要）
docker context use orbstack

# 重启OrbStack（如果遇到问题）
# 通过OrbStack菜单重启
```

### 3. 架构不匹配
确保使用正确的平台标识：
- `linux/amd64` 用于Intel/AMD处理器
- `linux/arm64` 用于ARM处理器（如Apple Silicon）

### 4. 网络问题
如果下载Miniconda失败，可以：
- 检查网络连接
- 使用代理
- 手动下载并放入构建上下文

### 5. 性能优化（OrbStack）
OrbStack用户可以享受：
- 更快的构建速度
- 更低的内存占用
- 更好的文件系统性能

## 注意事项

1. **TA-Lib**: 此基础镜像不包含TA-Lib，如需要请在后续镜像中安装
2. **多架构推送**: 需要配置Docker registry才能推送多架构镜像
3. **构建时间**: ARM64架构的构建时间可能较长
4. **内存需求**: 建议至少4GB内存用于构建

## 版本历史

- **v1.0**: 初始版本，支持AMD64和ARM64
- **v1.1**: 移除TA-Lib依赖，优化构建脚本
