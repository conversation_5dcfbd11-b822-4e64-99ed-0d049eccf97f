#!/bin/bash

set -e

IMAGE_NAME="py312"

echo "🚀 Building py312 images for both architectures..."

# 构建AMD64镜像
echo "📦 Building AMD64 image..."
docker build --build-arg TARGET_ARCH=arm64 --platform linux/amd64 -t ${IMAGE_NAME}:amd64 .
echo "✅ AMD64 build completed: ${IMAGE_NAME}:amd64"

# 构建ARM64镜像  
echo "📦 Building ARM64 image..."
docker build --no-cache --build-arg TARGET_ARCH=arm64 --platform linux/arm64 -t ${IMAGE_NAME}:arm64 .
echo "✅ ARM64 build completed: ${IMAGE_NAME}:arm64"

echo ""
echo "🎉 All builds completed successfully!"
echo "Available images:"
docker images | grep ${IMAGE_NAME}

# docker build -t py312-arm --build-arg TARGET_ARCH=arm64 .
