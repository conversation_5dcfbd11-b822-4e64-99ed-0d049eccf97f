# 多阶段构建 - 构建阶段
FROM ubuntu:24.04 AS builder

# 设置环境变量
ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive \
    PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple \
    PIP_EXTRA_INDEX_URL=https://pypi.org/simple

# 检测架构并设置相应的变量
ARG TARGETARCH
ARG BUILDPLATFORM
RUN echo "Building for $TARGETARCH on $BUILDPLATFORM"

# 配置ARM64源 - 使用官方源避免镜像问题
RUN if [ "$TARGETARCH" = "arm64" ]; then \
    sed -i 's|http://archive.ubuntu.com/ubuntu|http://ports.ubuntu.com/ubuntu-ports|g' /etc/apt/sources.list.d/ubuntu.sources && \
    sed -i 's|mirrors.aliyun.com/ubuntu|ports.ubuntu.com/ubuntu-ports|g' /etc/apt/sources.list.d/ubuntu.sources; \
    fi

# 安装构建依赖和Python
RUN apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
    python3.12 \
    python3.12-venv \
    python3-pip \
    build-essential \
    ca-certificates \
    && \
    update-ca-certificates && \
    # 创建虚拟环境
    python3 -m venv /opt/venv && \
    # 安装Python包
    /opt/venv/bin/pip install --no-cache-dir \
    jupyterlab \
    jupyterlab-myst \
    typing-extensions \
    matplotlib \
    numpy \
    pandas && \
    # 清理构建缓存
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    # 清理pip缓存
    /opt/venv/bin/pip cache purge

# 运行时阶段
FROM ubuntu:24.04

# 设置环境变量
ENV TZ=Asia/Shanghai \
    DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    MATPLOTLIBRC=/etc/matplotlibrc \
    PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple \
    PIP_EXTRA_INDEX_URL=https://pypi.org/simple \
    FONT_PATH=/usr/share/fonts/truetype/wqy

# 设置工作目录
WORKDIR /

# 复制rootfs文件
COPY rootfs ./

# 检测架构并设置相应的变量
ARG TARGETARCH
RUN if [ "$TARGETARCH" = "arm64" ]; then \
    sed -i 's|http://archive.ubuntu.com/ubuntu|http://ports.ubuntu.com/ubuntu-ports|g' /etc/apt/sources.list.d/ubuntu.sources && \
    sed -i 's|mirrors.aliyun.com/ubuntu|ports.ubuntu.com/ubuntu-ports|g' /etc/apt/sources.list.d/ubuntu.sources; \
    fi

# 安装运行时依赖和调试工具
RUN apt-get update --allow-insecure-repositories && \
    apt-get install -y --no-install-recommends --allow-unauthenticated \
    python3.12 \
    python3-pip \
    fonts-wqy-zenhei \
    libgl1 \
    libsm6 \
    ca-certificates \
    # 调试和管理工具
    vim \
    wget \
    curl \
    iputils-ping \
    net-tools \
    procps \
    htop \
    less \
    && \
    update-ca-certificates && \
    # 配置中文字体
    cp /usr/share/fonts/truetype/wqy/wqy-zenhei.ttc /usr/local/share/fonts/ && \
    echo "axes.unicode_minus: False\nfont.family: WenQuanYi Zen Hei, DejaVu Sans, Bitstream Vera Sans\nfont.size: 12" > /etc/matplotlibrc && \
    # 设置root密码
    echo 'root:root' | chpasswd && \
    # 清理APT缓存
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 设置虚拟环境路径
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/opt/venv/lib/python3.12/site-packages"

# 暴露端口
EXPOSE 8888

# 设置入口点 - 严格保持不变
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
