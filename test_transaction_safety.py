#!/usr/bin/env python3
"""
测试事务安全性的脚本

这个脚本验证在并发环境下，seq 字段生成的事务安全性。
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from datetime import datetime

# 配置
API_BASE_URL = "http://localhost:403"
API_TOKEN = "a3c18565d92aa5eb1fb615d9a4fcb0520"

def create_test_resource(thread_id, iteration):
    """创建测试资源的函数"""
    
    # 准备测试数据
    meta = {
        "course": "blog",
        "resource": "articles",
        "title": f"并发测试文章 {thread_id}-{iteration}",
        "description": f"这是线程 {thread_id} 第 {iteration} 次创建的测试文章",
        "rel_path": f"concurrent_test_{thread_id}_{iteration}.ipynb",
        "division": "blog",
        "publish_date": datetime.now().isoformat(),
        "price": 0.0
    }
    
    data = {
        "meta": meta,
        "allow_all": False
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/admin/resources/publish",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {API_TOKEN}"
            },
            json=data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return {
                "success": True,
                "thread_id": thread_id,
                "iteration": iteration,
                "resource_id": result["id"],
                "seq": None,  # 我们无法直接获取 seq，但可以通过其他方式验证
                "message": result["message"]
            }
        else:
            return {
                "success": False,
                "thread_id": thread_id,
                "iteration": iteration,
                "error": response.text,
                "status_code": response.status_code
            }
            
    except Exception as e:
        return {
            "success": False,
            "thread_id": thread_id,
            "iteration": iteration,
            "error": str(e),
            "exception": True
        }

def test_concurrent_seq_generation():
    """测试并发 seq 生成"""
    print("=== 并发 seq 生成测试 ===")
    print("这个测试模拟多个线程同时创建资源，验证 seq 字段的唯一性")
    
    # 测试参数
    num_threads = 5
    iterations_per_thread = 3
    
    print(f"启动 {num_threads} 个线程，每个线程创建 {iterations_per_thread} 个资源")
    
    results = []
    
    # 使用线程池执行并发请求
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有任务
        futures = []
        for thread_id in range(num_threads):
            for iteration in range(iterations_per_thread):
                future = executor.submit(create_test_resource, thread_id, iteration)
                futures.append(future)
        
        # 收集结果
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            
            if result["success"]:
                print(f"✅ 线程 {result['thread_id']}-{result['iteration']}: 资源 ID {result['resource_id']}")
            else:
                print(f"❌ 线程 {result['thread_id']}-{result['iteration']}: {result.get('error', 'Unknown error')}")
    
    # 分析结果
    successful_results = [r for r in results if r["success"]]
    failed_results = [r for r in results if not r["success"]]
    
    print(f"\n📊 测试结果统计:")
    print(f"总请求数: {len(results)}")
    print(f"成功: {len(successful_results)}")
    print(f"失败: {len(failed_results)}")
    
    if failed_results:
        print(f"\n❌ 失败的请求:")
        for result in failed_results:
            print(f"  线程 {result['thread_id']}-{result['iteration']}: {result.get('error', 'Unknown')}")
    
    # 检查资源 ID 的唯一性
    resource_ids = [r["resource_id"] for r in successful_results]
    unique_ids = set(resource_ids)
    
    if len(resource_ids) == len(unique_ids):
        print(f"✅ 所有资源 ID 都是唯一的")
    else:
        print(f"❌ 发现重复的资源 ID！")
        print(f"  总 ID 数: {len(resource_ids)}")
        print(f"  唯一 ID 数: {len(unique_ids)}")
    
    return len(successful_results) == len(results) and len(resource_ids) == len(unique_ids)

def test_sequential_seq_generation():
    """测试顺序 seq 生成（作为对照组）"""
    print("\n=== 顺序 seq 生成测试 ===")
    print("这个测试顺序创建资源，验证基本功能")
    
    num_resources = 3
    results = []
    
    for i in range(num_resources):
        print(f"创建资源 {i+1}/{num_resources}...")
        result = create_test_resource("sequential", i)
        results.append(result)
        
        if result["success"]:
            print(f"✅ 资源 ID: {result['resource_id']}")
        else:
            print(f"❌ 错误: {result.get('error', 'Unknown')}")
        
        # 稍微延迟，确保时间戳不同
        time.sleep(0.1)
    
    successful_results = [r for r in results if r["success"]]
    print(f"\n顺序测试结果: {len(successful_results)}/{len(results)} 成功")
    
    return len(successful_results) == len(results)

def main():
    """主测试函数"""
    print("开始测试事务安全性...")
    print("注意：这个测试需要服务器运行在 localhost:403")
    
    try:
        # 首先测试服务器连接
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        print(f"✅ 服务器连接正常")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 服务器连接测试失败: {e}")
        return False
    
    # 运行测试
    sequential_success = test_sequential_seq_generation()
    concurrent_success = test_concurrent_seq_generation()
    
    print(f"\n🎯 最终结果:")
    print(f"顺序测试: {'✅ 通过' if sequential_success else '❌ 失败'}")
    print(f"并发测试: {'✅ 通过' if concurrent_success else '❌ 失败'}")
    
    if sequential_success and concurrent_success:
        print(f"\n🎉 所有测试通过！事务安全性验证成功。")
        return True
    else:
        print(f"\n⚠️  部分测试失败，请检查事务实现。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
