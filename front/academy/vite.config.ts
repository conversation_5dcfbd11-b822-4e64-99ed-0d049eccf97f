import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig({
  base: '/academy',
  build: {
    outDir: '../../provision/static/academy',
    assetsDir: 'assets',
    copyPublicDir: false
  },
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 3000,
    // 修复WebSocket连接问题
    fs: {
      // 允许从根目录提供文件
      allow: ['../']
    },
    // 确保WebSocket连接使用正确的路径
    hmr: {
      // 如果使用代理，请指定完整的URL
      host: 'localhost',
      port: 3000
    },
    proxy: {
      // 代理所有 /api 开头的请求到后端服务器
      '/api': {
        target: 'http://localhost:403',
        changeOrigin: true,
        secure: false
      },
      // 代理所有 /public 开头的请求到后端服务器
      '/public': {
        target: 'http://localhost:403',
        changeOrigin: true,
        secure: false
      },
      // 代理所有 /sso 开头的请求到后端服务器
      '/sso': {
        target: 'http://localhost:403',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
