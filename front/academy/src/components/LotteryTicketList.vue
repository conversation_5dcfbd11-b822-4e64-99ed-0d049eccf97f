<template>
    <div class="lottery-ticket-list">
        <el-tabs v-model="activeTab">
            <el-tab-pane label="我的奖券" name="my">
                <div class="filter-bar">
                    <div class="filter-left">
                        <el-radio-group v-model="ticketStatus" @change="filterTickets">
                            <el-radio-button label="all">全部</el-radio-button>
                            <el-radio-button label="unused">未使用</el-radio-button>
                            <el-radio-button label="transferring">转让中</el-radio-button>
                            <el-radio-button label="transferred">已转让</el-radio-button>
                            <el-radio-button label="used">已使用</el-radio-button>
                            <el-radio-button label="expired">已过期</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="filter-right">
                        <el-button type="primary" @click="showClaimDialog">接受转让</el-button>
                    </div>
                </div>

                <div class="ticket-list" v-loading="loading">
                    <div v-if="filteredTickets.length === 0" class="empty-list">
                        <el-empty description="暂无奖券" />
                    </div>

                    <div v-for="ticket in filteredTickets" :key="ticket.id" class="ticket-item">
                        <div class="ticket"
                            :class="{ 'expired': isExpired(ticket), 'used': ticket.status === 'used', 'transferred': ticket.status === 'transferred' }">
                            <div class="ticket-header">
                                <span class="course">{{ ticket.title }}</span>
                                <span class="discount">{{ ticket.discount }}元</span>
                            </div>
                            <div class="ticket-body">
                                <p>本券有最低消费额{{ ticket.base_fare }}元，可叠加，最大叠加金额{{ ticket.max_discount }}元。</p>
                                <p>本券可转让一次，转让后，您将获得价值{{ ticket.discount / 2 }}元的现金返回，对方将可抵扣{{ ticket.discount / 2
                                    }}元课程费用。</p>
                                <p class="expires">有效期至: {{ formatDate(ticket.expires_at) }}</p>

                                <!-- 状态和券码区域 -->
                                <div class="ticket-status-code-row">
                                    <!-- 状态标签 -->
                                    <div class="ticket-status">
                                        <el-tag v-if="ticket.status === 'unused' && !isExpired(ticket)"
                                            type="success">未使用</el-tag>
                                        <el-tag v-else-if="ticket.status === 'used'" type="info">已使用</el-tag>
                                        <el-tag v-else-if="ticket.status === 'transferring'" type="warning">转让中</el-tag>
                                        <el-tag v-else-if="ticket.status === 'transferred'" type="warning">已转让</el-tag>
                                        <el-tag v-else-if="isExpired(ticket)" type="danger">已过期</el-tag>
                                    </div>

                                    <!-- 券码显示区域 -->
                                    <div class="ticket-code-section"
                                        v-if="ticket.status === 'unused' && !isExpired(ticket)">
                                        <div class="ticket-code-display">
                                            <span class="code-label">券码</span>
                                            <div class="code-value" @click="copyTicketCodeDirect(ticket.ticket_code)">
                                                {{ ticket.ticket_code }}
                                                <el-icon class="copy-icon">
                                                    <DocumentCopy />
                                                </el-icon>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ticket-actions" v-if="ticket.status === 'unused' && !isExpired(ticket)">
                                    <el-button type="primary" size="small"
                                        @click="transferTicket(ticket)">转让</el-button>
                                </div>

                                <div class="ticket-actions"
                                    v-if="ticket.status === 'transferring' && !isExpired(ticket)">
                                    <el-button type="primary" size="small"
                                        @click="showTransferCode(ticket)">查看券码</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>

            <el-tab-pane label="转让给我的" name="transferred">
                <div class="ticket-list" v-loading="loading">
                    <div v-if="transferredTickets.length === 0" class="empty-list">
                        <el-empty description="暂无转让给您的奖券" />
                    </div>

                    <div v-for="ticket in transferredTickets" :key="ticket.id" class="ticket-item">
                        <div class="ticket transferred-to-me">
                            <div class="ticket-header">
                                <span class="course">{{ ticket.title }}</span>
                                <span class="discount">{{ ticket.discount / 2 }}元</span>
                            </div>
                            <div class="ticket-body">
                                <p>此券由其它用户转让，适用于<strong>{{ ticket.title || '未知课程' }}</strong>，您在购买时可以获得{{
            ticket.discount / 2 }}元的折扣。
                                </p>
                                <p>本券有最低消费额{{ ticket.base_fare }}元，可叠加，最大叠加金额{{ ticket.max_discount / 2 }}元。</p>
                                <p class="expires">有效期至: {{ formatDate(ticket.expires_at) }}</p>

                                <!-- 状态和券码区域 -->
                                <div class="ticket-status-code-row">
                                    <!-- 状态标签 -->
                                    <div class="ticket-status">
                                        <el-tag v-if="!isExpired(ticket)" type="success">可使用</el-tag>
                                        <el-tag v-else type="danger">已过期</el-tag>
                                    </div>

                                    <!-- 券码显示区域 -->
                                    <div class="ticket-code-section" v-if="!isExpired(ticket)">
                                        <div class="ticket-code-display">
                                            <span class="code-label">券码</span>
                                            <div class="code-value" @click="copyTicketCodeDirect(ticket.ticket_code)">
                                                {{ ticket.ticket_code }}
                                                <el-icon class="copy-icon">
                                                    <DocumentCopy />
                                                </el-icon>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 转让对话框 -->
        <el-dialog v-model="transferDialogVisible" :title="currentTicket?.status === 'transferring' ? '查看券码' : '转让奖券'"
            width="400px">
            <div class="transfer-dialog">
                <div class="transfer-code-container">
                    <p v-if="currentTicket">本券面值为{{ currentTicket.discount }}元。对方接受并成功兑现后，您将获得价值{{
            currentTicket.discount / 2
        }}元的现金返回，对方将可抵扣{{ currentTicket.discount / 2 }}元课程费用。</p>
                    <p>请将以下券码发送给接收者：</p>
                    <el-input ref="ticketCodeInput" v-model="ticketCodeForCopy" readonly class="ticket-code-input"
                        @focus="$event.target.select()"></el-input>
                    <p class="transfer-tip">接收者可以在"接受转让"中输入此券码来接收奖券</p>
                    <p class="transfer-tip">如需手动复制，请点击上方文本框</p>
                    <div class="dialog-footer">
                        <el-button @click="transferDialogVisible = false">关闭</el-button>
                        <el-button v-if="currentTicket?.status === 'unused'" @click="copyAndConfirmTransfer"
                            type="primary" :loading="transferLoading">复制并确认转让</el-button>
                        <el-button v-else @click="copyTicketCode" type="primary">复制券码</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>



        <!-- 接受转让对话框 -->
        <el-dialog v-model="claimDialogVisible" title="接受转让" width="400px">
            <div class="claim-dialog">
                <p>请输入券码：</p>
                <el-input v-model="transferClaimCode" placeholder="请输入5位券码"></el-input>
                <div class="dialog-footer">
                    <el-button @click="claimDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmClaim" :loading="claimLoading">确认接收</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    ref,
    computed,
    onMounted,
    defineExpose
} from 'vue'
import {
    ElMessage
} from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
    name: 'LotteryTicketList',
    setup() {
        const activeTab = ref('my')
        const ticketStatus = ref('all')
        const tickets = ref([])
        const transferredTickets = ref([])
        const loading = ref(false)

        // 转让对话框
        const transferDialogVisible = ref(false)
        const currentTicket = ref(null)
        const transferLoading = ref(false)
        const ticketCodeForCopy = ref('')



        // 接受转让对话框
        const claimDialogVisible = ref(false)
        const transferClaimCode = ref('')
        const claimLoading = ref(false)

        // 计算属性
        const filteredTickets = computed(() => {
            if (ticketStatus.value === 'all') {
                return tickets.value
            } else if (ticketStatus.value === 'expired') {
                return tickets.value.filter(ticket => isExpired(ticket))
            } else {
                return tickets.value.filter(ticket => ticket.status === ticketStatus.value)
            }
        })

        // 方法
        const loadTickets = async () => {
            try {
                loading.value = true
                const response = await axios.get('/api/academy/lottery/tickets')
                tickets.value = response.data.tickets || []
                transferredTickets.value = response.data.transferred_tickets || []
            } catch (error) {
                console.error('Failed to load tickets:', error)
                ElMessage.error('加载奖券失败')
            } finally {
                loading.value = false
            }
        }

        const filterTickets = () => {
            // 已经通过计算属性实现
        }

        const isExpired = (ticket) => {
            return new Date(ticket.expires_at) < new Date()
        }

        const transferTicket = (ticket) => {
            currentTicket.value = ticket
            ticketCodeForCopy.value = ticket.ticket_code || ''
            transferDialogVisible.value = true
        }

        const showTransferCode = (ticket) => {
            currentTicket.value = ticket
            ticketCodeForCopy.value = ticket.ticket_code || ''
            transferDialogVisible.value = true
        }



        const copyTicketCode = async () => {
            if (!currentTicket.value || !currentTicket.value.ticket_code) {
                ElMessage.warning('无法获取券码')
                return
            }

            const ticketCode = currentTicket.value.ticket_code
            let copySuccess = false

            // 尝试使用现代 Clipboard API
            try {
                await navigator.clipboard.writeText(ticketCode)
                copySuccess = true
                ElMessage.success('券码已复制到剪贴板')
            } catch (e) {
                console.error('Clipboard API 复制失败:', e)
                // 如果现代 API 失败，尝试使用传统方法
                try {
                    const textArea = document.createElement('textarea')
                    textArea.value = ticketCode
                    textArea.style.position = 'fixed' // 避免滚动到底部
                    textArea.style.left = '-999999px'
                    textArea.style.top = '-999999px'
                    document.body.appendChild(textArea)
                    textArea.focus()
                    textArea.select()

                    const successful = document.execCommand('copy')
                    document.body.removeChild(textArea)

                    if (successful) {
                        copySuccess = true
                        ElMessage.success('券码已复制到剪贴板')
                    }
                } catch (e2) {
                    console.error('传统方法复制失败:', e2)
                }
            }

            // 如果两种方法都失败，显示警告
            if (!copySuccess) {
                ElMessage({
                    message: `请手动复制券码: ${ticketCode}`,
                    type: 'warning',
                    duration: 5000
                })
            }
        }

        const copyTicketCodeDirect = async (ticketCode) => {
            if (!ticketCode) {
                ElMessage.warning('无法获取券码')
                return
            }

            let copySuccess = false

            // 尝试使用现代 Clipboard API
            try {
                await navigator.clipboard.writeText(ticketCode)
                copySuccess = true
                ElMessage.success('券码已复制到剪贴板')
            } catch (e) {
                console.error('Clipboard API 复制失败:', e)
                // 如果现代 API 失败，尝试使用传统方法
                try {
                    const textArea = document.createElement('textarea')
                    textArea.value = ticketCode
                    textArea.style.position = 'fixed' // 避免滚动到底部
                    textArea.style.left = '-999999px'
                    textArea.style.top = '-999999px'
                    document.body.appendChild(textArea)
                    textArea.focus()
                    textArea.select()

                    const successful = document.execCommand('copy')
                    document.body.removeChild(textArea)

                    if (successful) {
                        copySuccess = true
                        ElMessage.success('券码已复制到剪贴板')
                    }
                } catch (e2) {
                    console.error('传统方法复制失败:', e2)
                }
            }

            // 如果两种方法都失败，显示警告
            if (!copySuccess) {
                ElMessage({
                    message: `请手动复制券码: ${ticketCode}`,
                    type: 'warning',
                    duration: 5000
                })
            }
        }

        const copyAndConfirmTransfer = async () => {
            if (!currentTicket.value) {
                ElMessage.warning('未选择奖券')
                return
            }

            try {
                transferLoading.value = true

                // 先复制券码
                if (currentTicket.value.ticket_code) {
                    await copyTicketCode()
                }

                // 然后创建转让记录
                const response = await axios.post(`/api/academy/lottery/tickets/${currentTicket.value.id}/create-transfer`)

                if (response.data.success) {
                    // 检查是否是已存在的转让记录
                    if (response.data.message === "Transfer already exists") {
                        ElMessage.info('该奖券已创建转让记录，可以继续使用当前券码')
                    } else {
                        ElMessage.success('转让成功')
                    }
                    transferDialogVisible.value = false
                    loadTickets() // 重新加载奖券列表
                } else {
                    ElMessage.error(response.data.error || '转让失败')
                }
            } catch (error) {
                console.error('Failed to transfer ticket:', error)
                ElMessage.error(error.response?.data?.error || '转让失败')
            } finally {
                transferLoading.value = false
            }
        }

        const showClaimDialog = () => {
            transferClaimCode.value = ''
            claimDialogVisible.value = true
        }

        const confirmClaim = async () => {
            if (!transferClaimCode.value) {
                ElMessage.warning('请输入券码')
                return
            }

            try {
                claimLoading.value = true
                const response = await axios.post('/api/academy/lottery/claim', {
                    ticket_code: transferClaimCode.value
                })

                if (response.data.success) {
                    ElMessage.success('奖券接收成功')
                    claimDialogVisible.value = false
                    loadTickets() // 重新加载奖券列表
                    // 切换到"转让给我的"标签页
                    activeTab.value = 'transferred'
                } else {
                    ElMessage.error(response.data.error || '接收奖券失败')
                }
            } catch (error) {
                console.error('Failed to claim ticket:', error)
                ElMessage.error(error.response?.data?.error || '接收奖券失败')
            } finally {
                claimLoading.value = false
            }
        }



        const formatDate = (dateString) => {
            if (!dateString) return ''
            const date = new Date(dateString)
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
        }

        // 生命周期钩子
        onMounted(() => {
            loadTickets()
        })

        // 暴露方法给父组件
        defineExpose({
            loadTickets
        });

        return {
            activeTab,
            ticketStatus,
            tickets,
            transferredTickets,
            loading,
            filteredTickets,
            transferDialogVisible,
            currentTicket,
            transferLoading,
            ticketCodeForCopy,
            claimDialogVisible,
            transferClaimCode,
            claimLoading,
            loadTickets,
            filterTickets,
            isExpired,
            transferTicket,
            showTransferCode,
            copyTicketCode,
            copyTicketCodeDirect,
            copyAndConfirmTransfer,
            showClaimDialog,
            confirmClaim,
            formatDate
        }
    },
    components: {
        DocumentCopy
    }
}
</script>

<style scoped>
.lottery-ticket-list {
    padding: 20px;
}

.filter-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-left {
    flex: 1;
}

.filter-right {
    margin-left: 20px;
}

.ticket-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.ticket-item {
    width: 100%;
    max-width: 400px;
}

.ticket {
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f8f8;
    transition: all 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.ticket:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
}

.ticket-header {
    background: #409EFF;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ticket.expired .ticket-header,
.ticket.used .ticket-header {
    background: #909399;
}

.ticket.transferred .ticket-header {
    background: #E6A23C;
}

.ticket.transferred-to-me .ticket-header {
    background: #67C23A;
}

.ticket-header .course {
    font-weight: bold;
}

.ticket-header .discount {
    font-size: 20px;
    font-weight: bold;
}

.ticket-body {
    padding: 15px;
}

.ticket-body p {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
}

.expires {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 10px;
}

.ticket-status {
    margin-top: 15px;
}

.ticket-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.empty-list {
    width: 100%;
    padding: 30px 0;
}

.transfer-dialog,
.use-dialog,
.claim-dialog {
    padding: 10px;
}

.dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.transfer-code-container {
    text-align: center;
}

/* 移除了重复的 .transfer-code 样式 */

.ticket-code-input {
    margin: 15px 0 10px 0;
}

.ticket-code-input :deep(.el-input__inner) {
    text-align: center;
    font-family: monospace;
    font-size: 24px;
    color: #409EFF;
    font-weight: bold;
    letter-spacing: 2px;
    background-color: #f0f9ff;
    padding: 15px 10px;
    height: auto;
    border: 2px solid #409EFF;
}

.transfer-tip {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
}

/* 状态和券码行布局 */
.ticket-status-code-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    margin-top: 15px;
}

.ticket-status {
    flex-shrink: 0;
}

/* 券码显示区域样式 */
.ticket-code-section {
    flex: 1;
    min-width: 0;
    /* 允许收缩 */
}

.ticket-code-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    border: 1px solid #e1e8ed;
}

.code-label {
    font-size: 11px;
    color: #ffffff;
    font-weight: 500;
    opacity: 0.9;
    margin-right: 8px;
    flex-shrink: 0;
}

.code-value {
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.code-value:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.copy-icon {
    font-size: 12px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    flex-shrink: 0;
}

.code-value:hover .copy-icon {
    opacity: 1;
}
</style>
