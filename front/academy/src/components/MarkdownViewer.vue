<template>
  <div class="markdown-viewer">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    <div v-else-if="error" class="error-container">
      <el-alert :title="error" type="error" :closable="false" show-icon />
    </div>
    <div v-else class="markdown-content" v-html="renderedContent"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';
import MarkdownIt from 'markdown-it';
import container from 'markdown-it-container';

// 创建markdown-it实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
});

// 添加提示框插件
const renderContainer = (type) => {
  return {
    validate: function (params) {
      // 更灵活的正则表达式，允许有或没有标题
      return params.trim().match(new RegExp(`^${type}(?:\\s+(.*))?$`));
    },
    render: function (tokens, idx) {
      const m = tokens[idx].info.trim().match(new RegExp(`^${type}(?:\\s+(.*))?$`));

      if (tokens[idx].nesting === 1) {
        // 开始标签
        const title = m[1] ? m[1] : type.charAt(0).toUpperCase() + type.slice(1);
        return `<div class="admonition ${type}"><p class="admonition-title">${title}</p>\n`;
      } else {
        // 结束标签
        return '</div>\n';
      }
    }
  };
};

// 注册各种类型的提示框
md.use(container, 'tip', renderContainer('tip'));
md.use(container, 'note', renderContainer('note'));
md.use(container, 'warning', renderContainer('warning'));
md.use(container, 'danger', renderContainer('danger'));
md.use(container, 'info', renderContainer('info'));
md.use(container, 'question', renderContainer('question'));

// 定义props
const props = defineProps({
  // markdown文件路径
  path: {
    type: String,
    required: true
  },
  // 是否自动加载内容
  autoLoad: {
    type: Boolean,
    default: true
  }
});

// 状态变量
const loading = ref(false);
const error = ref(null);
const renderedContent = ref('');

// 加载markdown内容
const loadMarkdownContent = async () => {
  if (!props.path) {
    error.value = '未指定Markdown文件路径';
    return;
  }

  try {
    // 设置加载状态
    loading.value = true;
    error.value = null;

    // 从URL获取markdown内容
    const response = await axios.get(props.path);
    const content = response.data;

    // 渲染markdown内容
    renderedContent.value = md.render(content);
  } catch (err) {
    console.error(`加载markdown内容失败: ${props.path}`, err);
    error.value = '加载内容失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 监听路径变化
watch(() => props.path, () => {
  if (props.path) {
    loadMarkdownContent();
  }
});

// 组件挂载时加载内容
onMounted(() => {
  if (props.autoLoad && props.path) {
    loadMarkdownContent();
  }
});

// 暴露方法给父组件
defineExpose({
  loadMarkdownContent
});
</script>

<style scoped>
.markdown-viewer {
  width: 100%;
  margin-bottom: 20px;
}

.loading-container,
.error-container {
  padding: 16px;
}

.markdown-content {
  padding: 16px;
  line-height: 1.6;
}

.markdown-content :deep(h1) {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #eaecef;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  padding-left: 2rem;
  margin-bottom: 1rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.5rem;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 1rem;
  color: #6a737d;
  margin-bottom: 1rem;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
}

.markdown-content :deep(table th),
.markdown-content :deep(table td) {
  border: 1px solid #dfe2e5;
  padding: 0.6rem 1rem;
}

.markdown-content :deep(table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 1rem;
  overflow: auto;
  margin-bottom: 1rem;
}

.markdown-content :deep(code) {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}

/* Admonition 样式 */
.markdown-content :deep(.admonition) {
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .14), 0 1px 5px 0 rgba(0, 0, 0, .12), 0 3px 1px -2px rgba(0, 0, 0, .2);
  position: relative;
  margin: 1.5625em 0;
  padding: 0 1.2rem;
  border-left: .4rem solid rgba(68, 138, 255, .8);
  border-radius: .2rem;
  background-color: rgba(255, 255, 255, 0.05);
  overflow: auto;
}

.markdown-content :deep(.admonition > p) {
  margin-top: .8rem;
}

.markdown-content :deep(.admonition > .admonition-title) {
  margin: 0 -1.2rem;
  padding: .8rem 1.0rem .8rem 1.0rem;
  border-bottom: 1px solid rgba(68, 138, 255, .2);
  background-color: rgba(68, 138, 255, .1);
  font-weight: 700;
}

/* 警告样式 */
.markdown-content :deep(.admonition.warning) {
  border-left-color: rgba(255, 145, 0, .8);
}

.markdown-content :deep(.admonition.warning > .admonition-title) {
  background-color: rgba(255, 145, 0, .1);
  border-bottom-color: rgba(255, 145, 0, .2);
  padding-left: 1.0rem;
}

/* 提示样式 */
.markdown-content :deep(.admonition.tip) {
  border-left-color: rgba(0, 191, 165, .8);
}

.markdown-content :deep(.admonition.tip > .admonition-title) {
  background-color: rgba(0, 191, 165, .1);
  border-bottom-color: rgba(0, 191, 165, .2);
  padding-left: 1.0rem;
}

/* 注意样式 */
.markdown-content :deep(.admonition.note) {
  border-left-color: rgba(68, 138, 255, .8);
}

.markdown-content :deep(.admonition.note > .admonition-title) {
  background-color: rgba(68, 138, 255, .1);
  border-bottom-color: rgba(68, 138, 255, .2);
  padding-left: 1.0rem;
}

/* 危险样式 */
.markdown-content :deep(.admonition.danger) {
  border-left-color: rgba(255, 23, 68, .8);
}

.markdown-content :deep(.admonition.danger > .admonition-title) {
  background-color: rgba(255, 23, 68, .1);
  border-bottom-color: rgba(255, 23, 68, .2);
  padding-left: 1.0rem;
}

/* 信息样式 */
.markdown-content :deep(.admonition.info) {
  border-left-color: rgba(0, 184, 212, .8);
}

.markdown-content :deep(.admonition.info > .admonition-title) {
  background-color: rgba(0, 184, 212, .1);
  border-bottom-color: rgba(0, 184, 212, .2);
  padding-left: 1.0rem;
}
</style>
