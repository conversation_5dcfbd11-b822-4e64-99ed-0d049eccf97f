<!-- src/components/RecommendedCourseCard.vue -->
<template>
    <div class="recommended-course-card">
        <!-- 课程级别标签 -->
        <div class="level-badge" :class="levelClass">
            {{ level }}
        </div>

        <div class="left-col">
            <div class="course-image">
                <img :src="courseImage" :alt="name" class="desktop-image" />
                <img :src="courseLargeImage" :alt="name" class="mobile-image" />
            </div>
            <div class="course-stats">
                <div class="stat-item">
                    <el-icon>
                        <VideoPlay />
                    </el-icon>
                    <span>{{ duration || '约20小时' }}</span>
                </div>
                <div class="stat-item">
                    <el-icon>
                        <Document />
                    </el-icon>
                    <span>{{ chapters }}章</span>
                </div>
            </div>
        </div>
        <div class="right-col">
            <div class="course-info">
                <h3 class="course-title">{{ name }}</h3>
                <div class="course-meta">
                    <span class="price-tag">{{ price }}</span>
                </div>
                <p class="course-desc">{{ desc }}</p>
            </div>
            <div class="action-buttons">
                <el-button type="primary" size="small" @click="enrollCourse">立即报名</el-button>
                <el-button type="info" size="small" plain @click="viewDetails">查看详情</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import l24Image from '@/assets/24lectures_300.png';
import faImage from '@/assets/fa_300.png';
import pandasImage from '@/assets/pandas_300.png';
// 导入大尺寸图片
import l24ImageLarge from '@/assets/24lectures_1000.png';
import faImageLarge from '@/assets/fa_1000.png';
import pandasImageLarge from '@/assets/pandas_1000.png';
import { Document, VideoPlay } from '@element-plus/icons-vue';
import { computed, defineProps } from 'vue';

const props = defineProps({
    id: Number,
    name: String,
    price: String,
    image: String,
    level: String,
    desc: String,
    courseId: String,
    chapters: Number,
    duration: String,
    buyLink: String,
    detail: String
})

// 计算属性：根据courseId选择正确的图片
const courseImage = computed(() => {
    if (props.courseId === 'fa') return faImage
    if (props.courseId === 'pandas') return pandasImage
    if (props.courseId === 'l24') return l24Image

    // 如果有图片路径且以http开头，直接使用
    if (props.image && (props.image.startsWith('http') || props.image.startsWith('/'))) {
        return props.image
    }

    // 默认返回fa图片
    return faImage
})

// 计算属性：根据courseId选择大尺寸图片（用于响应式）
const courseLargeImage = computed(() => {
    if (props.courseId === 'fa') return faImageLarge
    if (props.courseId === 'pandas') return pandasImageLarge
    if (props.courseId === 'l24') return l24ImageLarge

    // 如果有图片路径且以http开头，直接使用
    if (props.image && (props.image.startsWith('http') || props.image.startsWith('/'))) {
        return props.image
    }

    // 默认返回fa图片
    return faImageLarge
})

// 计算属性：根据level返回对应的CSS类
const levelClass = computed(() => {
    const lowerLevel = props.level.toLowerCase()

    if (lowerLevel.includes('高级') || lowerLevel === 'advanced') {
        return 'advanced'
    } else if (lowerLevel.includes('中级') || lowerLevel === 'intermediate') {
        return 'intermediate'
    } else {
        return 'beginner'
    }
})

// 报名课程
const enrollCourse = () => {
    console.log(`报名课程: ${props.name} (${props.courseId})`)
    // 如果有购买链接，则跳转到该链接
    if (props.buyLink) {
        window.open(props.buyLink, '_blank')
    } else {
        // 如果没有购买链接，可以跳转到默认的购买页面
        window.open(`https://ke.quantide.cn/course/${props.courseId}`, '_blank')
    }
}

// 查看详情
const viewDetails = () => {
    console.log(`查看课程详情: ${props.name} (${props.courseId})`)
    if (props.detail) {
        // 跳转到CourseDetail视图，而不是直接访问markdown文件
        window.location.href = `/academy/course-detail/${props.detail}`
    } else {
        console.warn('该课程暂无详情页面')
    }
}
</script>

<style scoped>
.recommended-course-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    margin-bottom: clamp(15px, 3vw, 20px);
    display: flex;
    flex-direction: row;
    gap: clamp(10px, 2vw, 20px);
    align-items: center;
    position: relative;
    padding: clamp(15px, 3vw, 20px);
    max-width: 100%;
    box-sizing: border-box;
}

.recommended-course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    border-color: #FFCC70;
}

.left-col {
    width: clamp(150px, 25vw, 200px);
    flex-shrink: 0;
}

.course-image {
    width: 100%;
    height: clamp(120px, 20vw, 150px);
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 10px;
}

.course-image .desktop-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    display: block;
}

.course-image .mobile-image {
    display: none;
}

.recommended-course-card:hover .course-image .desktop-image {
    transform: scale(1.05);
}

.course-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    color: #666;
}

.stat-item .el-icon {
    margin-right: 5px;
    font-size: 14px;
    color: #C850C0;
}

.right-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.course-info {
    margin-bottom: 15px;
}

.course-title {
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: #333;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.price-tag {
    background-color: #FF6B6B;
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 16px;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

.course-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

/* 课程级别标签 */
.level-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.level-badge.advanced {
    background-color: #FF4B91;
}

.level-badge.intermediate {
    background-color: #FF9843;
}

.level-badge.beginner {
    background-color: #4BC0C0;
}

/* 基础响应式样式 - 使用视口单位 */
.stat-item {
    font-size: clamp(11px, 1.5vw, 14px);
    padding: clamp(4px, 1vw, 8px) clamp(8px, 1.5vw, 10px);
}

.course-title {
    font-size: clamp(16px, 2vw, 18px);
}

.course-desc {
    font-size: clamp(13px, 1.5vw, 14px);
    -webkit-line-clamp: 3;
}

.action-buttons {
    gap: clamp(5px, 1vw, 10px);
}

/* 在小屏幕上应用特定样式 */
@media (max-width: 768px) {
    .recommended-course-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .left-col {
        width: 100%;
        margin-bottom: 15px;
    }

    .course-image {
        height: clamp(150px, 30vw, 200px);
    }

    .course-image .desktop-image {
        display: none;
    }

    .course-image .mobile-image {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
    }

    .course-stats {
        flex-wrap: wrap;
    }

    .course-desc {
        -webkit-line-clamp: 2;
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}

/* 设备特定样式已移至 autoScale.ts 中通过 JavaScript 动态处理
   可以使用 .device-xs, .device-sm, .device-md, .device-lg 类来定位不同设备 */
.device-xs .recommended-course-card,
.device-md .recommended-course-card {
    padding: 15px;
}

.device-xs .stat-item,
.device-md .stat-item {
    font-size: 11px;
    padding: 4px 6px;
}

.device-xs .action-buttons .el-button,
.device-md .action-buttons .el-button {
    padding: 8px 12px;
    font-size: 12px;
}
</style>
