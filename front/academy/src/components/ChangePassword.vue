<template>
  <el-dialog v-model="dialogVisible" title="修改密码" width="400px" :close-on-click-modal="false"
    :close-on-press-escape="true" @closed="resetForm">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" status-icon>
      <el-form-item label="当前密码" prop="currentPassword">
        <el-input v-model="form.currentPassword" type="password" placeholder="请输入当前密码" show-password />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" type="password" placeholder="请输入新密码" show-password />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input v-model="form.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
      </el-form-item>
    </el-form>

    <div class="password-tips">
      <p>密码要求：</p>
      <ul>
        <li>至少8个字符</li>
        <li>至少大写、小写、数字和特殊字符各一</li>
      </ul>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确认修改
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

// 对话框可见性
const dialogVisible = ref(false);

// 加载状态
const loading = ref(false);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 表单验证规则
const validatePass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'));
  } else if (value.length < 8) {
    callback(new Error('密码长度至少为8个字符'));
  } else if (!/(?=.*[A-Z])/.test(value)) {
    callback(new Error('密码必须包含至少一个大写字母'));
  } else if (!/(?=.*[a-z])/.test(value)) {
    callback(new Error('密码必须包含至少一个小写字母'));
  } else if (!/(?=.*\d)/.test(value)) {
    callback(new Error('密码必须包含至少一个数字'));
  } else if (!/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(value)) {
    callback(new Error('密码必须包含至少一个特殊字符'));
  } else {
    callback();
  }
};

const validateConfirmPass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== form.newPassword) {
    callback(new Error('两次输入密码不一致'));
  } else {
    callback();
  }
};

const rules = reactive<FormRules>({
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, trigger: 'blur', validator: validatePass }
  ],
  confirmPassword: [
    { required: true, trigger: 'blur', validator: validateConfirmPass }
  ]
});

// 获取用户ID
const customerId = computed(() => localStorage.getItem('customer'));

// 打开对话框
const open = () => {
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.currentPassword = '';
  form.newPassword = '';
  form.confirmPassword = '';
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      try {
        const response = await fetch('/api/academy/customer/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            customer_id: customerId.value,
            current_password: form.currentPassword,
            new_password: form.newPassword
          })
        });

        const data = await response.json();

        if (response.ok) {
          ElMessage.success('密码修改成功');
          dialogVisible.value = false;
        } else {
          ElMessage.error(data.error || '密码修改失败，请重试');
        }
      } catch (error) {
        console.error('修改密码时出错:', error);
        ElMessage.error('网络错误，请稍后重试');
      } finally {
        loading.value = false;
      }
    } else {
      console.log('表单验证失败', fields);
    }
  });
};

// 暴露方法和属性
defineExpose({
  open
});
</script>

<style scoped>
.password-tips {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #606266;
}

.password-tips p {
  margin: 0 0 5px 0;
  font-weight: bold;
}

.password-tips ul {
  margin: 0;
  padding-left: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
