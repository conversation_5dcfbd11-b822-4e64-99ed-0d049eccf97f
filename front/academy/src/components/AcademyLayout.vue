<template>
  <div class="academy-container">
    <!-- 修改密码对话框 -->
    <ChangePassword v-if="isLoggedIn" ref="changePasswordRef" />

    <!-- 抽奖对话框 -->
    <LotteryDialog v-if="isLoggedIn" v-model:visible="lotteryDialogVisible" @close="handleLotteryClose" />

    <!-- 左侧导航栏 - 在大屏幕上显示 -->
    <div class="sidebar">
      <div class="logo">
        <h2>Quant Academy</h2>
      </div>
      <nav class="nav-menu">
        <template v-for="item in navItems" :key="item.path">
          <!-- 普通导航项 -->
          <div v-if="item.path !== '/academy/bulletin'" :class="[
      'nav-item',
      { active: activeNavItem === item.path, 'message-nav-item': item.hasNotification },
    ]" @click="navigateTo(item.path)">
            <el-icon>
              <component :is="getIconComponent(item.icon)" />
            </el-icon>
            <span>{{ item.name }}</span>
            <!-- 在PC端导航条中显示未读消息数量 -->
            <MessageNotification v-if="item.hasNotification" mode="nav" class="nav-notification" />
          </div>

          <!-- 重要公告accordion -->
          <div v-else-if="item.path === '/academy/bulletin'" class="bulletin-accordion">
            <el-collapse v-model="activeBulletinSections" @change="handleBulletinChange">
              <el-collapse-item name="bulletin" class="bulletin-collapse-item">
                <template #title>
                  <div class="nav-item bulletin-nav-item">
                    <el-icon>
                      <component :is="getIconComponent(item.icon)" />
                    </el-icon>
                    <span>{{ item.name }}</span>
                  </div>
                </template>

                <!-- 动态加载的子菜单 -->
                <div v-if="bulletinMenuData" class="bulletin-submenu">
                  <template v-for="(items, category) in bulletinMenuData" :key="category">
                    <div class="bulletin-category">
                      <div class="category-title">{{ category }}</div>
                      <div v-for="menuItem in items" :key="menuItem.title" class="bulletin-menu-item"
                        @click="handleBulletinItemClick(menuItem)">
                        <el-icon>
                          <Document />
                        </el-icon>
                        <span class="menu-item-title">{{ menuItem.title }}</span>
                      </div>
                    </div>
                  </template>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 分隔线 -->
          <div v-if="item.divider" class="nav-divider"></div>
        </template>
      </nav>
    </div>

    <!-- 移动端抽屉菜单 -->
    <el-drawer v-model="drawerVisible" title="Quant Academy" direction="rtl" size="80%">
      <nav class="drawer-nav-menu">
        <template v-for="item in navItems" :key="item.path">
          <div :class="[
      'nav-item',
      { active: activeNavItem === item.path, 'message-nav-item': item.hasNotification },
    ]" @click="navigateTo(item.path, true)">
            <el-icon>
              <component :is="getIconComponent(item.icon)" />
            </el-icon>
            <span>{{ item.name }}</span>
            <!-- 在移动端导航菜单中显示未读消息数量 -->
            <MessageNotification v-if="item.hasNotification" mode="drawer" class="drawer-notification" />
          </div>

          <!-- 分隔线 -->
          <div v-if="item.divider" class="drawer-divider"></div>
        </template>

        <!-- 修改密码选项 -->
        <div class="nav-item" @click="openChangePassword" :class="{ disabled: !isLoggedIn }">
          <el-icon>
            <Lock />
          </el-icon>
          <span>修改密码</span>
        </div>

        <!-- 分割线 -->
        <div class="drawer-divider"></div>

        <!-- 登出按钮 -->
        <div class="nav-item" @click="handleLogout" :class="{ disabled: !isLoggedIn }">
          <el-icon>
            <SwitchButton />
          </el-icon>
          <span>退出登录</span>
        </div>
      </nav>
    </el-drawer>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 移动端顶部栏 -->
      <div class="top-bar">
        <!-- 左侧Logo -->
        <div class="mobile-logo">
          <img alt="QuantIDE logo" class="logo-img" src="@/assets/logo.png" />
        </div>

        <div class="mobile-controls">
          <!-- 抽屉菜单按钮 -->
          <div class="mobile-menu-button" @click="drawerVisible = true">
            <el-icon>
              <Menu />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 页面内容 -->
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ChatDotRound,
  Discount,
  Document,
  InfoFilled,
  Lock,
  Menu,
  Monitor,
  Star,
  SwitchButton,
  VideoPlay,
} from '@element-plus/icons-vue'
import MessageNotification from './MessageNotification.vue'
import ChangePassword from './ChangePassword.vue'
import LotteryDialog from './LotteryDialog.vue'

// 路由和状态管理
const router = useRouter()
const route = useRoute()

// 抽屉导航控制
const drawerVisible = ref(false)

// 修改密码对话框引用
const changePasswordRef = ref()

// 抽奖对话框控制
const lotteryDialogVisible = ref(false)

// 公告栏accordion控制
const activeBulletinSections = ref([])
const bulletinMenuData = ref(null)

// 导航项
const navItems = ref([
  {
    path: '/academy',
    name: '研报博文',
    icon: 'Star', // 使用星星图标表示热门
    divider: false,
  },
  {
    path: '/academy/academy',
    name: '我的课程',
    icon: 'Monitor',
    divider: false,
  },
  {
    path: '/academy/messages',
    name: '消息通知',
    icon: 'ChatDotRound',
    divider: false,
    hasNotification: true,
  },
  {
    path: '/academy/promotions',
    name: '幸运抽奖',
    icon: 'Discount',
    divider: false,
  },
  {
    path: '/academy/live',
    name: '直播预告',
    icon: 'VideoPlay',
    divider: false,
  },
  {
    path: '/academy/bulletin',
    name: '文档',
    icon: 'Document',
    divider: true,
  },
  {
    path: '/academy/about',
    name: '关于匡醍',
    icon: 'InfoFilled',
    divider: false,
  },
])

// 当前活动的导航项
const activeNavItem = ref('')

// 计算属性：用户是否已登录
const isLoggedIn = computed(() => !!localStorage.getItem('customer'))

// 获取图标组件
const getIconComponent = (iconName) => {
  switch (iconName) {
    case 'Star': return Star;
    case 'Monitor': return Monitor;
    case 'ChatDotRound': return ChatDotRound;
    case 'Discount': return Discount;
    case 'VideoPlay': return VideoPlay;
    case 'Document': return Document;
    case 'InfoFilled': return InfoFilled;
    default: return null;
  }
}

// 导航方法
const navigateTo = (path, fromDrawer = false) => {
  if (fromDrawer) {
    drawerVisible.value = false
  }
  router.push(path)
  activeNavItem.value = path
}

// 打开修改密码对话框
const openChangePassword = () => {
  drawerVisible.value = false // 关闭抽屉菜单
  setTimeout(() => {
    changePasswordRef.value?.open() // 打开修改密码对话框
  }, 300) // 等待抽屉关闭动画完成
}

// 登出方法
const handleLogout = async () => {
  if (!isLoggedIn.value) return

  try {
    await fetch('/sso/customer/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // 确保 cookie 被发送
    })

    localStorage.removeItem('customer')
  } catch (error) {
    console.error('Logout failed:', error)
    // 即使出错也清除本地存储并重定向
    localStorage.removeItem('customer')
  }

  router.push('/academy/login')
}

// 监听路由变化，更新活动导航项
watch(
  () => route.path,
  (newPath) => {
    activeNavItem.value = newPath
  },
)

// 处理抽奖对话框关闭
const handleLotteryClose = () => {
  lotteryDialogVisible.value = false
}

// 加载公告栏菜单数据
const loadBulletinMenuData = async () => {
  try {
    const response = await fetch('/bulletin.json')
    if (response.ok) {
      bulletinMenuData.value = await response.json()
      console.log('Bulletin menu data loaded:', bulletinMenuData.value)
    } else {
      console.error('Failed to load bulletin menu data:', response.status)
    }
  } catch (error) {
    console.error('Error loading bulletin menu data:', error)
  }
}

// 处理公告栏accordion变化
const handleBulletinChange = (activeNames) => {
  // 可以在这里添加额外的逻辑
  console.log('Bulletin accordion changed:', activeNames)
}

// 处理公告栏菜单项点击
const handleBulletinItemClick = (menuItem) => {
  // 根据URL类型决定跳转方式
  if (menuItem.url.includes('/academy/markdown/')) {
    // 跳转到课程详情页面
    const filename = menuItem.url.split('/').pop()
    router.push(`/academy/course-detail/${filename}`)
  } else {
    // 其他类型的URL直接跳转
    router.push(menuItem.url)
  }
}

// 组件挂载时，根据当前路由设置活动导航项
onMounted(() => {
  activeNavItem.value = route.path

  // 加载公告栏菜单数据
  loadBulletinMenuData()

  // 检查注册后是否需要显示抽奖对话框
  const showLottery = localStorage.getItem('show_lottery')
  if (showLottery === 'true') {
    // 延迟一下，等页面加载完成
    setTimeout(() => {
      lotteryDialogVisible.value = true
      // 清除标记，避免重复显示
      localStorage.removeItem('show_lottery')
    }, 1000)
  }
})
</script>

<style scoped>
@import '../assets/styles/academy-layout.css';

/* 组件特定样式 */
.nav-notification,
.drawer-notification {
  margin-left: auto;
}

/* 公告栏accordion样式 */
.bulletin-accordion {
  margin: 0;
  width: 100%;
}

.bulletin-accordion :deep(.el-collapse) {
  border: none;
  background: transparent;
}

.bulletin-accordion :deep(.el-collapse-item) {
  border: none;
  background: transparent;
}

.bulletin-accordion :deep(.el-collapse-item__header) {
  background: transparent;
  border: none;
  padding: 0;
  height: auto;
  line-height: normal;
  color: rgba(255, 255, 255, 0.9);
}

.bulletin-accordion :deep(.el-collapse-item__arrow) {
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.8);
}

.bulletin-accordion :deep(.el-collapse-item__content) {
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 0 0 8px 8px;
  margin: 0 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bulletin-nav-item {
  width: 100%;
  margin: 0;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bulletin-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
  transition: all 0.3s ease;
}

.bulletin-submenu {
  margin: 0;
  padding: 12px 0;
  background: transparent;
}

.bulletin-category {
  margin-bottom: 16px;
}

.category-title {
  font-size: 11px;
  color: #8B5A96;
  margin: 0 0 8px 16px;
  padding: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.bulletin-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px 8px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 6px;
  border-left: 3px solid transparent;
}

.bulletin-menu-item:hover {
  background: rgba(255, 255, 255, 1);
  border-left-color: #D946EF;
  transform: translateX(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bulletin-menu-item .el-icon {
  margin-right: 10px;
  font-size: 14px;
  color: #8B5A96;
}

.menu-item-title {
  font-size: 13px;
  color: #4A5568;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  font-weight: 500;
  line-height: 1.4;
}
</style>
