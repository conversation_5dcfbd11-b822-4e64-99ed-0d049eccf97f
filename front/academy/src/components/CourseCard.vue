<!-- src/components/CourseCard.vue -->
<template>
  <div class="completed-course-card">
    <!-- 课程头部信息 -->
    <div class="course-header">
      <img :src="courseImage" :alt="title" class="course-thumbnail desktop-image" />
      <img :src="courseLargeImage" :alt="title" class="course-thumbnail mobile-image" />
      <div class="course-info">
        <h3 class="course-title">{{ title }}</h3>
        <div class="course-dates">
          <span class="date-label">开课:</span>
          <span>{{ formatDate(startTime) }}</span>
          <span class="date-label">结课:</span>
          <span>{{ formatDate(endTime) }}</span>
          <span class="date-label">访问截止:</span>
          <span>{{ formatDate(expireTime) }}</span>
        </div>
        <div class="course-plan">
          <span class="plan-badge" :class="level.toLowerCase()">{{ level }} {{ getPlanIcon(level) }}</span>
          <span class="course-level" :class="getCourseLevel(courseId)">{{ getCourseLevel(courseId) }}</span>
        </div>
      </div>
    </div>

    <!-- 课程资源 -->
    <div class="course-resources">
      <!-- 课件资源 -->
      <div v-if="courseware.length > 0" class="resource-section">
        <h4>课件资源</h4>
        <div class="resource-links">
          <!-- 使用后端返回的资源可访问性数据 -->
          <template v-for="resource in courseware" :key="`courseware-${resource.chapter}`">
            <!-- 可访问的资源添加链接 -->
            <a v-if="resource.url" :href="resource.url" class="resource-link">
              {{ resource.chapter }}
            </a>
            <!-- 不可访问的资源不添加链接，并添加提示 -->
            <span v-else class="resource-link inaccessible" :title="resource.reason">
              {{ resource.chapter }}
            </span>
          </template>
        </div>
      </div>

      <!-- 习题资源 -->
      <div v-if="assignments.length > 0" class="resource-section">
        <h4>习题资源</h4>
        <div class="resource-links">
          <template v-for="resource in assignments" :key="`assignments-${resource.chapter}`">
            <!-- 可访问的习题添加链接 -->
            <a v-if="resource.url" :href="resource.url" class="resource-link">
              {{ resource.chapter }}
            </a>
            <!-- 不可访问的习题不添加链接，并添加提示 -->
            <span v-else class="resource-link inaccessible" :title="resource.reason">
              {{ resource.chapter }}
            </span>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import l24Image from '@/assets/24lectures_300.png';
import faImage from '@/assets/fa_300.png';
import pandasImage from '@/assets/pandas_300.png';
import { computed, defineProps } from 'vue';

const props = defineProps({
  title: String,
  level: String,
  image: String,
  chap: Array,
  courseId: String,
  account: String,
  startTime: String,
  endTime: String,
  expireTime: String,
  refundEndTime: String, // 退款期结束时间
  courseware: Array, // 课程
  assignments: Array, // 习题资源
})

// 导入大尺寸图片
import l24ImageLarge from '@/assets/24lectures_1000.png';
import faImageLarge from '@/assets/fa_1000.png';
import pandasImageLarge from '@/assets/pandas_1000.png';

// 计算属性：根据courseId选择正确的图片
const courseImage = computed(() => {
  if (props.courseId === 'fa') return faImage
  if (props.courseId === 'pandas') return pandasImage
  if (props.courseId === 'l24') return l24Image

  // 如果有图片路径且以http开头，直接使用
  if (props.image && (props.image.startsWith('http') || props.image.startsWith('/'))) {
    return props.image
  }

  // 默认返回fa图片
  return faImage
})

// 计算属性：根据courseId选择大尺寸图片（用于响应式）
const courseLargeImage = computed(() => {
  if (props.courseId === 'fa') return faImageLarge
  if (props.courseId === 'pandas') return pandasImageLarge
  if (props.courseId === 'l24') return l24ImageLarge

  // 如果有图片路径且以http开头，直接使用
  if (props.image && (props.image.startsWith('http') || props.image.startsWith('/'))) {
    return props.image
  }

  // 默认返回fa图片
  return faImageLarge
})

// 根据套餐级别返回图标
const getPlanIcon = (level) => {
  const lowerLevel = level.toLowerCase()

  switch (lowerLevel) {
    case 'diamond':
      return '💎' // 钻石图标
    case 'platinum':
      return '💍' // 四角星
    case 'gold':
      return '👑' // 金牌
    case 'silver':
      return '🏅' // 银牌
    case 'bronze':
      return '🪙' // 铜牌
    case 'iron':
      return '⚙️' // 齿轮图标
    default:
      return '•' // 圆点
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 获取课程级别
const getCourseLevel = (courseId) => {
  if (courseId === 'fa') return '难度: 高级';
  if (courseId === 'pandas') return '难度:初级';
  if (courseId === 'l24') return '难度：中级';

  // 默认返回中级
  return '难度：中级';
}

// 注意：资源 URL 已经由后端生成，不需要在前端生成
// 后端会根据用户是否有独立容器来决定使用哪个账号
</script>

<style scoped>
.completed-course-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  border: 1px solid #eee;
  padding: 20px;
  max-width: 100%;
  box-sizing: border-box;
}

.completed-course-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
  border-color: #FFCC70;
}

.course-header {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.course-thumbnail {
  border-radius: 5px;
}

.desktop-image {
  width: clamp(80px, 15vw, 100px);
  height: clamp(80px, 15vw, 100px);
  object-fit: cover;
  display: block;
}

.mobile-image {
  display: none;
}

.course-info {
  flex: 1;
}

.course-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.course-dates {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.date-label {
  color: #909399;
  margin-right: 5px;
}

.date-label:not(:first-child) {
  margin-left: 15px;
}

.course-plan {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.plan-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.course-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.course-level.初级 {
  background-color: #4BC0C0;
  box-shadow: 0 2px 4px rgba(75, 192, 192, 0.3);
}

.course-level.中级 {
  background-color: #FF9843;
  box-shadow: 0 2px 4px rgba(255, 152, 67, 0.3);
}

.course-level.高级 {
  background-color: #FF4B91;
  box-shadow: 0 2px 4px rgba(255, 75, 145, 0.3);
}

.plan-badge.platinum {
  background-color: #FF4B91;
  box-shadow: 0 2px 4px rgba(255, 75, 145, 0.3);
}

.plan-badge.gold {
  background-color: #FFD700;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
  color: #333;
}

.plan-badge.silver {
  background-color: #C0C0C0;
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
  color: #333;
}

.plan-badge.bronze {
  background-color: #CD7F32;
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.plan-badge.iron {
  background-color: #A19D94;
  box-shadow: 0 2px 4px rgba(161, 157, 148, 0.3);
}

.plan-badge.diamond {
  background: linear-gradient(135deg, #3a6186, #89253e);
  box-shadow: 0 2px 4px rgba(58, 97, 134, 0.3);
}

.course-resources {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.resource-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.resource-links {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.resource-link {
  display: inline-block;
  padding: 5px 10px;
  background-color: rgba(200, 80, 192, 0.1);
  color: #C850C0;
  border-radius: 4px;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-right: 10px;
  margin-bottom: 8px;
}

.resource-link:hover {
  color: #4158D0;
  background-color: rgba(65, 88, 208, 0.1);
  transform: translateY(-2px);
}

.resource-link.inaccessible {
  color: #909399;
  background-color: #f5f7fa;
  cursor: not-allowed;
  border: 1px dashed #dcdfe6;
}

/* 退款期间不开放的章节样式 */
.resource-link.inaccessible[title*="解锁日期"] {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px dashed #67c23a;
}

/* 需要升级套餐的章节样式 */
.resource-link.inaccessible[title*="请升级套餐"] {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px dashed #f56c6c;
}

/* 响应式设计 - 使用视口查询 */
.completed-course-card {
  padding: clamp(15px, 3vw, 20px);
}

.course-header {
  flex-direction: row;
  gap: clamp(10px, 2vw, 20px);
}

.resource-links {
  flex-wrap: wrap;
  gap: clamp(5px, 1vw, 10px);
}

.resource-link {
  font-size: clamp(12px, 1.5vw, 14px);
  padding: clamp(4px, 1vw, 8px) clamp(8px, 1.5vw, 10px);
  margin-right: clamp(5px, 1vw, 10px);
  margin-bottom: clamp(5px, 1vw, 8px);
}

/* 在小屏幕上应用特定样式 */
@media (max-width: 768px) {
  .course-header {
    flex-direction: column;
  }

  .desktop-image {
    display: none;
  }

  .mobile-image {
    display: block;
    width: 100%;
    height: clamp(150px, 30vw, 200px);
    object-fit: cover;
    object-position: center top;
    overflow: hidden;
  }

  .course-dates {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .date-label:not(:first-child) {
    margin-left: 0;
    margin-top: 5px;
  }
}

/* 设备特定样式已移至 autoScale.ts 中通过 JavaScript 动态处理
   可以使用 .device-xs, .device-sm, .device-md, .device-lg 类来定位不同设备 */
.device-xs .completed-course-card,
.device-md .completed-course-card {
  padding: 15px;
}

.device-xs .resource-links,
.device-md .resource-links {
  gap: 5px;
}
</style>
