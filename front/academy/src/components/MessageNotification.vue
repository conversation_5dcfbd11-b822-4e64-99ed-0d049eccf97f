<template>
    <div class="message-notification" :class="{ 'nav-mode': isNavMode, 'drawer-mode': isDrawerMode }">
        <!-- 消息图标和未读消息数量 -->
        <div class="message-icon" @click="goToMessages">
            <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="message-badge">
                <el-icon :size="iconSize">
                    <Bell />
                </el-icon>
            </el-badge>
        </div>
    </div>
</template>

<script setup>
import { Bell } from '@element-plus/icons-vue';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from '../router/axios';

// 路由器
const router = useRouter();

// 未读消息数量
const unreadCount = ref(0);

// 计算属性：当前用户ID
const customerId = computed(() => localStorage.getItem('customer'));

// 使用 props 来确定组件的模式，而不是依赖 DOM
const props = defineProps({
    mode: {
        type: String,
        default: 'default', // 可能的值: 'default', 'nav', 'drawer'
        validator: (value) => ['default', 'nav', 'drawer'].includes(value)
    }
});

// 计算属性：是否在导航条模式
const isNavMode = computed(() => props.mode === 'nav');

// 计算属性：是否在抽屉菜单模式
const isDrawerMode = computed(() => props.mode === 'drawer');

// 计算属性：图标大小
const iconSize = computed(() => {
    if (isNavMode.value) return 18; // 导航条模式下图标小一点
    if (isDrawerMode.value) return 20; // 抽屉菜单模式下图标中等
    return 24; // 默认大小
});

// 获取未读消息数量
const fetchUnreadCount = async () => {
    if (!customerId.value) return;

    try {
        const response = await axios.get('/api/academy/messages/unread');
        unreadCount.value = response.data.unread_count;
    } catch (error) {
        console.error('获取未读消息数量失败:', error);
    }
};

// 跳转到消息页面
const goToMessages = () => {
    router.push('/academy/messages');
};

// 组件挂载时获取消息数据
onMounted(() => {
    if (customerId.value) {
        fetchUnreadCount();

        // 定时刷新未读消息数量（每分钟）
        setInterval(fetchUnreadCount, 60000);
    }
});
</script>

<style scoped>
.message-notification {
    position: relative;
    /* 确保在小屏幕上不会被截断 */
    max-width: 100%;
    overflow: visible;
    z-index: 10;
}

.message-icon {
    cursor: pointer;
    padding: 5px;
}

.message-badge :deep(.el-badge__content) {
    background-color: #f56c6c;
    font-size: 12px;
}

/* 小屏幕设备上的额外样式 */
body.small-screen .message-notification {
    transform: scale(calc(1 / var(--scale-ratio)));
    transform-origin: center right;
}

/* 导航条模式样式 */
.nav-mode .message-icon {
    padding: 0;
    margin-left: 10px;
}

.nav-mode .message-badge :deep(.el-badge__content) {
    font-size: 10px;
    transform: scale(0.8);
    transform-origin: center;
}

/* 抽屉菜单模式样式 */
.drawer-mode .message-icon {
    padding: 0;
    margin-left: 5px;
}

/* 未读消息标签样式 */
.unread-tag {
    margin-right: 5px;
    font-size: 10px;
    padding: 0 4px;
    height: 18px;
    line-height: 16px;
}

.message-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #ebeef5;
}

.message-list-header h3 {
    margin: 0;
    font-size: 16px;
}

.message-list {
    max-height: 400px;
    overflow-y: auto;
}

.message-item {
    position: relative;
    padding: 12px 15px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    transition: all 0.3s;
    overflow: hidden;
}

.message-item:hover {
    background-color: #f5f7fa;
}

.message-item.unread {
    background-color: #ecf5ff;
}

.message-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: #409eff;
}

.message-title {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    margin-bottom: 5px;
}

.message-time {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
}

.message-content {
    font-size: 14px;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.message-loading,
.no-messages {
    padding: 20px;
    text-align: center;
}

.message-footer {
    padding: 10px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #ebeef5;
}

/* 移动端左滑删除 */
.mobile-actions {
    position: absolute;
    right: -100px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    transition: transform 0.3s;
    background-color: #f56c6c;
    width: 80px;
    justify-content: center;
}

@media (hover: none) {

    /* 仅在触摸设备上启用左滑功能 */
    .message-item {
        touch-action: pan-y;
    }

    .message-item:active {
        transform: translateX(-80px);
    }

    .message-item:active .mobile-actions {
        transform: translateX(0);
    }
}

/* 桌面端悬停效果 */
@media (hover: hover) {
    .message-item:hover .mobile-actions {
        display: none;
    }
}
</style>

<style>
/* 全局样式，确保弹出框样式正确 */
.message-popover {
    padding: 0 !important;
    max-width: 90vw;
}
</style>
