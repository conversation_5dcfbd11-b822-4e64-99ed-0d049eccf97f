<template>
  <div class="bulletin-accordion">
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item v-for="(item, index) in bulletinItems" :key="index" :name="item.path">
        <template #title>
          <div class="bulletin-title">
            <el-icon><Document /></el-icon>
            <span>{{ item.title }}</span>
          </div>
        </template>
        <div v-if="loadingContent[item.path]" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>
        <div v-else-if="errorContent[item.path]" class="error-container">
          <el-alert :title="errorContent[item.path]" type="error" :closable="false" show-icon />
        </div>
        <div v-else class="markdown-content" v-html="renderedContent[item.path]"></div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Document } from '@element-plus/icons-vue';
import axios from 'axios';
import MarkdownIt from 'markdown-it';

// 创建markdown-it实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
});

// 定义props
const props = defineProps({
  // 是否自动加载公告栏配置
  autoLoad: {
    type: Boolean,
    default: true
  }
});

// 状态变量
const bulletinItems = ref([]);
const activeNames = ref([]);
const loadingContent = ref({});
const errorContent = ref({});
const renderedContent = ref({});

// 加载公告栏配置
const loadBulletinConfig = async () => {
  try {
    // 从API获取公告栏配置
    const response = await axios.get('/api/provision/config');
    const config = response.data;
    
    // 检查是否有bulletin配置
    if (config && config.bulletin && Array.isArray(config.bulletin.items)) {
      bulletinItems.value = config.bulletin.items;
      
      // 如果有项目，默认展开第一个
      if (bulletinItems.value.length > 0) {
        activeNames.value = [bulletinItems.value[0].path];
        await loadMarkdownContent(bulletinItems.value[0].path);
      }
    }
  } catch (err) {
    console.error('加载公告栏配置失败:', err);
  }
};

// 加载markdown内容
const loadMarkdownContent = async (path) => {
  // 如果已经加载过，不重复加载
  if (renderedContent.value[path]) {
    return;
  }
  
  try {
    // 设置加载状态
    loadingContent.value[path] = true;
    errorContent.value[path] = null;
    
    // 从API获取markdown内容
    const response = await axios.get(`/api/bulletin/${path}`);
    const content = response.data;
    
    // 渲染markdown内容
    renderedContent.value[path] = md.render(content);
  } catch (err) {
    console.error(`加载markdown内容失败: ${path}`, err);
    errorContent.value[path] = '加载内容失败，请稍后重试';
  } finally {
    loadingContent.value[path] = false;
  }
};

// 处理折叠面板变化
const handleChange = (val) => {
  // 加载新展开项的内容
  val.forEach(path => {
    if (!renderedContent.value[path]) {
      loadMarkdownContent(path);
    }
  });
};

// 组件挂载时加载配置
onMounted(() => {
  if (props.autoLoad) {
    loadBulletinConfig();
  }
});

// 暴露方法和数据给父组件
defineExpose({
  loadBulletinConfig,
  loadMarkdownContent,
  bulletinItems
});
</script>

<style scoped>
.bulletin-accordion {
  width: 100%;
  margin-bottom: 20px;
}

.bulletin-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.loading-container, .error-container {
  padding: 16px;
}

.markdown-content {
  padding: 16px;
  line-height: 1.6;
}

.markdown-content :deep(h1) {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3rem;
}

.markdown-content :deep(h2) {
  font-size: 1.5rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid #eaecef;
}

.markdown-content :deep(h3) {
  font-size: 1.25rem;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.markdown-content :deep(p) {
  margin-bottom: 1rem;
}

.markdown-content :deep(ul), .markdown-content :deep(ol) {
  padding-left: 2rem;
  margin-bottom: 1rem;
}

.markdown-content :deep(li) {
  margin-bottom: 0.5rem;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 1rem;
  color: #6a737d;
  margin-bottom: 1rem;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
}

.markdown-content :deep(table th), .markdown-content :deep(table td) {
  border: 1px solid #dfe2e5;
  padding: 0.6rem 1rem;
}

.markdown-content :deep(table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 1rem;
  overflow: auto;
  margin-bottom: 1rem;
}

.markdown-content :deep(code) {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
}
</style>
