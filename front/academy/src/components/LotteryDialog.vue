<template>
    <el-dialog v-model="dialogVisible" :title="title" width="400px" :close-on-click-modal="false" :show-close="true"
        class="lottery-dialog">
        <div class="lottery-content">
            <!-- 抽奖前界面已移除，直接进入抽奖动画 -->

            <!-- 抽奖中 -->
            <div v-if="step === 'drawing'" class="lottery-drawing">
                <div class="lottery-wheel">
                    <img src="@/assets/logo.png" alt="抽奖中" class="rotating" />
                </div>
                <h3>抽奖中...</h3>
                <p>请稍候</p>
                <div class="single-random-number">
                    <span class="random-number">{{ currentRandomNumber }}</span>
                </div>
            </div>

            <!-- 抽奖结果 -->
            <div v-else-if="step === 'result'" class="lottery-result">
                <div class="result-icon">
                    <img src="@/assets/logo.png" alt="抽奖成功" />
                </div>
                <h3>恭喜您抽中了</h3>
                <div v-if="ticket" class="ticket">
                    <div class="ticket-header">
                        <span class="course">{{ ticket.course_name || ticket.course }}</span>
                        <span class="discount">{{ ticket.discount }}元</span>
                    </div>
                    <div class="ticket-body">
                        <p class="ticket-code">券码: <span>{{ ticket.ticket_code }}</span></p>
                        <p>本券有最低消费额{{ ticket.base_fare }}元，可叠加，最大叠加金额{{ ticket.max_discount }}元。</p>
                        <p>本券可转让一次，转让后，受让者都可获得一半面值即{{ ticket.discount / 2 }}元的奖励用以购买课程，您将获得等值的现金返还。</p>
                        <p class="expires">有效期至: {{ formatDate(ticket.expires_at) }}</p>
                    </div>
                </div>
                <div v-else class="error-message">
                    <p>奖券信息加载失败，请重试</p>
                </div>
                <div class="actions">
                    <el-button type="primary" @click="closeDialog">开心收下</el-button>
                </div>
            </div>

            <!-- 调试信息 -->
            <div v-if="false" class="debug-info">
                <p>当前状态: {{ step }}</p>
                <p>票券信息: {{ ticket ? '已加载' : '未加载' }}</p>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
    name: 'LotteryDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        course: {
            type: String,
            default: ''
        }
    },
    emits: ['update:visible', 'close', 'lottery-complete'],
    setup(props, { emit }) {
        const dialogVisible = computed({
            get: () => props.visible,
            set: (val) => emit('update:visible', val)
        })

        const step = ref('start')
        const configs = ref([])
        const selectedCourse = ref(props.course)
        const ticket = ref(null)
        const remainingTimes = ref(0)
        const loading = ref(false)

        // 计算属性
        const title = computed(() => {
            if (step.value === 'start') return '抽奖活动'
            if (step.value === 'drawing') return '抽奖中'
            if (step.value === 'result') return '抽奖结果'
            return '抽奖活动'
        })

        const maxDiscount = computed(() => {
            if (configs.value.length === 0) return 0
            // 使用 max 属性，这是抽奖配置中的最大奖金
            return Math.max(...configs.value.map(config => config.max || 0))
        })

        const canDraw = computed(() => {
            // 如果没有选择课程，默认允许抽奖
            if (!selectedCourse.value && configs.value.length > 0) {
                // 自动选择第一个有抽奖次数的课程
                const availableCourse = configs.value.find(c => c.remaining_times > 0)
                if (availableCourse) {
                    selectedCourse.value = availableCourse.course
                    return true
                }
                // 如果没有可用的课程，选择第一个课程
                selectedCourse.value = configs.value[0].course
            }

            // 检查选择的课程是否有抽奖次数
            const config = configs.value.find(c => c.course === selectedCourse.value)
            return config && config.remaining_times > 0
        })

        // 方法
        const loadConfigs = async () => {
            try {
                loading.value = true
                const response = await axios.get('/api/academy/lottery/config')
                configs.value = response.data.configs || []

                // 自动选择第一个有抽奖次数的课程
                if (configs.value.length > 0 && !selectedCourse.value) {
                    const availableCourse = configs.value.find(c => c.remaining_times > 0)
                    if (availableCourse) {
                        selectedCourse.value = availableCourse.course
                    } else {
                        selectedCourse.value = configs.value[0].course
                    }
                }

                return configs.value
            } catch (error) {
                console.error('Failed to load lottery configs:', error)
                ElMessage.error('加载抽奖配置失败')
                return []
            } finally {
                loading.value = false
            }
        }

        // 生成随机数显示
        const currentRandomNumber = ref(0)
        const generateRandomNumbers = () => {
            // 找到当前选择的课程配置
            const config = configs.value.find(c => c.course === selectedCourse.value)
            if (!config) return

            // 根据配置生成可能的随机数
            const min = config.min || 100
            const max = config.max || 500
            const step = config.step || 50

            // 计算可能的值
            const possibleValues = []
            for (let i = min; i <= max; i += step) {
                possibleValues.push(i)
            }

            // 随机选择一个值显示
            const randomIndex = Math.floor(Math.random() * possibleValues.length)
            currentRandomNumber.value = possibleValues[randomIndex]
        }

        const startDraw = async () => {
            try {
                // 确保配置已加载且已选择课程
                if (configs.value.length === 0) {
                    // 如果配置尚未加载完成，等待加载
                    await loadConfigs()
                }

                // 确保已选择课程
                if (!selectedCourse.value && configs.value.length > 0) {
                    // 自动选择第一个有抽奖次数的课程
                    const availableCourse = configs.value.find(c => c.remaining_times > 0)
                    if (availableCourse) {
                        selectedCourse.value = availableCourse.course
                    } else {
                        // 如果没有可用的课程，选择第一个课程
                        selectedCourse.value = configs.value[0].course
                    }
                }

                // 检查是否有抽奖次数
                const config = configs.value.find(c => c.course === selectedCourse.value)
                if (!config || config.remaining_times <= 0) {
                    ElMessage.warning('您的抽奖次数已用完')
                    closeDialog()
                    return
                }

                step.value = 'drawing'

                // 初始化随机数
                generateRandomNumbers()

                // 每100ms更新一次随机数显示，让数字变化更快
                const interval = setInterval(generateRandomNumbers, 100)

                // 获取服务器结果（在动画开始前就已经确定）
                let serverResponse
                try {
                    serverResponse = await axios.post('/api/academy/lottery/draw', {
                        course: selectedCourse.value
                    })

                    // 检查服务器是否返回错误
                    if (!serverResponse.data.success && serverResponse.data.error) {
                        // 特殊处理已有有效奖券的情况
                        if (serverResponse.data.error.includes("already have a valid ticket")) {
                            console.error('用户已有该课程的有效奖券:', serverResponse.data.error)
                            ElMessage.warning('您已经有该课程的有效奖券，不能重复抽取')
                            closeDialog()
                            return
                        } else {
                            console.error('抽奖失败:', serverResponse.data.error)
                            ElMessage.error(serverResponse.data.error || '抽奖失败')
                            closeDialog()
                            return
                        }
                    }
                } catch (error) {
                    console.error('Failed to get lottery result:', error)
                    ElMessage.error('抽奖失败')
                    closeDialog()
                    return
                }

                // 延迟3秒，让用户看到抽奖动画
                setTimeout(() => {
                    try {
                        clearInterval(interval) // 停止随机数更新

                        // 最后显示服务器返回的实际金额
                        if (serverResponse && serverResponse.data && serverResponse.data.success && serverResponse.data.ticket) {
                            // 设置最终显示的数字为服务器返回的金额
                            currentRandomNumber.value = serverResponse.data.ticket.discount

                            // 使用已获取的服务器响应
                            ticket.value = serverResponse.data.ticket
                            remainingTimes.value = serverResponse.data.remaining_times

                            console.log('抽奖结果:', serverResponse.data.ticket)
                            console.log('票券信息:', ticket.value)
                            console.log('课程信息:', serverResponse.data.ticket.course, serverResponse.data.ticket.course_name)

                            // 更新配置中的剩余次数
                            const config = configs.value.find(c => c.course === selectedCourse.value)
                            if (config) {
                                config.remaining_times = serverResponse.data.remaining_times
                            }

                            console.log('显示抽奖结果界面')
                            // 直接显示结果界面
                            step.value = 'result'

                            // 确保在下一个事件循环中票券信息已经更新
                            setTimeout(() => {
                                console.log('检查票券信息是否已更新:', ticket.value)
                                if (!ticket.value) {
                                    console.error('票券信息未正确更新')
                                    ticket.value = serverResponse.data.ticket
                                }

                                // 触发抽奖完成事件，通知父组件更新奖券列表
                                emit('lottery-complete', ticket.value)
                            }, 0)
                        } else {
                            console.error('服务器响应无效:', serverResponse)
                            ElMessage.error('抽奖失败，请稍后再试')
                            closeDialog()
                        }
                    } catch (error) {
                        console.error('Failed to draw lottery:', error)
                        ElMessage.error('抽奖失败')
                        closeDialog()
                    }
                }, 3000) // 3秒动画时间
            } catch (error) {
                console.error('Failed to start drawing:', error)
                ElMessage.error('开始抽奖失败')
                closeDialog()
            }
        }

        const continueDraw = () => {
            ticket.value = null
            // 直接开始新的抽奖，而不是返回到初始界面
            startDraw()
        }

        const closeDialog = () => {
            dialogVisible.value = false
            emit('close')
        }

        // courseLabel 方法已移除，改为从服务器获取课程名称

        const formatDate = (dateString) => {
            if (!dateString) return ''
            const date = new Date(dateString)
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
        }

        const getRemainingTimes = (course) => {
            const config = configs.value.find(c => c.course === course)
            return config ? config.remaining_times : 0
        }

        // 生命周期钩子
        onMounted(() => {
            if (dialogVisible.value) {
                loadConfigs()
            }
        })

        // 监听对话框可见性变化
        watch(dialogVisible, (newVal) => {
            if (newVal) {
                loadConfigs()
                ticket.value = null
                // 直接开始抽奖动画，而不是显示初始界面
                startDraw()
            }
        })

        // 监听课程变化
        watch(() => props.course, (newVal) => {
            if (newVal) {
                selectedCourse.value = newVal
            }
        })

        return {
            dialogVisible,
            step,
            configs,
            selectedCourse,
            ticket,
            remainingTimes,
            currentRandomNumber,
            title,
            maxDiscount,
            canDraw,
            startDraw,
            continueDraw,
            closeDialog,
            formatDate,
            getRemainingTimes
        }
    }
}
</script>

<style scoped>
.lottery-dialog :deep(.el-dialog__header) {
    text-align: center;
    padding: 15px;
    margin-right: 0;
}

.lottery-content {
    padding: 20px;
    text-align: center;
}

.lottery-icon img,
.result-icon img {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
}

.lottery-wheel img {
    width: 150px;
    height: 150px;
    margin-bottom: 15px;
}

.rotating {
    animation: rotate 5s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.lottery-start h3,
.lottery-result h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #409EFF;
}

.lottery-start p {
    margin-bottom: 20px;
    color: #606266;
}

.course-info {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border: 1px solid #d9ecff;
}

.course-info h4 {
    margin: 0 0 5px 0;
    color: #409EFF;
    font-size: 16px;
}

.remaining-times-info {
    margin: 0;
    color: #606266;
    font-size: 14px;
}

.ticket {
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    margin: 15px 0;
    overflow: hidden;
    background: #f8f8f8;
}

.ticket-header {
    background: #409EFF;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ticket-header .course {
    font-weight: bold;
}

.ticket-header .discount {
    font-size: 20px;
    font-weight: bold;
}

.ticket-body {
    padding: 15px;
    text-align: left;
}

.ticket-body p {
    margin: 5px 0;
    font-size: 14px;
    color: #606266;
}

.ticket-code {
    font-weight: bold;
    margin-bottom: 10px !important;
}

.ticket-code span {
    background: #f0f9ff;
    color: #409EFF;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    letter-spacing: 1px;
}

.expires {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 10px;
}

.error-message {
    background-color: #fef0f0;
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
    color: #F56C6C;
}

.debug-info {
    margin-top: 20px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    text-align: left;
    font-family: monospace;
    font-size: 12px;
}

.actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.single-random-number {
    margin-top: 15px;
    display: flex;
    justify-content: center;
}

.random-number {
    display: inline-block;
    padding: 10px 20px;
    background-color: #f0f9ff;
    border: 1px solid #d9ecff;
    border-radius: 4px;
    color: #409EFF;
    font-weight: bold;
    font-size: 24px;
    min-width: 80px;
    text-align: center;
    animation: fadeInOut 0.5s infinite alternate;
}

@keyframes fadeInOut {
    from {
        opacity: 0.5;
    }

    to {
        opacity: 1;
    }
}
</style>
