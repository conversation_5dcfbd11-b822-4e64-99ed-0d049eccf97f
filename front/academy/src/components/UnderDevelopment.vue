<template>
  <div class="under-development">
    <div class="development-container">
      <el-icon class="development-icon"><Tools /></el-icon>
      <h2>功能开发中</h2>
      <p>{{ message || '该功能正在开发中，敬请期待！' }}</p>
      <el-button type="primary" @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import { Tools } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  message: String,
  featureName: String
});

const router = useRouter();

const goBack = () => {
  router.push('/academy');
};
</script>

<style scoped>
.under-development {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
  text-align: center;
}

.development-container {
  max-width: 500px;
  padding: 40px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.5s ease-in-out;
}

.development-icon {
  font-size: 60px;
  color: #409EFF;
  margin-bottom: 20px;
  animation: rotate 3s infinite linear;
}

h2 {
  font-size: 24px;
  margin-bottom: 15px;
  color: #303133;
}

p {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
  line-height: 1.6;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
