<template>
  <div class="accordion-menu">
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item v-for="(item, index) in menuItems" :key="index" :name="item.name">
        <template #title>
          <div class="accordion-title">
            <el-icon v-if="item.icon"><component :is="getIconComponent(item.icon)" /></el-icon>
            <span>{{ item.title }}</span>
          </div>
        </template>
        <div class="accordion-content">
          <div 
            v-for="(subItem, subIndex) in item.children" 
            :key="subIndex" 
            class="sub-menu-item"
            @click="handleSubItemClick(subItem)"
          >
            <el-icon v-if="subItem.icon"><component :is="getIconComponent(subItem.icon)" /></el-icon>
            <span>{{ subItem.title }}</span>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

const router = useRouter();

// 定义props
const props = defineProps({
  // 菜单项配置
  items: {
    type: Array,
    default: () => []
  },
  // 默认展开的菜单项
  defaultActive: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['change', 'select']);

// 状态变量
const activeNames = ref(props.defaultActive);
const menuItems = computed(() => props.items);

// 获取图标组件
const getIconComponent = (iconName) => {
  if (!iconName) return null;
  return ElementPlusIconsVue[iconName] || null;
};

// 处理折叠面板变化
const handleChange = (val) => {
  emit('change', val);
};

// 处理子菜单项点击
const handleSubItemClick = (item) => {
  emit('select', item);
  
  // 如果有路由，则导航到该路由
  if (item.route) {
    router.push(item.route);
  }
};

// 暴露方法和数据给父组件
defineExpose({
  activeNames
});
</script>

<style scoped>
.accordion-menu {
  width: 100%;
  margin-bottom: 20px;
}

.accordion-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.accordion-content {
  padding: 8px 0;
}

.sub-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-radius: 4px;
  margin-bottom: 4px;
}

.sub-menu-item:hover {
  background-color: #f5f7fa;
}

.sub-menu-item .el-icon {
  font-size: 16px;
  color: #909399;
}
</style>
