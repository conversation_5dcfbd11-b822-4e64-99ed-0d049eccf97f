<script setup lang="ts">
import axios from 'axios';
import { computed, onMounted, provide, ref } from 'vue';
import { RouterLink, RouterView, useRoute, useRouter } from 'vue-router';
import MessageNotification from './components/MessageNotification.vue';
import ChangePassword from './components/ChangePassword.vue';
import { Avatar } from '@element-plus/icons-vue';

const route = useRoute()
const router = useRouter()
const activeRoute = computed(() => route.path)

// 判断当前是否为登录页面
const isLoginPage = computed(() => {
  return route.path === '/login' || route.path === '/academy/login'
})

// 检查用户是否已登录
const customerId = computed(() => localStorage.getItem('customer'))
const isLoggedIn = computed(() => !!customerId.value)

// 获取用户名
const username = ref('')

// 计算用户名首字母
const userInitial = computed(() => {
  if (!username.value) return ''
  return username.value.charAt(0).toUpperCase()
})

// 获取用户信息
const fetchUserInfo = () => {
  if (!customerId.value) return

  try {
    // 从 localStorage 获取用户名
    const storedUsername = localStorage.getItem('username')
    if (storedUsername) {
      username.value = storedUsername
    } else {
      // 如果没有用户名，使用默认值
      username.value = 'User'
    }
  } catch (error) {
    console.error('Failed to get username from localStorage:', error)
    username.value = 'User' // 默认用户名
  }
}

// 处理下拉菜单命令
const changePasswordRef = ref()
const handleCommand = (command: string) => {
  if (command === 'logout') {
    handleLogout()
  } else if (command === 'changePassword') {
    changePasswordRef.value?.open()
  }
}

// 登出方法
const handleLogout = async () => {
  if (!isLoggedIn.value) return

  try {
    await fetch('/sso/customer/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include' // 确保cookie被发送
    })

    localStorage.removeItem('customer')
    localStorage.removeItem('username')
    username.value = ''
  } catch (error) {
    console.error('Logout failed:', error)
    // 即使出错也清除本地存储并重定向
    localStorage.removeItem('customer')
    localStorage.removeItem('username')
    username.value = ''
  }

  router.push('/login')
}

// 页面加载时获取用户信息
onMounted(() => {
  if (isLoggedIn.value) {
    fetchUserInfo()
  }
})
</script>

<template>
  <div class="app-container">
    <!-- 统一的 Header，在登录页面不显示 -->
    <header class="app-header" v-if="!isLoginPage">
      <div class="header-content">
        <div class="logo-container">
          <img alt="QuantIDE logo" class="logo" src="@/assets/logo.png" />
        </div>
        <div class="header-right" v-if="!isLoginPage && route.path.startsWith('/academy')">
          <!-- 消息通知组件 -->
          <MessageNotification class="header-notification" />

          <!-- 用户头像和下拉菜单 -->
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="user-avatar">
              <el-avatar :size="40" v-if="userInitial">{{ userInitial }}</el-avatar>
              <el-avatar :size="40" icon="Avatar" v-else />
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item disabled>{{ username || 'User' }}</el-dropdown-item>
                <el-dropdown-item command="changePassword" :disabled="!isLoggedIn">修改密码</el-dropdown-item>
                <el-dropdown-item divided :disabled="!isLoggedIn" command="logout">Logout</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="spacer" v-else></div>
      </div>
    </header>

    <!-- 修改密码对话框 -->
    <ChangePassword ref="changePasswordRef" />

    <div class="main-layout" :class="{ 'no-header': isLoginPage }">
      <main class="content-area">
        <RouterView />
      </main>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  overflow: auto;
}


.app-header {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 0.5rem 2rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 70px;
}

.header-content {
  max-width: 1400px;
  margin: 0;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
  position: absolute;
  right: 0;
}

.user-avatar {
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-avatar .el-avatar {
  background-color: #4158D0;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.header-notification {
  margin-right: 10px;
}

.logo-container {
  position: absolute;
  width: 220px;
  left: 0;
}

.slogan {
  margin: 0 0 0 220px !important;
  width: calc(100% - 220px);
  height: 100%;
  position: absolute;
  font-size: 1.5rem;
  color: #37474f;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spacer {
  width: 70px;
  /* 与logo宽度相同，保持对称 */
}

.logo {
  height: 50px;
  width: auto;
  margin-right: 20px;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .logo-container {
    position: relative;
    width: auto;
    left: 0;
    display: block;
  }

  .logo {
    height: 40px;
    margin-right: 10px;
  }

  .slogan {
    display: none;
  }

  .app-header {
    display: none;
    height: 60px;
    padding: 0.5rem 1rem;
  }

  .header-content {
    justify-content: space-between;
    display: flex;
    align-items: center;
    position: relative;
  }

  .spacer {
    display: none;
  }

  /* 调整main-layout，使内容从顶部开始显示 */
  .main-layout {
    margin-top: 60px;
  }
}

.main-layout {
  display: flex;
  margin-top: 70px;
  max-width: 1400px;
  min-height: calc(100vh - 70px);
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* 登录页面没有header，不需要margin-top */
.main-layout.no-header {
  margin-top: 0;
  min-height: 100vh;
}

.content-area {
  flex-grow: 1;
  padding: 1rem 0;
  min-height: calc(100vh - 70px);
  min-width: 500px;
}

@media (max-width: 768px) {
  .content-area {
    margin-left: 5px;
  }
}
</style>
