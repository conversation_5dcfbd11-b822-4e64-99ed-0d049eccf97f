/* 共享布局样式 */
.academy-container {
    display: flex;
    min-height: 100%;
    background-color: #f8f9fa;
    background-image: linear-gradient(120deg, #fdfbfb 0%, #e<PERSON>ee 100%);
    overflow: hidden;
    margin-top: -70px;
    padding-top: 70px;
}

/* 侧边栏样式 */
.sidebar {
    width: 200px;
    background-color: #4158D0;
    background-image: linear-gradient(43deg, #4158D0 0%, #C850C0 46%, #FFCC70 100%);
    color: white;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 移动端菜单按钮 */
.mobile-menu-button {
    display: none;
    font-size: 40px;
    cursor: pointer;
}

/* 抽屉式导航菜单样式 */
.drawer-nav-menu {
    padding: 20px 0;
}

.drawer-divider,
.nav-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 10px 20px;
}

.logo {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.nav-menu {
    flex: 1;
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.nav-item:hover,
.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.nav-item.active {
    border-left: 3px solid #FFCC70;
}

.nav-item .el-icon {
    margin-right: 10px;
    font-size: 18px;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    height: 100%;
}

/* 移动端顶部栏样式 */
.top-bar {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }

    .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        margin-bottom: 20px;
    }

    .mobile-logo {
        display: flex;
        align-items: center;
    }

    .logo-img {
        height: 40px;
    }

    .mobile-controls {
        display: flex;
        align-items: center;
    }

    .mobile-menu-button {
        display: block;
    }
}
