@import './base.css';

#app {
  width: 100%;
  height: 100vh;
  margin: 0 auto;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
  }

  #app {
    display: flex;
    flex-direction: column;
    padding: 0;
  }
}

@media (max-width: 768px) {
  #app {
    padding: 0;
  }

  .main-layout {
    margin-top: 0px !important;
  }
}
