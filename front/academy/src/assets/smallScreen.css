/**
 * 小屏幕设备的额外样式
 */

/* 当设备被标记为小屏幕时应用的样式 */
body.small-screen {
  /* 防止内容溢出 */
  overflow-x: hidden;
}

/* 消息通知组件在小屏幕上的样式调整 */
body.small-screen .header-notification {
  /* 确保消息通知组件不会被截断 */
  max-width: 100%;
  overflow: visible;
}

/* 确保内容在小屏幕上可见 */
body.small-screen .content-area {
  min-width: unset;
  width: 100%;
}

/* 调整消息通知在小屏幕上的显示 */
body.small-screen .message-item {
  width: 100%;
  max-width: 100%;
}

/* 确保文本在小屏幕上不会溢出 */
body.small-screen .message-content,
body.small-screen .message-title {
  word-break: break-word;
  overflow-wrap: break-word;
}
