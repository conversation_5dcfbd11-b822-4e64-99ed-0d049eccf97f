import { createStore } from 'vuex'

export default createStore({
  state: {
    activeNavItem: '', // 当前活动的导航项
    navItems: [
      {
        path: '/academy',
        name: '我的课程',
        icon: 'Monitor',
        divider: false
      },
      {
        path: '/academy/messages',
        name: '消息通知',
        icon: 'ChatDotRound',
        divider: false,
        hasNotification: true
      },
      {
        path: '/academy/promotions',
        name: '幸运抽奖',
        icon: 'Discount',
        divider: false
      },
      {
        path: '/academy/live',
        name: '直播预告',
        icon: 'VideoPlay',
        divider: true
      },
      {
        path: '/academy/about',
        name: '关于匡醍',
        icon: 'InfoFilled',
        divider: false
      }
    ]
  },
  mutations: {
    setActiveNavItem(state, path) {
      state.activeNavItem = path
    }
  },
  actions: {
    updateActiveNavItem({ commit }, path) {
      commit('setActiveNavItem', path)
    }
  },
  getters: {
    getNavItems: state => state.navItems,
    getActiveNavItem: state => state.activeNavItem
  }
})
