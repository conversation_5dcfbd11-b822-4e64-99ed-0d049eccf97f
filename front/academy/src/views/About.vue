<template>
    <AcademyLayout>
        <div class="about-container">
            <MarkdownViewer path="/academy/markdown/about-quantide.md" />
        </div>
    </AcademyLayout>
</template>

<script setup>
import AcademyLayout from '../components/AcademyLayout.vue';
import MarkdownViewer from '../components/MarkdownViewer.vue';
</script>

<style scoped>
.about-container {
    width: 100%;
    padding: 20px;
}

.about-container :deep(.markdown-content) {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .about-container :deep(.markdown-content) {
        padding: 20px;
    }
}
</style>
