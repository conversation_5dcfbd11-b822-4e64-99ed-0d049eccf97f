<template>
    <AcademyLayout>
        <div class="live-streams-view">
            <UnderDevelopment message="直播预告功能正在开发中，您将能够查看即将进行的直播课程并参与互动。敬请期待！" featureName="直播预告" />
        </div>
    </AcademyLayout>
</template>

<script setup>
import AcademyLayout from '../components/AcademyLayout.vue';
import UnderDevelopment from '../components/UnderDevelopment.vue';
</script>

<style scoped>
.live-streams-view {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 70vh;
}
</style>
