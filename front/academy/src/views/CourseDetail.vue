<template>
    <AcademyLayout>
        <div class="course-detail-container">
            <!-- 回退按钮 -->
            <div class="back-button-container">
                <el-button type="primary" @click="goBack" :icon="ArrowLeft">
                    返回
                </el-button>
            </div>

            <!-- Markdown内容 -->
            <div class="markdown-content-container">
                <MarkdownViewer v-if="markdownPath" :path="markdownPath" :auto-load="true" />
                <div v-else class="error-container">
                    <el-alert title="未找到课程详情" type="error" :closable="false" show-icon />
                </div>
            </div>
        </div>
    </AcademyLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import AcademyLayout from '@/components/AcademyLayout.vue'
import MarkdownViewer from '@/components/MarkdownViewer.vue'

const route = useRoute()
const router = useRouter()

// 获取markdown文件名参数
const markdownFile = computed(() => route.params.filename)

// 构建markdown文件路径
const markdownPath = computed(() => {
    if (markdownFile.value) {
        return `/academy/markdown/${markdownFile.value}`
    }
    return null
})

// 回退功能
const goBack = () => {
    // 检查是否有历史记录可以回退
    if (window.history.length > 1) {
        router.go(-1)
    } else {
        // 如果没有历史记录，回到academy主页
        router.push('/academy/academy')
    }
}

// 页面标题设置
onMounted(() => {
    // 可以根据文件名设置页面标题
    if (markdownFile.value) {
        const title = markdownFile.value.replace('.md', '').replace('-', ' ')
        document.title = `${title} - Quant Academy`
    }
})
</script>

<style scoped>
.course-detail-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.back-button-container {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.markdown-content-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.error-container {
    padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .course-detail-container {
        padding: 15px;
    }

    .back-button-container {
        margin-bottom: 15px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .markdown-content-container {
        background: #1a1a1a;
        box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.1);
    }
}
</style>
