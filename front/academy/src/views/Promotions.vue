<template>
  <AcademyLayout>
    <!-- 促销活动内容 -->
    <div class="promotions-section">
      <div class="promotion-page">
        <div class="page-header">
          <h1>Lucky Draw</h1>
        </div>

        <div class="promotion-content">
          <h2 class="section-title">我的抽奖</h2>

          <div class="lottery-cards" v-if="configs.length > 0">
            <el-card v-for="config in configs" :key="config.course" class="lottery-card">
              <div class="lottery-card-content">
                <div class="lottery-image">
                  <img :src="getCourseCoverImage(config.course)" :alt="config.title" />
                </div>
                <div class="lottery-details">
                  <h3>{{ config.title }}</h3>
                  <p class="lottery-prize">最高可得 <span class="prize-amount">{{ config.max }}</span> 元
                  </p>
                  <p class="lottery-times">剩余次数: <span class="times-count">{{ config.remaining_times
                  }}</span></p>
                  <el-button type="primary" class="draw-button" @click="drawSpecificCourse(config.course)"
                    :disabled="config.remaining_times <= 0">
                    立即抽奖
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <div v-else class="no-lottery-cards">
            <el-empty description="暂无可抽奖课程">
              <template #description>
                <p>您已经抽取了所有可用课程的奖券，或者本月抽奖次数已用完</p>
              </template>
            </el-empty>
          </div>

          <div class="lottery-description">
            <h3>活动说明</h3>
            <p>参与抽奖，赢取课程优惠券！抽中的奖券可用于课程购买。</p>
            <p>奖券有效期根据活动设置，过期后将自动失效。您也可以将奖券转让给其他用户，转让后双方都能获得奖券面值一半的奖励。</p>
          </div>

          <el-card class="tickets-card">
            <template #header>
              <div class="card-header">
                <h2>我的奖券</h2>
              </div>
            </template>

            <LotteryTicketList ref="ticketListRef" />
          </el-card>
        </div>

        <!-- 抽奖对话框 -->
        <LotteryDialog v-model:visible="lotteryDialogVisible" :course="selectedCourse" @close="handleLotteryDialogClose"
          @lottery-complete="handleLotteryComplete" />
      </div>
    </div>
  </AcademyLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import AcademyLayout from '../components/AcademyLayout.vue';
import LotteryDialog from '../components/LotteryDialog.vue';
import LotteryTicketList from '../components/LotteryTicketList.vue';
import { getCourseImage } from '../utils/imageManager';

const lotteryDialogVisible = ref(false);
const configs = ref([]);
const loading = ref(false);
const selectedCourse = ref('');
const ticketListRef = ref(null);

// 方法
const loadConfigs = async () => {
  try {
    loading.value = true;

    // 获取抽奖配置（服务器端已过滤掉已有有效奖券的课程）
    const configResponse = await axios.get('/api/academy/lottery/config');
    configs.value = configResponse.data.configs || [];
    console.log('抽奖配置:', configs.value);
  } catch (error) {
    console.error('Failed to load lottery configs:', error);
    ElMessage.error('加载抽奖配置失败');
  } finally {
    loading.value = false;
  }
};

const openLotteryDialog = () => {
  lotteryDialogVisible.value = true;
};

const handleLotteryDialogClose = () => {
  // 重新加载配置
  loadConfigs();
};

const handleLotteryComplete = () => {
  // 抽奖完成后，更新奖券列表
  if (ticketListRef.value) {
    // 调用 loadTickets 方法刷新奖券列表
    ticketListRef.value.loadTickets();
    console.log('已刷新奖券列表');
  } else {
    console.error('无法找到 LotteryTicketList 组件');
  }
};

const drawSpecificCourse = (course) => {
  // 设置特定课程并打开抽奖对话框
  selectedCourse.value = course;

  // 检查课程是否有抽奖次数
  const config = configs.value.find(c => c.course === course);
  if (config && config.remaining_times <= 0) {
    ElMessage.warning('您的抽奖次数已用完');
    return;
  }

  // 直接打开抽奖对话框
  // 注意：服务器端已经过滤掉了已有有效奖券的课程，所以这里不需要再次检查
  openLotteryDialog();
};

const getCourseCoverImage = (course) => {
  // 使用图片管理函数获取课程封面图
  return getCourseImage(course, 1000);
};

// 生命周期钩子
onMounted(() => {
  loadConfigs();
});
</script>

<style scoped>
/* 促销活动页面样式 */
.promotions-section {
  padding: 20px;
  min-height: 70vh;
}

.promotion-page {
  width: 100%;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 24px;
  color: #303133;
}

.promotion-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 20px;
  color: #303133;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.lottery-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.lottery-card {
  width: calc(33.33% - 14px);
  transition: transform 0.3s;
}

.lottery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.lottery-card-content {
  display: flex;
  flex-direction: column;
}

.lottery-image {
  height: 160px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 10px;
}

.lottery-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.lottery-card:hover .lottery-image img {
  transform: scale(1.05);
}

.lottery-details {
  padding: 10px 0;
}

.lottery-details h3 {
  font-size: 18px;
  margin: 0 0 10px 0;
  color: #303133;
}

.lottery-prize {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.prize-amount {
  font-size: 18px;
  color: #F56C6C;
  font-weight: bold;
}

.lottery-times {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.times-count {
  font-weight: bold;
  color: #409EFF;
}

.draw-button {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.lottery-description {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.lottery-description h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #409EFF;
}

.lottery-description p {
  margin: 5px 0;
  color: #606266;
  line-height: 1.5;
}

.no-lottery-cards {
  background-color: #f5f7fa;
  padding: 30px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

.no-lottery-cards p {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 14px;
}

@media (max-width: 1200px) {
  .lottery-card {
    width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .promotions-section {
    padding: 10px;
  }

  .lottery-card {
    width: 100%;
  }

  .lottery-image {
    height: 120px;
  }
}
</style>
