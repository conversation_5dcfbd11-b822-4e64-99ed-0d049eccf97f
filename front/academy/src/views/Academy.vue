<!-- src/views/Academy.vue -->
<template>
    <AcademyLayout>
        <!-- 课程内容区域 -->
        <div class="courses-section">
            <!-- 加载中提示 -->
            <div v-if="loading" class="loading-container">
                <el-skeleton :rows="5" animated />
            </div>

            <!-- 错误提示 -->
            <el-alert v-if="error" :title="error" type="error" :closable="false" show-icon />

            <!-- 课程内容 -->
            <div v-if="!loading && !error">
                <!-- 我的课程 -->
                <div class="section-header">
                    <h2>我的课程</h2>
                    <div class="see-more" @click="seeMoreCourses">
                        <span>查看更多</span>
                        <el-icon>
                            <ArrowRight />
                        </el-icon>
                    </div>
                </div>

                <div class="course-cards">
                    <CourseCard v-for="course in ongoing" :key="index" :title="course.title" :level="course.plan"
                        :image="course.image" :chap="course.chap" :course-id="course.courseId" :account="course.account"
                        :start-time="course.startTime" :end-time="course.endTime" :expire-time="course.expireTime"
                        :refund-end-time="course.refundEndTime" :accessible-chapters="course.accessibleChapters"
                        :assignments="course.assignments" :courseware="course.courseware" />
                </div>

                <!-- 推荐课程 -->
                <div class="section-header">
                    <h2>推荐课程</h2>
                    <div class="view-all" @click="viewAllCourses">
                        <span>全部查看</span>
                    </div>
                </div>

                <div class="recommended-courses">
                    <RecommendedCourseCard v-for="course in recommendedCourses" :key="course.id" :id="course.id"
                        :name="course.name" :price="course.price" :image="course.image" :level="course.level"
                        :desc="course.desc" :course-id="course.courseId" :chapters="course.chapters"
                        :duration="course.duration" :buy-link="course.buyLink" :detail="course.detail" />
                </div>

                <!-- 已完结课程 -->
                <div class="section-header">
                    <h2>已完结课程</h2>
                    <div class="view-all" @click="viewAllCompletedCourses">
                        <span>查看全部</span>
                    </div>
                </div>

                <div class="completed-courses">
                    <div v-if="completedCourses.length === 0" class="no-completed-courses">
                        您目前没有已完结的课程。
                    </div>
                    <el-table v-else :data="completedCourses" style="width: 100%">
                        <el-table-column label="课程名称">
                            <template #default="scope">
                                <span>{{ scope.row.title || scope.row.name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="到期时间" width="180">
                            <template #default="scope">
                                <span>{{ formatDate(scope.row.expireTime) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" width="150">
                            <template #default="scope">
                                <el-tag v-if="scope.row.isExpired" type="info">已完结</el-tag>
                                <el-tag v-else type="success">共享服务器访问</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150">
                            <template #default="scope">
                                <a v-if="!scope.row.isExpired" :href="scope.row.url" target="_blank"
                                    class="course-link">
                                    <span class="desktop-text">访问课程</span>
                                    <span class="mobile-text">访问</span>
                                </a>
                                <span v-else class="expired-text">-</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </AcademyLayout>
</template>

<script setup>
import CourseCard from '@/components/CourseCard.vue' // 引入 CourseCard 组件
import RecommendedCourseCard from '@/components/RecommendedCourseCard.vue' // 引入 RecommendedCourseCard 组件
import { ArrowRight } from '@element-plus/icons-vue'
import AcademyLayout from '../components/AcademyLayout.vue'
import { computed, onMounted, ref } from 'vue'
import axios from '../router/axios'
import { initAutoScale } from '../utils/autoScale'

// 移除不需要的变量

// 课程数据
const ongoing = ref([])
const recommendedCourses = ref([])
const completedCourses = ref([])
const loading = ref(true)
const error = ref(null)

// 检查用户是否已登录
const customerId = computed(() => localStorage.getItem('customer'))

// 加载数据
const loadAcademyData = async () => {
    try {
        loading.value = true
        error.value = null

        const customerId = localStorage.getItem('customer')

        console.log('Using customer ID:', customerId)

        // 获取主页数据
        const homeResponse = await axios.get(`/api/academy/home/<USER>
        console.log('Academy data response:', homeResponse.data)

        // 更新数据
        ongoing.value = homeResponse.data.ongoing
        recommendedCourses.value = homeResponse.data.recommendedCourses

        completedCourses.value = homeResponse.data.completedCourses
    } catch (err) {
        console.error('Failed to load academy data:', err)
        error.value = '加载数据失败，请稍后重试'

        // 加载失败时使用默认数据
        ongoing.value = [
            {
                title: '量化24课',
                plan: 'bronze',
                image: '', // 使用计算属性中的图片
                chap: Array.from(
                    {
                        length: 20,
                    },
                    (_, index) => index + 1,
                ).map((number) => number.toString().padStart(2, '0')),
                courseId: 'l24',
                account: 'quantide',
                startTime: new Date().toISOString(),
                endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                expireTime: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
                accessibleChapters: [1, 2, 3, 4, 5],
            },
            {
                title: '因子分析与机器学习策略',
                plan: 'platinum',
                image: '', // 使用计算属性中的图片
                chap: Array.from(
                    {
                        length: 15,
                    },
                    (_, index) => index + 1,
                ).map((number) => number.toString().padStart(2, '0')),
                courseId: 'fa',
                account: 'quantide',
                startTime: new Date().toISOString(),
                endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                expireTime: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
                accessibleChapters: [1, 2, 3],
            },
        ]

        recommendedCourses.value = [
            {
                id: 1,
                name: 'Graphic Design',
                rating: 4,
                instructor: 'Jane Doe',
                price: '5,000',
            },
            {
                id: 2,
                name: 'Graphic Design',
                rating: 4,
                instructor: 'Jane Doe',
                price: '5,000',
            },
            {
                id: 3,
                name: 'Graphic Design',
                rating: 4,
                instructor: 'Jane Doe',
                price: '5,000',
            },
            {
                id: 4,
                name: 'Graphic Design',
                rating: 4,
                instructor: 'Jane Doe',
                price: '5,000',
            },
            {
                id: 5,
                name: 'Graphic Design',
                rating: 4,
                instructor: 'Jane Doe',
                price: '5,000',
            },
        ]

        // 测试数据：包括一个已过期的课程和一个未过期的课程
        completedCourses.value = [
            {
                id: 1,
                title: '量化人的Numpy&Pandas',
                courseId: 'pandas',
                expireTime: new Date(2024, 3, 26).toISOString(), // 已过期（4月26日）
                account: 'quantide',
                isExpired: true,
            },
            {
                id: 2,
                title: '量化24课',
                courseId: 'l24',
                expireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 未过期
                account: 'quantide',
                isExpired: false,
            },
        ]
    } finally {
        loading.value = false
    }
}

// 页面加载时获取数据并初始化自动缩放
onMounted(() => {
    loadAcademyData()
    initAutoScale() // 初始化自动缩放功能
})

// 方法
const seeMoreCourses = () => {
    console.log('See more courses clicked')
}

const viewAllCourses = () => {
    console.log('View all courses clicked')
}

const viewAllCompletedCourses = () => {
    // 暂时不做任何操作，因为所有已完结课程都已经显示在当前页面
    console.log('View all completed courses clicked')

    // 滚动到已完结课程部分
    const completedCoursesSection = document.querySelector('.completed-courses')
    if (completedCoursesSection) {
        completedCoursesSection.scrollIntoView({
            behavior: 'smooth',
        })
    }
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    })
}
</script>

<style scoped>
.academy-container {
    display: flex;
    min-height: 100%;
    /* 使用百分比高度，适应父容器 */
    background-color: #f8f9fa;
    background-image: linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%);
    overflow: hidden;
    /* 防止容器本身滚动 */
    margin-top: -70px;
    /* 抵消 main-layout 的 margin-top */
    padding-top: 70px;
    /* 保持内容不被 header 遮挡 */
}

/* 侧边栏样式 */
.sidebar {
    width: 200px;
    background-color: #4158d0;
    background-image: linear-gradient(43deg, #4158d0 0%, #c850c0 46%, #ffcc70 100%);
    color: white;
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 移动端菜单按钮 */
.mobile-menu-button {
    display: none;
    font-size: 40px;
    cursor: pointer;
}

/* 抽屉式导航菜单样式 */
.drawer-nav-menu {
    padding: 20px 0;
}

.drawer-divider,
.nav-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 10px 20px;
}

.nav-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.logo {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.nav-menu {
    flex: 1;
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.nav-item:hover,
.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.nav-item.active {
    border-left: 3px solid #ffcc70;
}

.nav-item .el-icon {
    margin-right: 10px;
    font-size: 18px;
}

.sidebar-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    /* 允许内容区域垂直滚动 */
    height: 100%;
    /* 确保高度填满父容器 */
}

/* PC端顶部栏样式（简化） */
.pc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 移动端顶部栏样式 */
.top-bar {
    display: none;
}

.search-bar {
    width: 300px;
}

.user-info {
    display: flex;
    align-items: center;
    background-color: white;
    padding: 8px 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.user-details {
    margin: 0 10px;
}

.user-name {
    font-weight: 600;
}

.user-role {
    font-size: 12px;
    color: #909399;
}

.more-icon {
    cursor: pointer;
}

/* 课程部分样式 */
.courses-section {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-sizing: border-box;
    padding: clamp(10px, 2vw, 20px);
}

/* 缩放效果已移至 autoScale.ts 中通过 JavaScript 动态处理 */

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: clamp(15px, 3vw, 20px);
}

.section-header h2 {
    margin: 0;
    font-size: clamp(16px, 2.5vw, 20px);
}

.see-more,
.view-all {
    display: flex;
    align-items: center;
    color: #c850c0;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    font-size: clamp(12px, 1.5vw, 14px);
}

.see-more:hover,
.view-all:hover {
    transform: translateX(5px);
    color: #4158d0;
}

.see-more .el-icon,
.view-all .el-icon {
    margin-left: 5px;
}

/* 课程卡片样式 */
.course-cards,
.recommended-courses {
    display: block;
    margin-bottom: 40px;
    padding-bottom: 10px;
}

.course-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    width: 100%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    margin-bottom: 20px;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    border-color: #ffcc70;
}

.course-content {
    display: flex;
    flex-direction: column;
}

.course-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.course-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

.course-title {
    font-weight: 600;
}

.course-level {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.course-level.advanced {
    background-color: #ff4b91;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(255, 75, 145, 0.3);
}

.course-level.intermediate {
    background-color: #ff9843;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(255, 152, 67, 0.3);
}

.course-level.beginner {
    background-color: #4bc0c0;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(75, 192, 192, 0.3);
}

.course-progress {
    display: flex;
    justify-content: space-between;
    padding: 0 15px 15px;
}

.progress-text {
    color: #909399;
    font-size: 14px;
}

.chapter-list {
    display: flex;
    flex-wrap: wrap;
    width: 75%;
    margin: 10px 0;
}

.chapter-link {
    margin: 5px;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    transition:
        background-color 0.3s,
        color 0.3s;
}

.chapter-link:hover {
    background-color: #e9ecef;
    color: #007bff;
}

/* 表格样式 */
.recommended-courses,
.completed-courses {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 40px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    border-top: 4px solid #ffcc70;
    transition: all 0.3s ease;
}

.recommended-courses:hover,
.completed-courses:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.course-name {
    color: #c850c0;
    font-weight: 600;
    transition: all 0.3s ease;
}

.course-name:hover {
    color: #4158d0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .academy-container {
        flex-direction: column;
    }

    .sidebar {
        display: none;
        /* 在小屏幕上隐藏侧边栏 */
    }

    .pc-header {
        display: none;
        /* 在小屏幕上隐藏PC端顶部栏 */
    }

    .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .mobile-menu-button {
        display: flex;
        /* 在小屏幕上显示菜单按钮 */
        font-size: 24px;
        cursor: pointer;
        align-items: center;
        justify-content: center;
    }

    .main-content {
        width: 100%;
        padding: 15px;
    }

    .top-bar {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .search-bar {
        display: none;
        /* 移动端隐藏搜索框 */
    }

    .user-info {
        display: none;
        /* 隐藏原来的用户信息 */
    }

    .mobile-logo {
        display: flex;
        align-items: center;
        height: 40px;
        width: 40px;
        justify-content: center;
    }

    .mobile-logo .logo {
        height: 36px;
        width: 36px;
        object-fit: contain;
    }

    .mobile-logo .logo-img {
        width: 100%;
        object-fit: contain;
    }

    .mobile-controls {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .mobile-user-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        width: 40px;
    }

    .mobile-user-avatar .el-avatar {
        height: 40px;
        width: 40px;
    }

    .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        height: 50px;
    }

    .mobile-menu-button {
        display: flex;
        cursor: pointer;
        align-items: center;
        justify-content: center;
        height: 40px;
        width: 40px;
    }

    .mobile-menu-button .el-icon {
        font-size: 36px;
    }

    /* 移动端表格样式 */
    .el-table {
        width: 100% !important;
        font-size: clamp(12px, 1.5vw, 14px);
    }

    .el-table .cell {
        padding-left: clamp(3px, 1vw, 5px);
        padding-right: clamp(3px, 1vw, 5px);
    }

    /* 移动端课程卡片样式 - 使用视口单位 */
    .course-link {
        max-width: clamp(80px, 15vw, 100px);
    }
}

.loading-container {
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

/* 导航条中的消息通知图标 */
.message-nav-item {
    display: flex;
    align-items: center;
    position: relative;
}

.nav-notification {
    margin-left: 5px;
}

.drawer-notification {
    margin-left: 5px;
}

/* 移除不需要的响应式样式 */

/* 已完结课程样式 */
.completed-courses {
    width: 100%;
    overflow-x: auto;
    box-sizing: border-box;
}

/* 设备特定样式已移至 autoScale.ts 中通过 JavaScript 动态处理 */
.device-xs .completed-courses .el-table,
.device-md .completed-courses .el-table {
    font-size: 11px;
}

.device-xs .completed-courses .el-table th,
.device-md .completed-courses .el-table th {
    padding: 8px 0;
}

.device-xs .completed-courses .el-table td,
.device-md .completed-courses .el-table td {
    padding: 6px 0;
}

.no-completed-courses,
.all-expired-courses {
    padding: 20px;
    text-align: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #909399;
    margin-bottom: 20px;
}

.all-expired-courses {
    background-color: #fef0f0;
    color: #f56c6c;
}

.expired-text {
    color: #909399;
    font-size: 14px;
    font-style: italic;
}

.course-link {
    color: #409eff;
    font-size: clamp(12px, 1.5vw, 14px);
    text-decoration: none;
    word-break: break-all;
    display: block;
    max-width: clamp(120px, 20vw, 150px);
    overflow: hidden;
    text-overflow: ellipsis;
}

.course-link:hover {
    color: #66b1ff;
    text-decoration: underline;
}

.mobile-text {
    display: none;
}

@media (max-width: 768px) {
    .desktop-text {
        display: none;
    }

    .mobile-text {
        display: inline;
    }
}
</style>
