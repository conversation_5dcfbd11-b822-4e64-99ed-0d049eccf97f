<template>
  <div class="login-container">
    <div class="login-panel" :class="{ 'panel-swap': isRightPanel }">
      <!-- 左侧面板 - 表单 -->
      <div class="panel-half panel-form">
        <!-- 登录表单 -->
        <div v-if="activeTab === 'login'" class="form-container">
          <h2 class="form-title">欢迎回来！</h2>
          <p class="form-subtitle">请登录您的账号继续访问</p>

          <form @submit.prevent="handleLogin">
            <div class="form-group">
              <label for="username">用户名</label>
              <input type="text" id="username" v-model="username" required placeholder="输入你的用户名" />
            </div>
            <div class="form-group">
              <label for="password">登录密码</label>
              <input type="password" id="password" v-model="password" required placeholder="请输入密码" />
            </div>
            <div class="error-message" v-if="errorMessage">{{ errorMessage }}</div>
            <div class="button-group-joined">
              <button type="submit" :disabled="isLoading" class="primary-button button-left"
                :style="{ backgroundColor: currentCourseColor }">
                {{ isLoading ? '正在登录...' : '登录' }}
              </button>
              <button type="button" class="secondary-button button-right" @click="togglePanel">
                注册
              </button>
            </div>
          </form>

          <!-- 底部信息区域 -->
          <div class="footer-content">
            <div class="footer-row">
              <div class="logo">
                <img src="@/assets/logo.png" alt="QuantIDE Logo" />
              </div>
              <div class="qr-code">
                <img src="@/assets/quantfans.jpg" alt="QuantFans QR Code" />
              </div>
              <div class="slogan">{{ randomSlogan }}</div>
            </div>
          </div>
        </div>

        <!-- 注册表单 -->
        <div v-if="activeTab === 'register'" class="form-container">
          <!-- 只在第一步显示标题和副标题 -->
          <div v-if="registerStep === 1">
            <h2 class="form-title">创建账号</h2>
            <p class="form-subtitle">加入我们，开始您的学习之旅</p>
          </div>

          <form @submit.prevent="handleRegisterSubmit">
            <!-- 第一步：基本信息 -->
            <div v-if="registerStep === 1">
              <div class="form-group">
                <label for="reg-account">用户名</label>
                <input type="text" id="reg-account" v-model="registerForm.account" required
                  placeholder="4-20个字符，仅限字母和数字" @input="validateAccount" />
                <div class="field-hint" v-if="registerForm.accountError">{{ registerForm.accountError }}
                </div>
              </div>
              <div class="form-group">
                <label for="reg-password">密码</label>
                <input type="password" id="reg-password" v-model="registerForm.password" required
                  placeholder="至少8个字符，包含大小写字母、数字和特殊符号" @input="validatePassword" />
                <div class="field-hint" v-if="registerForm.passwordError">{{ registerForm.passwordError
                  }}</div>
              </div>
              <div class="form-group">
                <label for="reg-confirm-password">确认密码</label>
                <input type="password" id="reg-confirm-password" v-model="registerForm.confirmPassword" required
                  placeholder="再次输入密码" @input="validateConfirmPassword" />
                <div class="field-hint" v-if="registerForm.confirmPasswordError">{{
      registerForm.confirmPasswordError
    }}
                </div>
              </div>

              <button type="button" @click="goToNextStep" :disabled="!canGoToNextStep" class="primary-button"
                :style="{ backgroundColor: currentCourseColor }">
                下一步
              </button>
            </div>

            <!-- 第二步：联系方式验证 -->
            <div v-if="registerStep === 2">
              <div class="form-group">
                <label for="reg-nickname">昵称</label>
                <input type="text" id="reg-nickname" v-model="registerForm.nickname" required placeholder="您希望显示的名称" />
              </div>
              <div class="form-group">
                <div class="label-with-info">
                  <label for="reg-wx">微信ID</label>
                  <div class="info-icon" title="提供微信id，以便我们在紧急情况能够通知到您">
                    <span>i</span>
                  </div>
                </div>
                <input type="text" id="reg-wx" v-model="registerForm.wx" required placeholder="您的微信ID" />
              </div>
              <div class="form-group">
                <label for="reg-phone">手机号</label>
                <div class="phone-verification">
                  <input type="text" id="reg-phone" v-model="registerForm.phone" required placeholder="11位手机号码"
                    :disabled="registerForm.codeSent" />
                  <button type="button" class="verification-button" @click="showCaptchaVerification"
                    :disabled="isVerificationLoading || !isPhoneValid || registerForm.codeSent"
                    :style="{ backgroundColor: currentCourseColor }">
                    {{ getVerificationButtonText() }}
                  </button>
                </div>
                <div class="field-hint" v-if="registerForm.phoneError">{{ registerForm.phoneError }}
                </div>
              </div>
              <div class="form-group" v-if="registerForm.codeSent">
                <label for="reg-verification-code">验证码</label>
                <input type="text" id="reg-verification-code" v-model="registerForm.verificationCode" required
                  placeholder="6位验证码" />
                <div class="field-hint" v-if="registerForm.verificationCodeError">{{
      registerForm.verificationCodeError
    }}</div>
              </div>

              <div class="button-group">
                <button type="button" class="secondary-button" @click="goToPreviousStep">
                  返回
                </button>
                <button type="submit" :disabled="isRegisterLoading || !canRegister" class="primary-button"
                  :style="{ backgroundColor: currentCourseColor }">
                  {{ isRegisterLoading ? '正在注册...' : '注册' }}
                </button>
              </div>

              <!-- 只在第二步显示底部信息区域 -->
              <div class="footer-content">
                <div class="footer-row">
                  <div class="logo">
                    <img src="@/assets/logo.png" alt="QuantIDE Logo" />
                  </div>
                  <div class="qr-code">
                    <img src="@/assets/quantfans.jpg" alt="QuantFans QR Code" />
                  </div>
                  <div class="slogan">{{ randomSlogan }}</div>
                </div>
              </div>
            </div>

            <div class="error-message" v-if="registerError">{{ registerError }}</div>
          </form>
        </div>
      </div>

      <!-- 右侧面板 - 图片 -->
      <div class="panel-half panel-image">
        <img :src="randomImage" alt="Course Image" class="background-image" />
      </div>
    </div>

    <!-- CAPTCHA Verification Dialog -->
    <div class="captcha-dialog" v-if="showCaptcha">
      <div class="captcha-content">
        <h3>人机验证</h3>
        <p>请输入下方图片中的验证码</p>
        <div class="captcha-image">
          <img :src="captchaImage" alt="CAPTCHA" />
          <button type="button" class="refresh-captcha" @click="refreshCaptcha">
            ↻
          </button>
        </div>
        <div class="captcha-input">
          <input type="text" v-model="captchaCode" placeholder="请输入验证码" />
        </div>
        <div class="captcha-error" v-if="captchaError">{{ captchaError }}</div>
        <div class="captcha-buttons">
          <button type="button" @click="cancelCaptcha">取消</button>
          <button type="button" @click="verifyCaptcha" :disabled="!captchaCode"
            :style="{ backgroundColor: currentCourseColor }">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import l24Image from '@/assets/24lectures_1000.png';
import faImage from '@/assets/fa_1000.png';
import pandasImage from '@/assets/pandas_1000.png';
// 导入横向图片
import l24LandscapeImage from '@/assets/l24-landscape.png';
import faLandscapeImage from '@/assets/fa-landscape.png';
import pandasLandscapeImage from '@/assets/pandas-landscape.png';
import sloganFile from '@/assets/slogan.md?raw';
import {
  useRouter
} from 'vue-router';

export default {
  name: 'LoginView',
  data() {
    return {
      activeTab: 'login',
      isRightPanel: false, // 控制面板位置
      registerStep: 1, // 注册步骤，1表示第一步，2表示第二步
      username: '',
      password: '',
      errorMessage: '',
      isLoading: false,
      registerError: '',
      isRegisterLoading: false,
      isVerificationLoading: false,
      verificationCountdown: 0,
      verificationTimer: null,
      showCaptcha: false,
      captchaId: '',
      captchaCode: '',
      captchaImage: '',
      captchaError: '',
      registerForm: {
        account: '',
        nickname: '',
        password: '',
        confirmPassword: '',
        wx: '',
        phone: '',
        verificationCode: '',
        codeSent: false,
        accountError: '',
        passwordError: '',
        confirmPasswordError: '',
        phoneError: '',
        verificationCodeError: ''
      },
      slogans: this.parseSlogans(sloganFile),
      imageMap: {
        '24lecture': l24Image,
        'fa': faImage,
        'pandas': pandasImage
      },
      // 横向图片映射
      landscapeImageMap: {
        '24lecture': l24LandscapeImage,
        'fa': faLandscapeImage,
        'pandas': pandasLandscapeImage
      },
      // 课程颜色映射
      courseColors: {
        '24lecture': '#A03938',
        'fa': '#E0CD66',
        'pandas': '#67AD5B'
      },
      currentCourse: null
    }
  },
  computed: {
    // 随机选择一张图片，在移动端使用横向图片
    randomImage() {
      const courses = Object.keys(this.imageMap);
      if (!this.currentCourse) {
        const randomIndex = Math.floor(Math.random() * courses.length);
        this.currentCourse = courses[randomIndex];
      }

      // 检测是否为移动设备
      const isMobile = window.innerWidth <= 768;

      // 在移动设备上使用横向图片
      if (isMobile) {
        return this.landscapeImageMap[this.currentCourse];
      }

      // 在桌面设备上使用普通图片
      return this.imageMap[this.currentCourse];
    },
    // 随机选择一条slogan
    randomSlogan() {
      if (this.slogans.length === 0) {
        return 'Grow with QuantIDE';
      }
      const randomIndex = Math.floor(Math.random() * this.slogans.length);
      return this.slogans[randomIndex];
    },
    // 验证手机号是否有效
    isPhoneValid() {
      return /^1\d{10}$/.test(this.registerForm.phone);
    },
    // 判断是否可以进入下一步
    canGoToNextStep() {
      return (
        this.registerForm.account &&
        this.registerForm.password &&
        this.registerForm.confirmPassword &&
        !this.registerForm.accountError &&
        !this.registerForm.passwordError &&
        !this.registerForm.confirmPasswordError
      );
    },
    // 判断是否可以注册
    canRegister() {
      return (
        this.registerForm.account &&
        this.registerForm.nickname &&
        this.registerForm.password &&
        this.registerForm.confirmPassword &&
        this.registerForm.wx &&
        this.registerForm.phone &&
        this.registerForm.codeSent &&
        this.registerForm.verificationCode &&
        !this.registerForm.accountError &&
        !this.registerForm.passwordError &&
        !this.registerForm.confirmPasswordError &&
        !this.registerForm.phoneError &&
        !this.registerForm.verificationCodeError
      );
    },
    // 获取当前课程的颜色
    currentCourseColor() {
      return this.currentCourse ? this.courseColors[this.currentCourse] : '#008080';
    }
  },
  setup() {
    const router = useRouter();
    return {
      router
    };
  },
  methods: {
    // 切换面板（登录/注册）
    togglePanel() {
      // 准备新的表单类型
      const newTab = this.activeTab === 'login' ? 'register' : 'login';

      // 检测是否为移动设备
      const isMobile = window.innerWidth <= 768;

      // 在移动设备上立即切换表单内容，不需要等待动画
      if (isMobile) {
        this.activeTab = newTab;
        this.resetForm(newTab);
        return;
      }

      // PC端直接切换面板位置状态，让 left 属性变化触发单一动画
      this.isRightPanel = !this.isRightPanel;

      // 延迟切换表单内容，等待动画完成
      setTimeout(() => {
        // 切换到新的表单类型
        this.activeTab = newTab;
        this.resetForm(newTab);
      }, 250); // 动画持续时间，0.5秒
    },

    // 重置表单
    resetForm(formType) {
      if (formType === 'login') {
        this.username = '';
        this.password = '';
        this.errorMessage = '';
      } else {
        this.registerStep = 1;
        this.registerForm = {
          account: '',
          nickname: '',
          password: '',
          confirmPassword: '',
          wx: '',
          phone: '',
          verificationCode: '',
          codeSent: false,
          accountError: '',
          passwordError: '',
          confirmPasswordError: '',
          phoneError: '',
          verificationCodeError: ''
        };
        this.registerError = '';
      }
    },

    // 解析slogan文件
    parseSlogans(sloganText) {
      return sloganText.split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.trim());
    },

    // 获取验证码按钮文本
    getVerificationButtonText() {
      if (this.isVerificationLoading) {
        return '发送中...';
      }
      if (this.verificationCountdown > 0) {
        return `${this.verificationCountdown}秒后重试`;
      }
      return '获取验证码';
    },

    // 验证账号格式
    validateAccount() {
      // 如果账号为空，清除错误信息
      if (!this.registerForm.account) {
        this.registerForm.accountError = '';
        return false;
      }

      // 验证账号格式
      if (!/^[a-zA-Z0-9]{4,20}$/.test(this.registerForm.account)) {
        this.registerForm.accountError = '账号必须是4-20个字符，且只能包含字母和数字';
        return false;
      }

      // 验证通过，清除错误信息
      this.registerForm.accountError = '';
      return true;
    },

    // 验证密码强度
    validatePassword() {
      // 如果密码为空，清除错误信息
      if (!this.registerForm.password) {
        this.registerForm.passwordError = '';
        return false;
      }

      // 验证密码长度
      if (this.registerForm.password.length < 8) {
        this.registerForm.passwordError = '密码长度至少为8个字符';
        return false;
      }

      // 验证是否包含大写字母
      if (!/[A-Z]/.test(this.registerForm.password)) {
        this.registerForm.passwordError = '密码必须包含至少一个大写字母';
        return false;
      }

      // 验证是否包含小写字母
      if (!/[a-z]/.test(this.registerForm.password)) {
        this.registerForm.passwordError = '密码必须包含至少一个小写字母';
        return false;
      }

      // 验证是否包含数字
      if (!/\d/.test(this.registerForm.password)) {
        this.registerForm.passwordError = '密码必须包含至少一个数字';
        return false;
      }

      // 验证是否包含特殊字符
      if (!/[!@#$%^&*(),.?":{}|<>]/.test(this.registerForm.password)) {
        this.registerForm.passwordError = '密码必须包含至少一个特殊字符';
        return false;
      }

      // 验证通过，清除错误信息
      this.registerForm.passwordError = '';

      // 如果确认密码已经输入，则同时验证确认密码
      if (this.registerForm.confirmPassword) {
        this.validateConfirmPassword();
      }

      return true;
    },

    // 验证确认密码
    validateConfirmPassword() {
      // 如果确认密码为空，清除错误信息
      if (!this.registerForm.confirmPassword) {
        this.registerForm.confirmPasswordError = '';
        return false;
      }

      // 验证两次输入的密码是否一致
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.registerForm.confirmPasswordError = '两次输入的密码不一致';
        return false;
      }

      // 验证通过，清除错误信息
      this.registerForm.confirmPasswordError = '';
      return true;
    },

    // 验证手机号
    validatePhone() {
      if (!this.registerForm.phone) {
        this.registerForm.phoneError = '';
        return false;
      }

      if (!/^1\d{10}$/.test(this.registerForm.phone)) {
        this.registerForm.phoneError = '请输入有效的11位手机号码';
        return false;
      }

      this.registerForm.phoneError = '';
      return true;
    },

    // 显示CAPTCHA验证对话框
    showCaptchaVerification() {
      if (!this.validatePhone()) {
        return;
      }

      this.getCaptcha();
      this.showCaptcha = true;
    },

    // 获取CAPTCHA图片
    async getCaptcha(retryCount = 0) {
      this.captchaError = '';
      this.captchaImage = ''; // 清空之前的图片

      // 显示加载状态
      this.captchaImage = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMiAzMiIgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjMDA4MDgwIj4NCiAgPHBhdGggb3BhY2l0eT0iLjI1IiBkPSJNMTYgMCBBMTYgMTYgMCAwIDAgMTYgMzIgQTE2IDE2IDAgMCAwIDE2IDAgTTE2IDQgQTEyIDEyIDAgMCAxIDE2IDI4IEExMiAxMiAwIDAgMSAxNiA0Ii8+DQogIDxwYXRoIGQ9Ik0xNiAwIEExNiAxNiAwIDAgMSAzMiAxNiBMMjggMTYgQTEyIDEyIDAgMCAwIDE2IDR6Ij4NCiAgICA8YW5pbWF0ZVRyYW5zZm9ybSBhdHRyaWJ1dGVOYW1lPSJ0cmFuc2Zvcm0iIHR5cGU9InJvdGF0ZSIgZnJvbT0iMCAxNiAxNiIgdG89IjM2MCAxNiAxNiIgZHVyPSIwLjhzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgLz4NCiAgPC9wYXRoPg0KPC9zdmc+';

      console.log('Getting CAPTCHA, retry count:', retryCount);

      try {
        // 尝试使用测试验证码
        if (retryCount >= 2) {
          console.log('Trying test CAPTCHA');
          this.captchaId = 'test_captcha_id';
          this.captchaCode = '';
          this.captchaError = '使用测试验证码，请输入"TEST"';
          this.captchaImage = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iNDAiPg0KICA8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjlmOWY5IiAvPg0KICA8dGV4dCB4PSI2MCIgeT0iMjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iIzAwODA4MCI+VEVTVDwvdGV4dD4NCjwvc3ZnPg==';
          return;
        }

        const response = await fetch('/sso/customer/get_captcha', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();
        console.log('CAPTCHA response:', data);

        if (!response.ok) {
          throw new Error(data.error || '获取验证码失败');
        }

        this.captchaId = data.captcha_id;
        this.captchaImage = data.captcha_image;
        this.captchaCode = '';
        this.captchaError = '';
        console.log('CAPTCHA ID set to:', this.captchaId);

      } catch (error) {
        console.error('CAPTCHA generation error:', error);

        // 如果失败次数小于3，则自动重试
        if (retryCount < 3) {
          this.captchaError = `正在重试获取验证码 (${retryCount + 1}/3)...`;
          setTimeout(() => {
            this.getCaptcha(retryCount + 1);
          }, 1000);
        } else {
          this.captchaError = '验证码生成失败，请点击刷新按钮重试';
          this.captchaImage = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMjAgNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iNDAiPg0KICA8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjlmOWY5IiAvPg0KICA8dGV4dCB4PSI2MCIgeT0iMjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iI2RjMzU0NSI+5Yib5bu65Zu+54mH5aSx6LSlPC90ZXh0Pg0KPC9zdmc+';
        }
      }
    },

    // 刷新CAPTCHA
    refreshCaptcha() {
      this.getCaptcha();
    },

    // 取消CAPTCHA验证
    cancelCaptcha() {
      this.showCaptcha = false;
      this.captchaId = '';
      this.captchaCode = '';
      this.captchaImage = '';
      this.captchaError = '';
    },

    // 验证CAPTCHA
    async verifyCaptcha() {
      if (!this.captchaCode) {
        this.captchaError = '请输入验证码';
        return;
      }

      try {
        console.log('Verifying CAPTCHA with ID:', this.captchaId, 'Code:', this.captchaCode);

        const response = await fetch('/sso/customer/verify_captcha', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            captcha_id: this.captchaId,
            captcha_code: this.captchaCode
          })
        });

        const data = await response.json();
        console.log('CAPTCHA verification response:', data);

        if (!response.ok) {
          throw new Error(data.error || '验证码验证失败');
        }

        // CAPTCHA验证成功，发送短信验证码
        this.showCaptcha = false;
        this.sendVerificationCode();

      } catch (error) {
        console.error('CAPTCHA verification error:', error);
        this.captchaError = error.message;

        // 如果验证失败，自动刷新验证码
        setTimeout(() => {
          this.refreshCaptcha();
        }, 1500);
      }
    },

    // 发送验证码
    async sendVerificationCode() {
      if (!this.validatePhone()) {
        return;
      }

      this.isVerificationLoading = true;

      try {
        const response = await fetch('/sso/customer/send_verification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            phone: this.registerForm.phone,
            captcha_id: this.captchaId,
            captcha_code: this.captchaCode
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '发送验证码失败');
        }

        // 验证码发送成功
        this.registerForm.codeSent = true;
        this.startVerificationCountdown();

      } catch (error) {
        this.registerForm.phoneError = error.message;
      } finally {
        this.isVerificationLoading = false;
      }
    },

    // 开始验证码倒计时
    startVerificationCountdown() {
      this.verificationCountdown = 60;

      if (this.verificationTimer) {
        clearInterval(this.verificationTimer);
      }

      this.verificationTimer = setInterval(() => {
        if (this.verificationCountdown > 0) {
          this.verificationCountdown--;
        } else {
          clearInterval(this.verificationTimer);
          this.verificationTimer = null;
        }
      }, 1000);
    },

    // 验证验证码
    async verifyCode() {
      if (!this.registerForm.verificationCode) {
        this.registerForm.verificationCodeError = '请输入验证码';
        return false;
      }

      if (!/^\d{6}$/.test(this.registerForm.verificationCode)) {
        this.registerForm.verificationCodeError = '验证码必须是6位数字';
        return false;
      }

      try {
        const response = await fetch('/sso/customer/verify_code', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            phone: this.registerForm.phone,
            code: this.registerForm.verificationCode
          })
        });

        const data = await response.json();

        if (!response.ok) {
          this.registerForm.verificationCodeError = data.error || '验证码无效';
          return false;
        }

        this.registerForm.verificationCodeError = '';
        return true;

      } catch (error) {
        this.registerForm.verificationCodeError = error.message;
        return false;
      }
    },

    // 前往下一步
    goToNextStep() {
      // 清除所有错误信息
      this.registerForm.accountError = '';
      this.registerForm.passwordError = '';
      this.registerForm.confirmPasswordError = '';

      // 验证第一步的字段
      const isAccountValid = this.validateAccount();
      const isPasswordValid = this.validatePassword();
      const isConfirmPasswordValid = this.validateConfirmPassword();

      if (!isAccountValid || !isPasswordValid || !isConfirmPasswordValid) {
        return;
      }

      // 切换到第二步
      this.registerStep = 2;
      this.registerError = '';
    },

    // 返回上一步
    goToPreviousStep() {
      this.registerStep = 1;
      this.registerError = '';
    },

    // 处理注册表单提交
    async handleRegisterSubmit() {
      // 验证所有字段
      const isAccountValid = this.validateAccount();
      const isPasswordValid = this.validatePassword();
      const isConfirmPasswordValid = this.validateConfirmPassword();
      const isPhoneValid = this.validatePhone();

      if (!isAccountValid || !isPasswordValid || !isConfirmPasswordValid || !isPhoneValid) {
        return;
      }

      // 验证验证码
      const isCodeValid = await this.verifyCode();
      if (!isCodeValid) {
        return;
      }

      this.isRegisterLoading = true;
      this.registerError = '';

      try {
        const response = await fetch('/sso/customer/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({
            account: this.registerForm.account,
            password: this.registerForm.password,
            confirm_password: this.registerForm.confirmPassword,
            nickname: this.registerForm.nickname,
            phone: this.registerForm.phone,
            wx: this.registerForm.wx,
            verification_code: this.registerForm.verificationCode
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || '注册失败');
        }

        // 注册成功，存储用户信息
        if (data.customer) {
          localStorage.setItem('customer', data.customer);
          localStorage.setItem('username', this.registerForm.account);

          // 设置抽奖标志
          if (data.show_lottery) {
            localStorage.setItem('show_lottery', 'true');
          }
        }

        // 重定向到首页或指定页面
        if (data.redirectUrl) {
          window.location.href = data.redirectUrl;
        } else {
          this.router.push('/academy/planet');
        }

      } catch (error) {
        this.registerError = error.message;
      } finally {
        this.isRegisterLoading = false;
      }
    },

    async handleLogin() {
      this.isLoading = true;
      this.errorMessage = '';

      try {
        console.log('Sending login request...');
        const response = await fetch('/sso/customer/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          // 确保发送和接收cookie
          credentials: 'include',
          body: JSON.stringify({
            account: this.username,
            password: this.password
          })
        });

        console.log('Login response status:', response.status);
        console.log('Login response headers:', response.headers);

        const data = await response.json();
        console.log('Login response data:', data);

        if (!response.ok) {
          // Handle different error status codes
          if (response.status === 401) {
            throw new Error(data.status || 'Invalid username or password');
          } else if (response.status === 400) {
            throw new Error(data.status || 'Missing username or password');
          } else {
            throw new Error(data.status || 'Login failed. Please try again.');
          }
        }

        // 将 customer 数据存储到 localStorage
        if (data.customer) {
          localStorage.setItem('customer', data.customer);

          // 将用户名存储到 localStorage
          localStorage.setItem('username', this.username);
        }

        // 检查是否有原始 URL cookie
        const cookies = document.cookie.split(';');
        let originUrl = null;

        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.startsWith('ORIGINURL=')) {
            originUrl = cookie.substring('ORIGINURL='.length, cookie.length);
            // 清除 cookie
            document.cookie = 'ORIGINURL=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
            break;
          }
        }

        // 如果有原始 URL，则重定向到原始 URL，否则重定向到首页
        if (originUrl && originUrl !== 'undefined' &&
          !originUrl.includes('/academy/login') &&
          !originUrl.includes('/academy/login/')) {
          console.log('Redirecting to original URL:', originUrl);
          window.location.href = originUrl;
        } else if (data.redirectUrl) {
          console.log('Redirecting to:', data.redirectUrl);
          // 使用绝对路径进行重定向
          window.location.href = data.redirectUrl;
        } else {
          console.log('Redirecting to academy hot articles page');
          this.router.push('/academy');
        }
      } catch (error) {
        this.errorMessage = error.message;
      } finally {
        this.isLoading = false;
      }
    },

    // 检查预览模式
    checkPreviewMode() {
      const urlParams = new URLSearchParams(window.location.search);
      const isPreview = urlParams.get('preview') === 'true';
      const course = urlParams.get('course');

      if (isPreview) {
        // 检查用户是否已经登录
        const customerId = localStorage.getItem('customer');
        const username = localStorage.getItem('username');

        if (customerId && username === 'preview') {
          // 已经登录且是 preview 账号，直接跳转到 notebook
          this.redirectToNotebook(course);
          return;
        }

        // 未登录或不是 preview 账号，自动填充并登录
        this.username = 'preview';
        this.password = 'quantide';

        // 延迟一点时间再自动登录，让用户看到填充过程
        setTimeout(() => {
          this.handlePreviewLogin(course);
        }, 500);
      }
    },

    // 处理预览登录
    async handlePreviewLogin(course) {
      try {
        this.isLoading = true;
        this.errorMessage = '';

        const response = await fetch('/sso/customer/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            account: this.username,
            password: this.password
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.detail || '登录失败');
        }

        // 登录成功，存储用户信息
        if (data.customer) {
          localStorage.setItem('customer', data.customer);
          localStorage.setItem('username', this.username);
        }

        // 跳转到对应课程的 notebook
        this.redirectToNotebook(course);

      } catch (error) {
        this.errorMessage = `预览登录失败: ${error.message}`;
      } finally {
        this.isLoading = false;
      }
    },

    // 跳转到 notebook
    redirectToNotebook(course) {
      if (course) {
        // Preview 账号使用专门的 preview 容器
        const account = 'preview';
        const notebookUrl = `/course/${course}/${account}/lab?`;
        window.location.href = notebookUrl;
      } else {
        // 如果没有指定课程，跳转到 academy 首页
        this.router.push('/academy');
      }
    }
  },
  // 组件挂载后初始化
  mounted() {
    // 确保初始状态正确
    this.isRightPanel = false;
    this.activeTab = 'login';

    // 检查是否为预览模式
    this.checkPreviewMode();
  },

  // 组件销毁时清除定时器
  beforeUnmount() {
    if (this.verificationTimer) {
      clearInterval(this.verificationTimer);
    }
  }
}
</script>

<style scoped>
/* 基础样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 1rem;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

/* 登录面板 */
.login-panel {
  width: 100%;
  max-width: 900px;
  /* 减小总宽度 */
  height: 500px;
  /* 减小高度 */
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  overflow: hidden;
}

/* 左右面板交换动画 */
.panel-half {
  transition: left 0.5s ease-in-out;
  position: absolute;
  height: 100%;
  will-change: left;
  /* 提示浏览器优化动画性能 */
}

/* 调整两侧面板的宽度 */
.panel-form {
  width: 400px;
  /* 功能框宽度 */
  left: 0;
  z-index: 2;
}

.panel-image {
  width: 500px;
  /* 图片宽度 */
  left: 400px;
  /* 紧贴着表单面板 */
  z-index: 1;
  display: block;
  /* 确保图片框始终显示 */
}

/* 切换状态 - 交换位置 */
.panel-swap .panel-form {
  left: 500px;
  /* 图片面板宽度 */
}

.panel-swap .panel-image {
  left: 0;
  /* 移到左侧 */
}

/* 表单面板 */
.panel-form {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  padding: 1.5rem;
  z-index: 2;
  box-sizing: border-box;
}

/* 图片面板 */
.panel-image {
  overflow: hidden;
  z-index: 1;
}

/* 背景图片 */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* 改为contain确保图片完整显示 */
  z-index: 1;
  background-color: #f5f5f5;
  /* 添加背景色 */
}

/* 切换按钮容器 */
.toggle-buttons-container {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  z-index: 10;
  gap: 10px;
}

.toggle-button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 0.5rem 1.2rem;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.toggle-button.active {
  background-color: #00a0a0;
  border-color: #00a0a0;
}

/* 表单容器 */
.form-container {
  width: 100%;
  max-width: 350px;
  /* 增加最大宽度 */
  max-height: 450px;
  /* 增加最大高度 */
  overflow-y: auto;
  padding-right: 5px;
  box-sizing: border-box;
}

.form-title {
  font-size: 1.8rem;
  /* 减小标题字体 */
  color: #333;
  margin-bottom: 0.3rem;
  /* 减小下边距 */
  font-weight: 600;
}

.form-subtitle {
  color: #666;
  margin-bottom: 1rem;
  /* 进一步减小下边距 */
  font-size: 0.85rem;
  /* 进一步减小副标题字体 */
  line-height: 1.3;
  /* 减小行高 */
}

/* 表单元素 */
.form-group {
  margin-bottom: 1rem;
  /* 进一步减小表单组间距 */
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  /* 减小标签下边距 */
  color: #555;
  font-weight: 500;
  font-size: 0.9rem;
  /* 减小标签字体 */
}

/* 带信息图标的标签容器 */
.label-with-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 信息图标样式 */
.info-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #ccc;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-style: italic;
  font-weight: bold;
  cursor: help;
  transition: background-color 0.3s;
}

.info-icon:hover {
  background-color: #999;
}

input {
  width: 100%;
  padding: 0.7rem;
  /* 减小输入框内边距 */
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.9rem;
  /* 减小输入框字体 */
  transition: border-color 0.3s;
}

input:focus {
  border-color: #008080;
  outline: none;
}

.field-hint {
  color: #dc3545;
  font-size: 0.75rem;
  /* 减小字体 */
  margin-top: 0.2rem;
  /* 减小上边距 */
  line-height: 1.2;
  /* 减小行高 */
}

.error-message {
  color: #dc3545;
  margin-bottom: 1rem;
}

/* 按钮样式 */
.primary-button {
  width: 100%;
  padding: 0.7rem;
  /* 减小按钮内边距 */
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.9rem;
  /* 减小按钮字体 */
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 0.8rem;
  /* 减小按钮上边距 */
}

.primary-button:hover {
  filter: brightness(90%);
}

.primary-button:disabled {
  background-color: #cccccc !important;
  cursor: not-allowed;
}

.secondary-button {
  width: 100%;
  padding: 0.7rem;
  /* 减小按钮内边距 */
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 5px;
  font-size: 0.9rem;
  /* 减小按钮字体 */
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 0.8rem;
  /* 减小按钮上边距 */
}

.secondary-button:hover {
  background-color: #e0e0e0;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  /* 减小按钮间距 */
}

/* 紧靠在一起的按钮组 */
.button-group-joined {
  display: flex;
  width: 100%;
  margin-top: 1rem;
}

.button-group-joined .button-left {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: 0;
}

.button-group-joined .button-right {
  flex: 1;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-top: 0;
  background-color: #f0f0f0;
  color: #333;
}

/* 手机验证码 */
.phone-verification {
  display: flex;
  gap: 0.5rem;
}

.verification-button {
  width: auto;
  white-space: nowrap;
  padding: 0.6rem 0.8rem;
  /* 减小内边距 */
  font-size: 0.8rem;
  /* 减小字体 */
  color: white;
  margin-top: 0;
}

.verification-button:disabled {
  background-color: #cccccc;
}

/* 底部信息区域 */
.footer-content {
  margin-top: 1.5rem;
  /* 减小上边距 */
  padding-top: 0.8rem;
  /* 减小内边距 */
  border-top: 1px solid #eee;
  width: 100%;
}

.footer-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo,
.qr-code {
  flex-shrink: 0;
  width: 40px;
  /* 减小图标尺寸 */
  height: 40px;
  /* 减小图标尺寸 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo img,
.qr-code img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qr-code img {
  border-radius: 5px;
}

.slogan {
  flex: 1;
  margin-left: 1rem;
  font-style: italic;
  color: #666;
  font-size: 0.8rem;
  /* 减小字体大小 */
  text-align: right;
  line-height: 1.2;
  /* 减小行高 */
  /* 允许换行显示 */
  overflow: visible;
  white-space: normal;
}

/* 动画效果 */
.panel-right {
  z-index: 0;
}

/* CAPTCHA 对话框 */
.captcha-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.captcha-content {
  background-color: #fff;
  padding: 2rem;
  border-radius: 10px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.captcha-content h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  text-align: center;
  color: #333;
  font-weight: 600;
}

.captcha-content p {
  margin-bottom: 1rem;
  text-align: center;
  color: #666;
}

.captcha-image {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  position: relative;
  min-height: 80px;
  /* 确保加载时有足够的高度 */
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.captcha-image img {
  max-width: 100%;
  max-height: 80px;
  border-radius: 5px;
}

.refresh-captcha {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 30px;
  height: 30px;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: none;
  font-size: 16px;
}

.captcha-input {
  margin-bottom: 1rem;
}

.captcha-input input {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  text-align: center;
  letter-spacing: 2px;
}

.captcha-error {
  color: #dc3545;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.9rem;
  min-height: 1.5rem;
  /* 保持固定高度，避免界面跳动 */
}

.captcha-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.captcha-buttons button {
  width: auto;
  padding: 0.6rem 1.2rem;
  margin: 0;
}

.captcha-buttons button:first-child {
  background-color: #f0f0f0;
  color: #333;
}

.captcha-buttons button:last-child {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .login-panel {
    height: 100vh;
    width: 100%;
    max-width: 100%;
    border-radius: 0;
    box-shadow: none;
  }

  .panel-half {
    width: 100%;
  }

  .panel-form {
    padding: 1.5rem;
    width: 100%;
    /* 高度为剩余空间，减去图片高度并考虑重叠效果 */
    height: calc(100% - calc(100vw * 4 / 7) + 20px);
    /* 顶部位置与图片高度相关，考虑重叠效果 */
    top: calc(calc(100vw * 4 / 7) - 20px);
    left: 0 !important;
    /* 确保在移动端位置固定 */
    border-radius: 20px 20px 0 0;
    margin-top: -10px;
    /* 创建与图片的重叠效果 */
    background-color: white;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    transition: none;
    /* 移动端禁用过渡动画 */
  }

  .panel-image {
    /* 根据7:4的宽高比，高度应为屏幕宽度的4/7 */
    height: calc(100vw * 4 / 7);
    width: 100%;
    top: 0;
    left: 0 !important;
    /* 确保在移动端位置固定 */
    z-index: 1;
    position: relative;
    overflow: hidden;
    background-color: #f5f5f5;
    display: block !important;
    /* 确保图片框始终显示 */
    transition: none;
    /* 移动端禁用过渡动画 */
  }

  /* 移动端背景图片样式 */
  .panel-image .background-image {
    position: absolute;
    width: 100%;
    /* 确保图片等宽 */
    height: 100%;
    /* 高度与容器一致 */
    left: 0;
    top: 0;
    object-fit: cover;
    /* 覆盖模式，确保填满容器 */
  }

  /* 在移动设备上只切换表单内容，图片保持不变 */
  .panel-swap .panel-form {
    /* 不再使用left移动表单，而是通过activeTab控制显示/隐藏 */
    left: 0 !important;
  }

  .panel-swap .panel-image {
    /* 图片面板保持不变 */
    left: 0 !important;
  }

  .form-container {
    padding: 1rem 0;
    overflow-y: auto;
    max-height: 100%;
    height: 100%;
    animation: fadeIn 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .toggle-buttons-container {
    top: 10px;
    right: 10px;
  }

  .toggle-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .form-title {
    font-size: 1.5rem;
    margin-top: 0.5rem;
    margin-bottom: 0.3rem;
  }

  .form-subtitle {
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
  }

  /* 确保表单元素占满宽度 */
  .form-group {
    width: 100%;
    margin-bottom: 1rem;
  }

  input {
    width: 100%;
    box-sizing: border-box;
  }

  .button-group {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* 在移动设备上保持按钮组的水平布局 */
  .button-group-joined {
    display: flex;
    flex-direction: row;
    width: 100%;
  }

  .button-group-joined button {
    flex: 1;
    margin: 0;
  }

  .button-left {
    border-radius: 5px 0 0 5px;
  }

  .button-right {
    border-radius: 0 5px 5px 0;
  }

  .footer-row {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-top: auto;
    /* 将底部信息推到底部 */
    padding-top: 1rem;
  }

  .logo,
  .qr-code {
    margin: 0 0.5rem;
    transform: scale(0.9);
    /* 稍微缩小图标 */
  }

  .slogan {
    width: 100%;
    margin: 0.5rem 0 0 0;
    text-align: center;
    font-size: 0.8rem;
  }

  /* 调整底部内容区域 */
  .footer-content {
    margin-top: auto;
  }
}
</style>
