<template>
    <AcademyLayout>
        <!-- 消息内容区域 -->
        <div class="messages-section">
            <div class="messages-header">
                <h1>消息通知</h1>
                <div class="header-actions">
                    <el-button v-if="messages.length > 0" type="primary" size="small" @click="markAllAsRead">
                        全部标为已读
                    </el-button>
                </div>
            </div>

            <div v-if="loading" class="messages-loading">
                <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="messages.length === 0" class="no-messages">
                <el-empty description="暂无消息" />
            </div>

            <div v-else class="messages-list">
                <div v-for="message in messages" :key="message.id"
                    :class="['message-item', { 'unread': !message.is_read }]" @click="markAsRead(message)"
                    @dblclick="deleteMessage(message)">
                    <div class="message-title">
                        <span>
                            <!-- 未读消息标记 -->
                            <el-tag v-if="!message.is_read" size="small" type="danger" effect="dark"
                                class="unread-tag">未读</el-tag>
                            {{ message.title }}
                        </span>
                        <span class="message-time">{{ formatTime(message.created_at) }}</span>
                    </div>
                    <div class="message-content">{{ message.content }}</div>

                    <!-- 移动端删除按钮 (左滑显示) -->
                    <div class="mobile-actions">
                        <el-button type="danger" size="small" @click.stop="deleteMessage(message)">
                            <el-icon>
                                <Delete />
                            </el-icon>
                            删除
                        </el-button>
                    </div>
                </div>
            </div>

            <div v-if="messages.length > 0" class="messages-footer">
                <el-pagination layout="prev, pager, next" :total="totalMessages" :page-size="pageSize"
                    :current-page="currentPage" @current-change="handlePageChange" />
            </div>
        </div>
    </AcademyLayout>
</template>

<script setup>
import { Delete } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import AcademyLayout from '../components/AcademyLayout.vue';
import axios from '../router/axios';

// 路由器
const router = useRouter();

// 消息列表状态
const messages = ref([]);
const loading = ref(true);
const unreadCount = ref(0);
const totalMessages = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 计算属性：当前用户ID
const customerId = computed(() => localStorage.getItem('customer'));

// 获取消息列表
const fetchMessages = async (page = 1) => {
    if (!customerId.value) return;

    try {
        loading.value = true;
        const offset = (page - 1) * pageSize.value;

        const response = await axios.get('/api/academy/messages', {
            params: {
                limit: pageSize.value,
                offset: offset
            }
        });

        messages.value = response.data.messages;
        unreadCount.value = response.data.unread_count;
        totalMessages.value = response.data.total;
    } catch (error) {
        console.error('获取消息失败:', error);
        ElMessage.error('获取消息失败');
    } finally {
        loading.value = false;
    }
};

// 标记消息为已读
const markAsRead = async (message) => {
    if (message.is_read) return;

    try {
        await axios.put(`/api/academy/messages/${message.id}/read`);
        message.is_read = true;
        unreadCount.value = Math.max(0, unreadCount.value - 1);
    } catch (error) {
        console.error('标记消息已读失败:', error);
        ElMessage.error('标记消息已读失败');
    }
};

// 标记所有消息为已读
const markAllAsRead = async () => {
    try {
        const unreadMessages = messages.value.filter(msg => !msg.is_read);
        if (unreadMessages.length === 0) return;

        await Promise.all(unreadMessages.map(msg => markAsRead(msg)));
        ElMessage.success('已将所有消息标记为已读');
    } catch (error) {
        console.error('标记所有消息已读失败:', error);
        ElMessage.error('标记所有消息已读失败');
    }
};

// 删除消息
const deleteMessage = async (message) => {
    try {
        await ElMessageBox.confirm('确定要删除这条消息吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        await axios.delete(`/api/academy/messages/${message.id}`);
        messages.value = messages.value.filter(msg => msg.id !== message.id);

        if (!message.is_read) {
            unreadCount.value = Math.max(0, unreadCount.value - 1);
        }

        ElMessage.success('消息已删除');
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除消息失败:', error);
            ElMessage.error('删除消息失败');
        }
    }
};

// 处理分页变化
const handlePageChange = (page) => {
    currentPage.value = page;
    fetchMessages(page);
};

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    // 一小时内
    if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes}分钟前`;
    }

    // 今天内
    if (date.toDateString() === now.toDateString()) {
        return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }

    // 昨天
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
        return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }

    // 一周内
    if (diff < 604800000) {
        const days = ['日', '一', '二', '三', '四', '五', '六'];
        return `周${days[date.getDay()]} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }

    // 其他
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
};

// 组件挂载时获取消息数据
onMounted(() => {
    if (customerId.value) {
        fetchMessages();
    }
});
</script>

<style scoped>
/* 消息部分样式 */
.messages-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.messages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.messages-header h1 {
    margin: 0;
    font-size: 24px;
    color: #303133;
}

.messages-loading,
.no-messages {
    padding: 40px 0;
    text-align: center;
}

.messages-list {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.message-item {
    position: relative;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    transition: all 0.3s;
    overflow: hidden;
}

.message-item:last-child {
    border-bottom: none;
}

.message-item:hover {
    background-color: #f5f7fa;
}

.message-item.unread {
    background-color: #ecf5ff;
}

.message-item.unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: #409eff;
}

.message-title {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    margin-bottom: 8px;
}

.message-time {
    font-size: 12px;
    color: #909399;
    font-weight: normal;
}

.message-content {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
}

.unread-tag {
    margin-right: 5px;
    font-size: 10px;
    padding: 0 4px;
    height: 18px;
    line-height: 16px;
}

.messages-footer {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

/* 移动端左滑删除 */
.mobile-actions {
    position: absolute;
    right: -100px;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    transition: transform 0.3s;
    background-color: #f56c6c;
    width: 80px;
    justify-content: center;
}

@media (hover: none) {

    /* 仅在触摸设备上启用左滑功能 */
    .message-item {
        touch-action: pan-y;
    }

    .message-item:active {
        transform: translateX(-80px);
    }

    .message-item:active .mobile-actions {
        transform: translateX(0);
    }
}

/* 桌面端悬停效果 */
@media (hover: hover) {
    .message-item:hover .mobile-actions {
        display: none;
    }
}

@media (max-width: 768px) {
    .messages-section {
        padding: 10px;
    }

    .messages-header h1 {
        font-size: 20px;
    }
}
</style>
