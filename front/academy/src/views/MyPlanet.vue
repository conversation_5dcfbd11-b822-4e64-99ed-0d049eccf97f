<!-- src/views/MyPlanet.vue -->
<template>
  <AcademyLayout>
    <!-- 内容区域 -->
    <div class="planet-section">
      <!-- 加载中提示 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 错误提示 -->
      <el-alert v-if="error" :title="error" type="error" :closable="false" show-icon />

      <!-- 内容 -->
      <div v-if="!loading && !error">
        <!-- 标题 -->
        <div class="section-header">
          <h2>研报博文</h2>
          <div class="description">可运行的研报和博文，随时随地学习和实践</div>
        </div>

        <!-- 文章列表 -->
        <div class="article-list" v-if="articles.length > 0">
          <div class="article-card" v-for="article in articles" :key="article.id">
            <!-- 文章图片 -->
            <div class="article-image" v-if="article.img">
              <img :src="article.img" alt="文章封面" />
            </div>
            <div class="article-content">
              <div class="article-title">{{ article.title }}</div>
              <div class="article-description">{{ article.description || '暂无描述' }}</div>
              <div class="article-meta">
                <span class="publish-date">{{ formatDate(article.publish_date) }}</span>
              </div>
              <div class="article-actions">
                <!-- 1. 如果用户被允许访问某个资源，则显示『阅读文章』按钮 -->
                <el-button v-if="article.is_accessible" type="primary" @click="viewArticle(article)">
                  运行文章
                </el-button>
                <!-- 2. 如果用户不允许访问某个资源 -->
                <template v-else>
                  <!-- 2.1 用户订阅了博客，表明是进度控制原因，显示[等待解锁] -->
                  <el-tooltip v-if="article.has_subscription && article.access_reason === 'subscription_locked'"
                    :content="getUnlockTooltip(article)" placement="top">
                    <el-button type="info" disabled>
                      等待解锁
                    </el-button>
                  </el-tooltip>
                  <!-- 2.2 用户没有订阅博客，显示价格按钮 -->
                  <el-button v-else type="warning" @click="showPurchaseDialog(article)">
                    ¥{{ article.price }}
                  </el-button>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- 无文章提示 -->
        <el-empty v-else description="暂无可访问的文章" />
      </div>
    </div>

    <!-- 购买确认对话框 -->
    <el-dialog v-model="purchaseDialogVisible" title="购买文章" width="500px" center>
      <div class="purchase-dialog-content">
        <div class="article-info">
          <h3>{{ selectedArticle?.title }}</h3>
          <p class="article-desc">{{ selectedArticle?.description || '暂无描述' }}</p>
        </div>
        <div class="price-info">
          <span class="price-label">价格：</span>
          <span class="price-amount">¥{{ selectedArticle?.price }}</span>
        </div>

        <div class="purchase-instructions">
          <h4>购买说明</h4>
          <p>即将跳转到公众号平台完成付款。付款完成后，请联系助理开通文章权限。</p>
        </div>

      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="purchaseDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPurchase" :loading="purchasing">
            确认跳转购买
          </el-button>
        </div>
      </template>
    </el-dialog>
  </AcademyLayout>
</template>

<script setup>
import AcademyLayout from '../components/AcademyLayout.vue'
import { onMounted, ref } from 'vue'
import axios from '../router/axios'
import { initAutoScale } from '../utils/autoScale'
import { ElMessage } from 'element-plus'

// 数据
const articles = ref([])
const loading = ref(true)
const error = ref(null)

// 购买相关数据
const purchaseDialogVisible = ref(false)
const selectedArticle = ref(null)
const purchasing = ref(false)

// 加载数据
const loadPlanetData = async () => {
  try {
    loading.value = true
    error.value = null

    const customerId = localStorage.getItem('customer')
    if (!customerId) {
      error.value = '请先登录'
      return
    }

    // 获取星球数据
    const response = await axios.get(`/api/academy/blog/${customerId}`)
    console.log('Planet data response:', response.data)
    console.log('Articles with description:', response.data.articles.filter(a => a.description))

    // 更新数据
    articles.value = response.data.articles || []
  } catch (err) {
    console.error('Failed to load planet data:', err)
    error.value = '加载数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据并初始化自动缩放
onMounted(() => {
  loadPlanetData()
  initAutoScale() // 初始化自动缩放功能
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

// 获取解锁时间提示
const getUnlockTooltip = (article) => {
  if (!article.unlock_time) return '等待解锁'

  const unlockDate = new Date(article.unlock_time)
  const now = new Date()

  // 如果解锁时间已过，但仍显示为锁定状态，可能是缓存问题
  if (unlockDate <= now) {
    return '即将解锁，请刷新页面'
  }

  // 格式化解锁时间
  const month = unlockDate.getMonth() + 1
  const day = unlockDate.getDate()
  const hour = unlockDate.getHours()

  return `将于${month}月${day}日${hour}时解锁`
}

// 查看文章
const viewArticle = (article) => {
  if (!article.is_accessible) {
    ElMessage.warning('您没有权限访问此文章')
    return
  }

  if (!article.url) {
    ElMessage.error('文章链接不可用')
    return
  }

  // 跳转到文章页面
  window.open(article.url, '_blank')
}

// 显示购买对话框
const showPurchaseDialog = (article) => {
  selectedArticle.value = article
  purchaseDialogVisible.value = true
}

// 确认购买
const confirmPurchase = async () => {
  if (!selectedArticle.value) return

  try {
    purchasing.value = true

    // 直接使用文章的购买链接
    if (selectedArticle.value.buy_link) {
      // 跳转到微信公众号付费文章
      window.open(selectedArticle.value.buy_link, '_blank')
      ElMessage.success('已跳转到购买页面，请完成付款后联系助理开通权限')
    } else {
      // 如果没有配置微信链接，显示提示
      ElMessage.info('请联系助理购买此文章')
    }

    // 关闭对话框
    purchaseDialogVisible.value = false

  } catch (error) {
    console.error('Purchase failed:', error)
    ElMessage.error('跳转购买页面失败，请稍后重试')
  } finally {
    purchasing.value = false
  }
}
</script>

<style scoped>
.planet-section {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  padding: clamp(10px, 2vw, 20px);
}

.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0 0 10px 0;
  font-size: clamp(20px, 3vw, 24px);
  color: #333;
}

.description {
  color: #666;
  font-size: clamp(14px, 1.8vw, 16px);
  margin-bottom: 20px;
}

.loading-container {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
}

.article-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.article-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(64, 158, 255, 0.1);
}

.article-image {
  width: 100%;
  height: 0;
  padding-bottom: 75%;
  /* 4:3 比例 */
  position: relative;
  overflow: hidden;
}

.article-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.article-title {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.article-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
  flex: 1;
  min-height: 60px;
  /* 确保描述有最小高度，支持更多文字 */
  display: block;
  /* 确保描述显示为块级元素 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 4;
  /* 限制在4行，支持约120个字符 */
  -webkit-box-orient: vertical;
  line-height: 1.4;
  /* 设置行高，确保4行能显示足够内容 */
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  color: #999;
  font-size: 12px;
}

.price-tag {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background-color: #ff6b6b;
  color: white;
}

.price-tag.free {
  background-color: #51cf66;
}

.article-actions {
  display: flex;
  justify-content: flex-end;
}

/* 购买对话框样式 */
.purchase-dialog-content {
  padding: 20px 0;
}

.article-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.article-desc {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.price-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.price-label {
  font-size: 16px;
  color: #666;
  margin-right: 10px;
}

.price-amount {
  font-size: 24px;
  font-weight: 600;
  color: #ff6b6b;
}

.purchase-note {
  color: #999;
  font-size: 14px;
  text-align: center;
}

.purchase-instructions {
  margin-bottom: 20px;
}

.purchase-instructions h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.purchase-instructions p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.wechat-qr {
  text-align: center;
  margin-bottom: 20px;
}

.wechat-qr h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-image {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.qr-note {
  color: #999;
  font-size: 12px;
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-list {
    grid-template-columns: 1fr;
  }
}
</style>
