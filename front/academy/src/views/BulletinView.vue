<template>
  <AcademyLayout>
    <div class="bulletin-container">
      <div class="bulletin-content">
        <h2 class="content-title">{{ currentDocument.title }}</h2>
        <MarkdownViewer :path="currentDocument.path" />
      </div>
    </div>
  </AcademyLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import AcademyLayout from '../components/AcademyLayout.vue';
import MarkdownViewer from '../components/MarkdownViewer.vue';

const route = useRoute();

// 当前显示的文档
const currentDocument = ref({
  title: '抽奖规则',
  path: '/academy/markdown/lucky-draw-readme.md'
});

// 根据路由参数设置当前文档
const setCurrentDocumentFromRoute = () => {
  const id = route.params.id;
  if (id) {
    // 根据文件名设置标题
    const titleMap = {
      'lucky-draw': '抽奖规则',
      'tos': '用户协议',
      'privacy': '隐私政策',
      'faq': '常见问题'
    };

    const fileMap = {
      'lucky-draw': 'lucky-draw-readme.md',
      'tos': 'term-of-service.md',
      'privacy': 'privacy-policy.md',
      'faq': 'faq.md'
    };

    currentDocument.value = {
      title: titleMap[id] || '文档',
      path: `/academy/markdown/${fileMap[id] || id + '.md'}`
    };
  }
};

// 组件挂载时根据路由参数设置当前文档
onMounted(() => {
  setCurrentDocumentFromRoute();
});
</script>

<style scoped>
.bulletin-container {
  width: 100%;
  padding: 20px;
}

.bulletin-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.content-title {
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eaecef;
  font-size: 1.8rem;
  color: #303133;
}
</style>
