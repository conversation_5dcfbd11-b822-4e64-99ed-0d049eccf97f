import { createRouter, createWebHistory } from "vue-router";
import AcademyView from '../views/Academy.vue';
import LoginView from '../views/Login.vue';

const router = createRouter({
  history: createWebHistory('/academy'), // 使用 history 模式，基路径为 /academy
  routes: [
    {
      path: '/',
      name: 'home',
      component: AcademyView,
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requirsGuest: true }
    },
    {
      path: '/academy',
      redirect: '/'
    }
  ]
})

router.beforeEach((to, _from, next) => {
  const isLoggedIn = localStorage.getItem('customer') !== null;

  // Protect routes requiring auth
  if (to.meta.requiresAuth && !isLoggedIn) {
    next('/login');
    return;
  }

  // Prevent logged-in users from accessing login page
  if (to.meta.requiresGuest && isLoggedIn) {
    next('/');
    return;
  }

  next();
});

export default router
