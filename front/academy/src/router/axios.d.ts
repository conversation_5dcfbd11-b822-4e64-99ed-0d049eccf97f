import axios from 'axios';

declare module 'axios' {
  interface AxiosError<T = any> extends Error {
    config: any;
    code?: string;
    request?: any;
    response?: {
      status: number;
      statusText: string;
      data: T;
    };
    isAxiosError: boolean;
    toJSON: () => object;
  }

  interface AxiosInstance {
    request<T = any>(config: AxiosRequestConfig): Promise<T>;
    get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
    post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
    patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  }
}

declare module '@/router/axios' {
  const instance: axios.AxiosInstance;
  export default instance;
}
