import axios from 'axios';

const instance = axios.create({
    baseURL: '',  // 移除 baseURL 前缀
    headers: {
        'Content-Type': 'application/json'
    }
})

instance.interceptors.request.use(
    config => {
        console.log(`axios ${config.method} ${config.url}`);
        config.withCredentials = true;
        return config;
    }, error => {
        return Promise.reject(error);
    });

instance.interceptors.response.use(
    response => {
        console.info(response)
        return response;
    },
    error => {
        console.error('Axios error:', error);
        // 检查是否是 401 未授权错误
        if (error.response && error.response.status === 401) {
            console.log('Unauthorized, redirecting to login page');
            // 清除本地存储的用户信息
            localStorage.removeItem('customer');
            // 重定向到登录页面
            window.location.href = '/academy/login';
        }
        return Promise.reject(error);
    }
);

export default instance;
