import { createRouter, createWebHistory } from 'vue-router';
import About from '../views/About.vue';
import Academy from '../views/Academy.vue';
import BulletinView from '../views/BulletinView.vue';
import CourseDetail from '../views/CourseDetail.vue';
import LiveStreams from '../views/LiveStreams.vue';
import Login from '../views/Login.vue';
import Messages from '../views/Messages.vue';
import MyPlanet from '../views/MyPlanet.vue';
import Promotions from '../views/Promotions.vue';

const routes = [
    {
        path: '/',
        redirect: '/academy'
    },
    {
        path: '/academy',
        name: 'Planet',
        component: MyPlanet
    },
    {
        path: '/academy/academy',
        name: 'Academy',
        component: Academy
    },
    {
        path: '/academy/dashboard',
        name: 'Dashboard',
        component: Academy
    },
    {
        path: '/academy/messages',
        name: 'Messages',
        component: Messages
    },
    {
        path: '/academy/promotions',
        name: 'Promotions',
        component: Promotions
    },
    {
        path: '/academy/live',
        name: 'LiveStreams',
        component: LiveStreams
    },
    {
        path: '/academy/about',
        name: 'About',
        component: About
    },
    {
        path: '/academy/bulletin',
        name: 'Bulletin',
        component: BulletinView
    },
    {
        path: '/academy/bulletin/:id',
        name: 'BulletinDetail',
        component: BulletinView
    },
    {
        path: '/academy/course-detail/:filename',
        name: 'CourseDetail',
        component: CourseDetail
    },
    {
        path: '/academy/login',
        name: 'Login',
        component: Login
    },
    {
        path: '/login',
        redirect: '/academy/login'
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
    // 检查用户是否已登录
    const isLoggedIn = !!localStorage.getItem('customer');

    // 定义不需要登录的页面路径
    const publicPages = ['/academy/login', '/login'];
    const isPublicPage = publicPages.includes(to.path);

    // 如果用户未登录且不是访问公开页面，则重定向到登录页面
    if (!isLoggedIn && !isPublicPage) {
        next('/academy/login');
    } else {
        next();
    }
});

export default router;
