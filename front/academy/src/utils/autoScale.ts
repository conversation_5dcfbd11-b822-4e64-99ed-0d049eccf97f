/**
 * 自动缩放功能
 * 当屏幕宽度小于指定的最小宽度时，通过 scale 缩放内容
 */

// 最小设计宽度，低于这个宽度时会进行缩放
// iPhone 12/13 的屏幕宽度为 390px
// Galaxy S系列屏幕宽度约为 360-412px
// 其他常见移动设备宽度在 320-480px 之间
// 设置最小宽度为 480px 以覆盖更多设备
const MIN_WIDTH = 480;

/**
 * 检测是否为移动设备
 */
function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 计算并应用缩放比例
 */
export function applyAutoScale(): void {
  // 获取视口宽度
  const viewportWidth = window.innerWidth || document.documentElement.clientWidth;

  // 如果是移动设备且视口宽度小于最小宽度，计算缩放比例
  if (isMobileDevice() && viewportWidth < MIN_WIDTH) {
    // 根据不同设备宽度应用不同的缩放比例
    let scale: number;

    // 为不同设备优化缩放比例
    if (viewportWidth <= 360) {
      // 小屏幕设备（如 Galaxy S5 等）
      scale = 0.85;
    } else if (viewportWidth <= 390) {
      // iPhone 12/13 等设备
      scale = 0.9;
    } else if (viewportWidth <= 420) {
      // Galaxy S8/S9/S10 等设备
      scale = 0.92;
    } else {
      // 其他设备使用动态计算
      scale = viewportWidth / MIN_WIDTH;
    }

    // 获取应用容器
    const appElement = document.getElementById('app');
    if (!appElement) return;

    // 计算宽度补偿（1/scale）
    const widthCompensation = Math.round((1 / scale) * 100);

    // 应用缩放
    appElement.style.width = `${widthCompensation}%`;
    appElement.style.height = `${window.innerHeight / scale}px`;
    appElement.style.transform = `scale(${scale})`;
    appElement.style.transformOrigin = 'top left';

    // 调整滚动行为
    document.body.style.overflow = 'auto';
    document.body.style.width = `${viewportWidth}px`;
    document.body.style.height = `${window.innerHeight}px`;

    // 添加小屏幕标记类和设备特定类
    document.body.classList.add('small-screen');

    // 添加设备宽度范围类，便于CSS定位特定设备
    if (viewportWidth <= 360) {
      document.body.classList.add('device-xs');
    } else if (viewportWidth <= 390) {
      document.body.classList.add('device-sm');
    } else if (viewportWidth <= 420) {
      document.body.classList.add('device-md');
    } else {
      document.body.classList.add('device-lg');
    }

    // 添加缩放比例作为 CSS 变量，便于其他组件使用
    document.documentElement.style.setProperty('--scale-ratio', scale.toString());
    document.documentElement.style.setProperty('--width-compensation', `${widthCompensation}%`);
  } else {
    // 重置样式
    const appElement = document.getElementById('app');
    if (appElement) {
      appElement.style.width = '';
      appElement.style.height = '';
      appElement.style.transform = '';
      appElement.style.transformOrigin = '';
    }

    document.body.style.overflow = '';
    document.body.style.width = '';
    document.body.style.height = '';

    // 移除所有设备相关类
    document.body.classList.remove('small-screen', 'device-xs', 'device-sm', 'device-md', 'device-lg');

    // 移除CSS变量
    document.documentElement.style.removeProperty('--scale-ratio');
    document.documentElement.style.removeProperty('--width-compensation');
  }
}

/**
 * 初始化自动缩放
 */
export function initAutoScale(): void {
  // 页面加载时应用缩放
  applyAutoScale();

  // 窗口大小改变时重新计算缩放
  window.addEventListener('resize', applyAutoScale);
}

export default {
  initAutoScale,
  applyAutoScale
};
