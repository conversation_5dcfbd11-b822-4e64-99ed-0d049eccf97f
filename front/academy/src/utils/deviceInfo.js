/**
 * 设备信息收集工具
 */
import UAParser from 'ua-parser-js';

/**
 * 收集设备信息
 * @returns {Object} 设备信息对象
 */
export function collectDeviceInfo() {
    const parser = new UAParser();
    const result = parser.getResult();
    
    // 收集基本设备信息
    const deviceInfo = {
        // 浏览器信息
        browser: {
            name: result.browser.name || 'Unknown',
            version: result.browser.version || 'Unknown',
            major: result.browser.major || 'Unknown'
        },
        
        // 操作系统信息
        os: {
            name: result.os.name || 'Unknown',
            version: result.os.version || 'Unknown'
        },
        
        // 设备信息
        device: {
            type: result.device.type || 'desktop',
            vendor: result.device.vendor || 'Unknown',
            model: result.device.model || 'Unknown'
        },
        
        // CPU信息
        cpu: {
            architecture: result.cpu.architecture || 'Unknown'
        },
        
        // 引擎信息
        engine: {
            name: result.engine.name || 'Unknown',
            version: result.engine.version || 'Unknown'
        }
    };
    
    // 收集屏幕信息
    if (typeof window !== 'undefined' && window.screen) {
        deviceInfo.screen = {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight,
            colorDepth: window.screen.colorDepth,
            pixelDepth: window.screen.pixelDepth
        };
    }
    
    // 收集窗口信息
    if (typeof window !== 'undefined') {
        deviceInfo.viewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }
    
    // 收集时区信息
    try {
        deviceInfo.timezone = {
            name: Intl.DateTimeFormat().resolvedOptions().timeZone,
            offset: new Date().getTimezoneOffset()
        };
    } catch (e) {
        deviceInfo.timezone = {
            name: 'Unknown',
            offset: new Date().getTimezoneOffset()
        };
    }
    
    // 收集语言信息
    if (typeof navigator !== 'undefined') {
        deviceInfo.language = {
            primary: navigator.language || 'Unknown',
            languages: navigator.languages || []
        };
    }
    
    // 收集连接信息（如果可用）
    if (typeof navigator !== 'undefined' && navigator.connection) {
        deviceInfo.connection = {
            effectiveType: navigator.connection.effectiveType || 'Unknown',
            downlink: navigator.connection.downlink || 0,
            rtt: navigator.connection.rtt || 0
        };
    }
    
    // 收集内存信息（如果可用）
    if (typeof navigator !== 'undefined' && navigator.deviceMemory) {
        deviceInfo.memory = {
            deviceMemory: navigator.deviceMemory
        };
    }
    
    // 收集硬件并发信息
    if (typeof navigator !== 'undefined' && navigator.hardwareConcurrency) {
        deviceInfo.hardware = {
            concurrency: navigator.hardwareConcurrency
        };
    }
    
    // 添加收集时间戳
    deviceInfo.collectedAt = new Date().toISOString();
    
    return deviceInfo;
}

/**
 * 发送设备信息到后端
 * @param {Object} deviceInfo 设备信息
 * @param {string} url 当前访问的URL
 * @returns {Promise} 发送结果
 */
export async function sendDeviceInfo(deviceInfo, url = window.location.pathname) {
    try {
        const response = await fetch('/api/device-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include', // 包含cookies
            body: JSON.stringify({
                deviceInfo,
                url,
                timestamp: new Date().toISOString()
            })
        });
        
        if (!response.ok) {
            console.warn('发送设备信息失败:', response.status, response.statusText);
            return false;
        }
        
        const result = await response.json();
        console.log('设备信息发送成功:', result);
        return true;
    } catch (error) {
        console.error('发送设备信息时出错:', error);
        return false;
    }
}

/**
 * 初始化设备信息收集
 * 在页面加载时自动收集并发送设备信息
 */
export function initDeviceInfoCollection() {
    // 等待页面完全加载后收集设备信息
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', collectAndSend);
    } else {
        // 如果页面已经加载完成，立即执行
        setTimeout(collectAndSend, 100);
    }
}

/**
 * 收集并发送设备信息
 */
function collectAndSend() {
    try {
        const deviceInfo = collectDeviceInfo();
        console.log('收集到的设备信息:', deviceInfo);
        
        // 发送设备信息
        sendDeviceInfo(deviceInfo);
    } catch (error) {
        console.error('收集设备信息时出错:', error);
    }
}

/**
 * 获取简化的设备信息（用于日志记录）
 * @returns {string} 简化的设备信息字符串
 */
export function getSimpleDeviceInfo() {
    try {
        const parser = new UAParser();
        const result = parser.getResult();
        
        const browser = `${result.browser.name || 'Unknown'} ${result.browser.version || ''}`.trim();
        const os = `${result.os.name || 'Unknown'} ${result.os.version || ''}`.trim();
        const device = result.device.type || 'desktop';
        
        return `${browser} on ${os} (${device})`;
    } catch (error) {
        console.error('获取简化设备信息时出错:', error);
        return 'Unknown Device';
    }
}

// 默认导出主要函数
export default {
    collectDeviceInfo,
    sendDeviceInfo,
    initDeviceInfoCollection,
    getSimpleDeviceInfo
};
