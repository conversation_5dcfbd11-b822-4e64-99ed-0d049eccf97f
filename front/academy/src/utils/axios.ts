// @ts-nocheck
// 使用 @ts-nocheck 禁用整个文件的类型检查

import axios from 'axios';

// 创建axios实例
const instance = axios.create({
  // 在开发环境中，baseURL可以为空，因为我们使用了Vite的代理
  // 在生产环境中，这里可以设置为实际的API地址
  baseURL: '',
  // 请求超时时间
  timeout: 10000,
  // 请求头信息
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在这里可以添加认证信息，如token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 直接返回响应数据
    return response;
  },
  (error) => {
    console.error('Response error:', error);
    // 可以在这里统一处理错误，例如401未授权、500服务器错误等
    if (error.response) {
      switch (error.response.status) {
        case 401:
          console.error('Unauthorized, please login');
          // 可以在这里处理登录逻辑
          break;
        case 403:
          console.error('Forbidden');
          break;
        case 404:
          console.error('API not found');
          break;
        case 500:
          console.error('Server error');
          break;
        default:
          console.error(`Error with status code: ${error.response.status}`);
      }
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error setting up request:', error.message);
    }
    return Promise.reject(error);
  }
);

export default instance;
