// 导入所有需要的图片资源
import l24_300 from '@/assets/24lectures_300.png';
import l24_1000 from '@/assets/24lectures_1000.png';
import fa_300 from '@/assets/fa_300.png';
import fa_1000 from '@/assets/fa_1000.png';
import pandas_300 from '@/assets/pandas_300.png';
import pandas_1000 from '@/assets/pandas_1000.png';
import blog_300 from '@/assets/blog_300.jpg';
import blog_1000 from '@/assets/blog_1000.jpg';

const imageResources = {
  // 按课程名称分类
  courses: {
    'fa': {
      '300': fa_300,
      '1000': fa_1000
    },
    'pandas': {
      '300': pandas_300,
      '1000': pandas_1000
    },
    'blog': {
      '300': blog_300,
      '1000': blog_1000
    },
    'l24': {
      '300': l24_300,
      '1000': l24_1000
    }
  }
};

/**
 * 获取课程图片
 * @param {string} courseName - 课程名称
 * @param {number} width - 图片宽度
 * @param {number} height - 图片高度（可选）
 * @returns {string} - 图片资源
 */
export function getCourseImage(courseName, width) {
  // 将宽度转换为字符串，用于查找最接近的尺寸
  const widthKey = width.toString();

  // 检查课程是否存在
  if (!imageResources.courses[courseName]) {
    console.warn(`Course image not found for: ${courseName}`);
    return getCourseImage(courseName, 1000);
  }

  // 检查指定尺寸是否存在
  if (imageResources.courses[courseName][widthKey]) {
    return imageResources.courses[courseName][widthKey];
  }

  // 如果指定尺寸不存在，查找最接近的尺寸
  const availableSizes = Object.keys(imageResources.courses[courseName])
    .map(size => parseInt(size))
    .sort((a, b) => a - b);

  if (availableSizes.length === 0) {
    return getCourseImage(courseName, 1000);
  }

  // 找到最接近的尺寸
  let closestSize = availableSizes[0];
  for (const size of availableSizes) {
    if (Math.abs(size - width) < Math.abs(closestSize - width)) {
      closestSize = size;
    }
  }

  return imageResources.courses[courseName][closestSize.toString()];
}

export default {
  getCourseImage,
};

