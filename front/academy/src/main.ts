import './assets/main.css'
import './assets/smallScreen.css'

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// 引入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 引入axios配置
import type { AxiosInstance } from 'axios'
import axios from './utils/axios'

// 引入自动缩放功能
import { initAutoScale } from './utils/autoScale'

const app = createApp(App)

// 全局注册axios
app.config.globalProperties.$axios = axios

// 声明全局属性类型
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $axios: AxiosInstance
    }
}

app.use(router)
app.use(ElementPlus)

app.mount('#app')

// 初始化自动缩放功能
initAutoScale()
