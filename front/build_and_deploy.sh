#!/bin/bash

set -e
set -x

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "Script directory: $SCRIPT_DIR"

# 确保静态目录存在
STATIC_DIR="$SCRIPT_DIR/../provision/static"
echo "Static directory: $STATIC_DIR"

# 如果 $1 == admin
if [ "$1" == "admin" ]; then
    echo "Building admin frontend..."
    cd "$SCRIPT_DIR/admin"
    echo "Current directory: $(pwd)"

    mkdir -p "$STATIC_DIR/admin"
    rm -rf "$STATIC_DIR/admin/*"
    # 安装依赖
    echo "Installing dependencies..."
    npm install

    # 构建 Vue 应用
    echo "Building Vue application..."
    npm run build

    # 检查构建结果
    echo "Build results (directly in static directory):"
    ls -la "$STATIC_DIR/admin/"

    echo "Admin frontend built successfully."
else
    echo "Building academy frontend..."
    cd "$SCRIPT_DIR/academy"
    echo "Current directory: $(pwd)"

    mkdir -p "$STATIC_DIR/academy"
    rm -rf "$STATIC_DIR/academy/*"

    # 安装依赖
    echo "Installing dependencies..."
    npm install

    # 构建 Vue 应用
    echo "Building Vue application..."
    npm run build

    # 检查构建结果
    echo "Build results (directly in static directory):"
    ls -la "$STATIC_DIR/academy/"
fi

cp -r "$SCRIPT_DIR/public"/* "$STATIC_DIR/"
cp -r "$STATIC_DIR"/* ~/site

echo "Frontend build completed."
