{"name": "provision", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/axios": "^0.9.36", "axios": "^1.8.4", "luxon": "^3.6.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.0", "@tsconfig/node22": "^22.0.0", "@types/luxon": "^3.6.2", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.21.0", "eslint-plugin-oxlint": "^0.15.13", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "oxlint": "^0.15.13", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}