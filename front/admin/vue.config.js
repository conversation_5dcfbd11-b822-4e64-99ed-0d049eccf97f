const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
    transpileDependencies: true,
    // Output the built files to the provision/static directory
    outputDir: '../provision/static',
    // Set the public path to be relative
    publicPath: '/',
    // Configure the dev server for development
    devServer: {
        proxy: {
            '/api': {
                target: 'http://localhost:8000',
                changeOrigin: true
            }
        }
    }
}) 
