<script setup lang="ts">
import { checkAuth, logout } from '@/services/auth';
import { computed, onMounted, provide, ref, watch } from 'vue';
import { RouterLink, RouterView, useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const activeRoute = computed(() => route.path);

// 用户认证状态
const isAuthenticated = ref(false);
const username = ref('');

// 检查认证状态
const checkAuthentication = async () => {
    try {
        console.log('Checking authentication status...');
        const auth = await checkAuth();
        console.log('Auth response:', auth);
        isAuthenticated.value = auth.authenticated;

        if (!isAuthenticated.value) {
            if (route.name !== 'login') {
                router.push({ name: 'login' });
            }
            else {
                return
            }
        }

        console.log('isAuthenticated set to:', isAuthenticated.value);
        if (auth.authenticated && auth.username) {
            username.value = auth.username;
            console.log('Username set to:', username.value);
        } else {
            username.value = '';
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        isAuthenticated.value = false;
        username.value = '';
    }
};

// 在组件挂载时检查认证状态
onMounted(checkAuthentication);

// 监听路由变化，当路由变化时重新检查认证状态
watch(() => route.path, checkAuthentication);

// 处理登出
const handleLogout = async () => {
    try {
        console.log('Logging out...');

        // 先调用登出 API，清除服务器端的会话
        const response = await logout();
        console.log('Logout response:', response);

        // 清除本地存储的用户信息
        localStorage.removeItem('admin-token');

        // 重置认证状态
        isAuthenticated.value = false;
        username.value = '';

        // 使用路由器导航到登录页面
        console.log('尝试导航到登录页面');

        // 使用 setTimeout 给浏览器一点时间来处理 cookie 清除
        setTimeout(() => {
            router.push({ name: 'login' });
        }, 100);
    } catch (error) {
        console.error('Logout failed:', error);

        // 即使出错也确保用户已经登出
        localStorage.removeItem('admin-token');
        isAuthenticated.value = false;
        username.value = '';

        // 确保用户被重定向到登录页面
        if (route.name !== 'login') {
            router.push({ name: 'login' });
        }
    }
};


</script>

<template>
    <div class="app-container">
        <!-- 统一的 Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-container">
                    <img alt="QuantIDE logo" class="logo" src="@/assets/logo.png" />
                </div>
                <h1 class="slogan">Grow with QuantIDE</h1>
                <div class="spacer"></div>
                <div class="user-menu">
                    <span v-if="isAuthenticated" class="username">{{ username }}</span>
                    <button @click="handleLogout" class="logout-btn" :disabled="!isAuthenticated">登出</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- 左侧 Sidebar -->
            <aside class="sidebar">
                <nav class="sidebar-nav">
                    <RouterLink to="/customers" class="nav-item"
                        :class="{ active: activeRoute.includes('/customers') }">
                        <span class="nav-text">客户管理</span>
                    </RouterLink>
                    <RouterLink to="/registry" class="nav-item" :class="{ active: activeRoute.includes('/registry') }">
                        <span class="nav-text">课程注册</span>
                    </RouterLink>
                    <RouterLink to="/messages" class="nav-item" :class="{ active: activeRoute.includes('/messages') }">
                        <span class="nav-text">消息管理</span>
                    </RouterLink>
                    <RouterLink to="/resources" class="nav-item" :class="{ active: activeRoute === '/resources' }">
                        <span class="nav-text">资源管理</span>
                    </RouterLink>
                    <RouterLink to="/whitelist" class="nav-item"
                        :class="{ active: activeRoute === '/resource-whitelist' }">
                        <span class="nav-text">资源白名单</span>
                    </RouterLink>
                    <RouterLink to="/ticket-verification" class="nav-item"
                        :class="{ active: activeRoute === '/ticket-verification' }">
                        <span class="nav-text">核销优惠券</span>
                    </RouterLink>

                </nav>
            </aside>


            <!-- 主内容区域 -->
            <main class="content-area">
                <RouterView />
            </main>
        </div>
    </div>
</template>

<style scoped>
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f9fafb;
    font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* Header 样式 */
.app-header {
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 0.5rem 2rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    position: relative;
}

.logo-container {
    position: absolute;
    width: 220px;
    left: 0;
}

.slogan {
    margin: 0 0 0 220px !important;
    width: calc(100% - 220px);
    height: 100%;
    position: absolute;
    font-size: 1.5rem;
    color: #37474f;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spacer {
    width: 70px;
    /* 与logo宽度相同，保持对称 */
}

.logo {
    height: 50px;
    width: auto;
    margin-right: 20px;
}

.slogan {
    margin: 0;
    font-size: 1.5rem;
    color: #37474f;
    font-weight: 500;
    flex-grow: 0;
}

/* 主布局样式 */
.main-layout {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    width: 100%;
}

/* 侧边栏样式 */
.sidebar {
    width: 220px;
    background-color: white;
    border-right: 1px solid #eaeaea;
    overflow-y: auto;
    z-index: 900;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    padding: 1.5rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #455a64;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: #f5f5f5;
    color: #e53935;
}

.nav-item.active {
    background-color: #f5f5f5;
    color: #e53935;
    border-left-color: #e53935;
}

.nav-text {
    margin-left: 0.5rem;
}

/* 用户菜单样式 */
.user-menu {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.username {
    font-size: 14px;
    color: #606266;
    margin-right: 15px;
}

.logout-btn {
    background-color: #f56c6c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.logout-btn:hover {
    background-color: #f78989;
}

/* 内容区域样式 */
.content-area {
    flex-grow: 1;
    padding: 0;
    min-height: calc(100vh - 70px);
    min-width: calc(100vw - 280px);
    background-color: #f5f7fa;
}

@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }

    .nav-text {
        display: none;
    }

    .content-area {
        margin-left: 5px;
    }

    .slogan {
        display: none;
    }

    .user-menu {
        right: 10px;
    }

    .username {
        display: none;
    }

    .logout-btn {
        padding: 4px 8px;
        font-size: 12px;
    }
}
</style>
