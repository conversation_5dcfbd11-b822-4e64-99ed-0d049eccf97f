/* 全局样式 */

/* 表格样式调整 */
.el-table {
  font-size: 13px !important;
}

.el-table th {
  font-size: 13px !important;
  padding: 8px 0 !important;
}

.el-table td {
  padding: 6px 0 !important;
}

/* 表格内按钮样式调整 */
.el-table .el-button--small {
  padding: 6px 10px !important;
  font-size: 12px !important;
}

/* 表格内输入框样式调整 */
.el-table .el-input__inner {
  font-size: 13px !important;
  height: 30px !important;
  line-height: 30px !important;
}

/* 分页组件样式调整 */
.el-pagination {
  font-size: 13px !important;
  padding: 10px 5px !important;
}

/* 表单样式调整 */
.el-form-item__label {
  font-size: 14px !important;
}

/* 对话框样式调整 */
.el-dialog__title {
  font-size: 16px !important;
}

/* 卡片样式调整 */
.el-card__header {
  padding: 12px 15px !important;
}

/* 内容区域样式调整 */
.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 20px;
  margin: 0;
}

/* 表格操作列样式 */
.operation-column {
  width: 150px !important;
}

/* 表格内容列样式 */
.el-table .content-column {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 表格内容列样式 - 小列 */
.el-table .small-column {
  width: 80px !important;
}

/* 表格内容列样式 - 中列 */
.el-table .medium-column {
  width: 120px !important;
}

/* 表格内容列样式 - 大列 */
.el-table .large-column {
  width: 180px !important;
}

/* 表格内容列样式 - 超大列 */
.el-table .xlarge-column {
  width: 250px !important;
}

/* 表格内容列样式 - 自适应列 */
.el-table .flexible-column {
  min-width: 100px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .el-table {
    font-size: 12px !important;
  }
  
  .el-table th {
    font-size: 12px !important;
    padding: 6px 0 !important;
  }
  
  .el-table .small-column {
    width: 70px !important;
  }
  
  .el-table .medium-column {
    width: 100px !important;
  }
  
  .el-table .large-column {
    width: 150px !important;
  }
  
  .el-table .xlarge-column {
    width: 200px !important;
  }
}
