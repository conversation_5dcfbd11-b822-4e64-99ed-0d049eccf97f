import axios from '@/router/axios';

/**
 * 认证状态接口
 */
interface AuthStatus {
  authenticated: boolean;
  username?: string;
}

/**
 * 检查用户是否已登录
 * @returns Promise<AuthStatus> 认证状态对象
 */
export const checkAuth = async (): Promise<AuthStatus> => {
  // 首先检查本地存储中是否有 token
  const token = localStorage.getItem('admin-token');
  console.log('检查本地 token:', token ? '存在' : '不存在');

  // 如果没有 token，直接返回未登录状态
  if (!token) {
    return { authenticated: false };
  }

  // 有 token，请求服务器验证
  try {
    const apiPath = '/api/admin/check-auth';
    console.log(`发送请求到 ${apiPath}`);
    const response = await axios.get(apiPath, { withCredentials: true });
    console.log('认证检查响应:', response.data);

    // 如果服务器返回未认证，清除本地 token
    if (!response.data.authenticated) {
      localStorage.removeItem('admin-token');
      return { authenticated: false };
    }

    return {
      authenticated: response.data.authenticated === true,
      username: response.data.username
    };
  } catch (error: any) {
    console.error('认证检查失败:', error);

    // 如果是 401 错误，清除 token
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('admin-token');
    }

    return { authenticated: false };
  }
};

/**
 * 管理员登录
 * @param username 用户名
 * @param password 密码
 * @returns Promise 登录结果
 */
export const login = async (username: string, password: string) => {
  try {
    const apiPath = '/api/admin/login';
    console.log(`发送登录请求到 ${apiPath}`);
    const response = await axios.post(apiPath, { username, password }, { withCredentials: true });
    console.log('登录响应:', response.data);

    // 如果登录成功，设置 token
    if (response.data.success) {
      // 使用服务器返回的用户名创建 token
      localStorage.setItem('admin-token', `server-token-${response.data.username}`);
      console.log('登录成功，设置 token');

      // 修正重定向 URL，确保不会出现双重路径
      if (response.data.redirectUrl && response.data.redirectUrl.startsWith('/admin')) {
        response.data.redirectUrl = '/';
      }
    }

    return response.data;
  } catch (error: any) {
    console.error('登录失败:', error);
    throw error;
  }
};

/**
 * 管理员登出
 * @returns Promise 登出结果
 */
export const logout = async () => {
  // 先清除本地存储中的 token，无论是否开发模式
  localStorage.removeItem('admin-token');
  console.log('已清除本地 token');

  // 如果是开发模式，直接返回成功
  if (import.meta.env.DEV) {
    console.log('开发模式下登出成功');
    return {
      success: true,
      message: '登出成功',
      redirectUrl: '/login'
    };
  }

  // 非开发模式，请求服务器
  try {
    const apiPath = '/api/admin/logout';
    console.log(`发送登出请求到 ${apiPath}`);
    const response = await axios.post(apiPath, {}, { withCredentials: true });
    console.log('登出响应:', response.data);

    // 修正重定向 URL，确保不会出现双重路径
    if (response.data.redirectUrl && response.data.redirectUrl.startsWith('/admin')) {
      response.data.redirectUrl = '/login';
    }

    return response.data;
  } catch (error: any) {
    console.error('登出失败:', error);
    // 即使出错也返回一个成功的响应，因为我们已经清除了本地 token
    return {
      success: true,
      message: '登出成功（尽管服务器请求失败）',
      redirectUrl: '/login'
    };
  }
};
