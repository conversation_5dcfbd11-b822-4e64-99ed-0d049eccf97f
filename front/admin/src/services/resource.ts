import axios from 'axios';

// 资源管理API
export const getResources = async (params: any) => {
  try {
    const response = await axios.get('/api/admin/resources', { params });
    return response.data;
  } catch (error) {
    console.error('获取资源列表失败:', error);
    throw error;
  }
};

export const getResource = async (id: number) => {
  try {
    const response = await axios.get(`/api/admin/resources/${id}`);
    return response.data;
  } catch (error) {
    console.error(`获取资源 ${id} 失败:`, error);
    throw error;
  }
};

export const createResource = async (resourceData: any) => {
  try {
    const response = await axios.post('/api/admin/resources', resourceData);
    return response.data;
  } catch (error) {
    console.error('创建资源失败:', error);
    throw error;
  }
};

export const updateResource = async (id: number, resourceData: any) => {
  try {
    const response = await axios.put(`/api/admin/resources/${id}`, resourceData);
    return response.data;
  } catch (error) {
    console.error(`更新资源 ${id} 失败:`, error);
    throw error;
  }
};

export const deleteResource = async (id: number) => {
  try {
    const response = await axios.delete(`/api/admin/resources/${id}`);
    return response.data;
  } catch (error) {
    console.error(`删除资源 ${id} 失败:`, error);
    throw error;
  }
};
