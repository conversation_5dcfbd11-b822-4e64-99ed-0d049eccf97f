import { checkAuth } from "@/services/auth";
import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
    //import.meta.env.BASE_URL
    history: createWebHistory('/admin'),
    routes: [
        {
            path: '/',
            name: 'home',
            component: () => import('../views/customers/Management.vue')
        },
        {
            path: '/customers',
            name: 'customers',
            component: () => import('../views/customers/Management.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/registry',
            name: 'registry',
            component: () => import('../views/courses/RegistryManagement.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/messages',
            name: 'messages',
            component: () => import('../views/messages/MessageManagement.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/resources',
            name: 'resources',
            component: () => import('../views/resources/ResourceManagement.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/whitelist',
            name: 'resource-whitelist',
            component: () => import('../views/resources/ResourceWhitelistManagement.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/ticket-verification',
            name: 'ticket-verification',
            component: () => import('../views/tickets/TicketVerification.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('../views/LoginView.vue'),
            meta: { requiresGuest: true }
        }
    ]
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
    console.log(`路由导航到: ${to.path}`);

    // 检查路由是否需要认证
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
    const requiresGuest = to.matched.some(record => record.meta.requiresGuest);

    // 如果是登录页面，不需要检查认证状态
    if (to.name === 'login') {
        // 如果有 token，先检查是否有效
        const token = localStorage.getItem('admin-token');
        if (token) {
            try {
                const authStatus = await checkAuth();
                if (authStatus.authenticated) {
                    // 如果已登录，重定向到首页
                    console.log('已登录用户不应访问登录页面，重定向到首页');
                    return next({ name: 'home' });
                }
            } catch (error) {
                // 如果检查失败，清除 token
                localStorage.removeItem('admin-token');
            }
        }
        // 允许访问登录页面
        return next();
    }

    // 如果路由需要认证
    if (requiresAuth) {
        try {
            // 检查用户是否已登录
            const authStatus = await checkAuth();
            console.log('认证状态:', authStatus);

            if (!authStatus.authenticated) {
                // 需要认证但未登录，重定向到登录页面
                console.log('需要认证但未登录，重定向到登录页面');
                return next({ name: 'login' });
            }

            // 已登录，允许访问
            return next();
        } catch (error) {
            console.error('路由守卫中检查认证状态失败:', error);
            // 出错时，重定向到登录页面
            return next({ name: 'login' });
        }
    }

    // 其他情况正常导航
    console.log('正常导航');
    next();
});

export default router
