import axios from 'axios';

const instance = axios.create({
  baseURL: '',
  headers: {
    'Content-Type': 'application/json'
  }
})

instance.interceptors.request.use(
  config => {
    console.log(`axios ${config.method} ${config.url}`);
    config.withCredentials = true;
    return config;
  }, error => {
    return Promise.reject(error);
  });

instance.interceptors.response.use(
  response => {
    console.info(response)
    return response;
  },
  error => {
    console.error('Axios error:', error);

    // 检查是否是401未授权错误
    if (error.response && error.response.status === 401) {
      console.log('Unauthorized, redirecting to login page');

      // 如果当前不在登录页面，则重定向到登录页面
      if (!window.location.href.includes('/login')) {
        // 使用路由器导航到登录页面
        const router = window._vueRouter;
        if (router) {
          router.push('/login');
        } else {
          window.location.href = '/admin/login';
        }
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
