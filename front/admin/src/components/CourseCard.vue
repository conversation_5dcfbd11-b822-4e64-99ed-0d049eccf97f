<template>
  <el-card class="course-card" shadow="hover">
    <div class="card-image" v-if="course.img">
      <img :src="course.img" :alt="course.title" />
    </div>
    <div class="card-image placeholder" v-else>
      <el-icon>
        <Picture />
      </el-icon>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ course.title }}</h3>
      <div class="card-info">
        <div class="info-item" v-if="course.level">
          <el-icon>
            <Histogram />
          </el-icon>
          <span>{{ course.level }}</span>
        </div>
        <div class="info-item" v-if="course.chapters">
          <el-icon>
            <Reading />
          </el-icon>
          <span>{{ course.chapters }} 章节</span>
        </div>
        <div class="info-item" v-if="course.video_length">
          <el-icon>
            <VideoPlay />
          </el-icon>
          <span>{{ course.video_length }}</span>
        </div>
        <div class="info-item" v-if="course.price">
          <el-icon>
            <Money />
          </el-icon>
          <span>{{ course.price }}</span>
        </div>
      </div>
      <div class="card-description" v-if="course.description">
        <p>{{ course.description }}</p>
      </div>
      <div class="card-actions" v-if="course.buy_link">
        <el-button type="primary" size="small" @click="openBuyLink">立即购买</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { Picture, Histogram, Reading, VideoPlay, Money } from '@element-plus/icons-vue';

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
});

const openBuyLink = () => {
  if (props.course.buy_link) {
    window.open(props.course.buy_link, '_blank');
  }
};
</script>

<style scoped>
.course-card {
  margin-bottom: 20px;
  transition: transform 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  transform: translateY(-5px);
}

.card-image {
  height: 150px;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-image.placeholder {
  color: #909399;
}

.card-image.placeholder .el-icon {
  font-size: 48px;
}

.card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-info {
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.info-item .el-icon {
  margin-right: 5px;
  font-size: 16px;
}

.card-description {
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 10px;
  flex-grow: 1;
}

.card-actions {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-image {
    height: 120px;
  }
}
</style>
