<template>
    <div class="websocket-message" :class="{ 'show': messages.length > 0 }">
        <div class="message-container">
            <div v-for="(group, groupId) in messageGroups" :key="groupId" class="message-group"
                @dblclick="removeGroup(groupId as string)">
                <div class="group-header">
                    <span class="group-title">{{ getGroupTitle(groupId as string) }}</span>
                    <span class="close-btn" @click.stop="removeGroup(groupId as string)">&times;</span>
                </div>
                <div class="logs-console" ref="logsConsole">
                    <div v-for="(log, index) in group" :key="index" :class="['log-entry']">
                        <span class="log-time">{{ log.timestamp }}</span>
                        <span class="log-message">{{ log.message }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { DateTime } from 'luxon';
import { nextTick, onBeforeUnmount, ref, watch } from 'vue';

interface LogMessage {
    message: string;
    timestamp: string;
}

interface MessageGroups {
    [key: string]: LogMessage[];
}

const props = defineProps<{
    eventId?: string;
}>();

const messages = ref<LogMessage[]>([]);
const messageGroups = ref<Record<string, LogMessage[]>>({}); const websocket = ref<WebSocket | null>(null);
const isConnected = ref<boolean>(false);
const reconnectTimeout = ref<number | null>(null);
const logsConsole = ref<HTMLElement[]>([]);

// 监听eventId变化，连接或断开WebSocket
watch(() => props.eventId, (newEventId, oldEventId) => {
    // 如果有旧的eventId，先关闭之前的连接
    if (oldEventId && websocket.value) {
        closeWebSocketConnection();
    }

    // 如果有新的eventId，建立新的连接
    if (newEventId) {
        connectWebSocket(newEventId);
    }
}, { immediate: true });

// 连接WebSocket
const connectWebSocket = (eventId: string): void => {
    // 关闭之前的连接
    if (websocket.value && isConnected.value) {
        websocket.value.close();
    }

    // 如果这个eventId还没有消息组，创建一个
    if (!messageGroups.value[eventId]) {
        messageGroups.value[eventId] = [];
    }

    // 创建新的WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsUrl = `${protocol}//${host}/api/admin/ws?event_id=${eventId}`;

    console.log(`正在连接WebSocket: ${wsUrl}`);

    // 重连相关变量
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 3;
    const reconnectInterval = 3000; // 3秒

    // 创建WebSocket连接的函数
    const createConnection = () => {
        try {
            websocket.value = new WebSocket(wsUrl);

            // 连接建立时的处理
            websocket.value.onopen = () => {
                isConnected.value = true;
                reconnectAttempts = 0; // 重置重连计数
                addMessage(eventId, {
                    message: '已连接到服务',
                    timestamp: DateTime.now().toFormat('HH:mm:ss')
                });
            };

            // 连接超时处理
            const connectionTimeout = setTimeout(() => {
                if (!isConnected.value && websocket.value) {
                    addMessage(eventId, {
                        message: '连接服务超时，尝试重新连接...',
                        timestamp: DateTime.now().toFormat('HH:mm:ss')
                    });
                    websocket.value.close();
                    // 超时后自动尝试重连
                    attemptReconnect();
                }
            }, 10000); // 10秒超时

            // 接收消息的处理
            websocket.value.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    addMessage(eventId, data);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                    addMessage(eventId, {
                        message: '解析状态信息失败',
                        timestamp: DateTime.now().toFormat('HH:mm:ss')
                    });
                }
            };

            // 连接关闭时的处理
            websocket.value.onclose = (event) => {
                clearTimeout(connectionTimeout);
                isConnected.value = false;

                console.log(`WebSocket连接关闭，代码: ${event.code}，原因: ${event.reason || '未知'}`);

                // 非正常关闭且未达到最大重连次数时尝试重连
                if (event.code !== 1000 && event.code !== 1001) {
                    attemptReconnect();
                } else {
                    addMessage(eventId, {
                        message: '服务连接已关闭',
                        timestamp: DateTime.now().toFormat('HH:mm:ss')
                    });
                }
            };

            // 连接错误时的处理
            websocket.value.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
                addMessage(eventId, {
                    message: '服务连接错误，正在尝试重新连接...',
                    timestamp: DateTime.now().toFormat('HH:mm:ss')
                });
                // 错误发生时不立即重连，让onclose事件处理重连
            };
        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            addMessage(eventId, {
                message: '无法连接到服务，正在尝试重新连接...',
                timestamp: DateTime.now().toFormat('HH:mm:ss')
            });
            // 连接创建失败时尝试重连
            attemptReconnect();
        }
    };

    // 尝试重新连接
    const attemptReconnect = () => {
        if (reconnectAttempts >= maxReconnectAttempts) {
            addMessage(eventId, {
                message: `重连失败，已达到最大重试次数(${maxReconnectAttempts}次)`,
                timestamp: DateTime.now().toFormat('HH:mm:ss')
            });
            return;
        }

        reconnectAttempts++;

        addMessage(eventId, {
            message: `正在尝试第${reconnectAttempts}次重新连接...`,
            timestamp: DateTime.now().toFormat('HH:mm:ss')
        });

        // 清除之前的重连定时器
        if (reconnectTimeout.value) {
            clearTimeout(reconnectTimeout.value);
        }

        // 设置新的重连定时器
        reconnectTimeout.value = setTimeout(() => {
            console.log(`尝试第${reconnectAttempts}次重新连接WebSocket...`);
            createConnection();
        }, reconnectInterval * reconnectAttempts) as unknown as number; // 重连间隔随重试次数增加
    };

    // 初始连接
    createConnection();
};

// 关闭WebSocket连接
const closeWebSocketConnection = (): void => {
    // 清除所有重连定时器
    if (typeof reconnectTimeout.value === 'number') {
        clearTimeout(reconnectTimeout.value);
        reconnectTimeout.value = null;
    }

    if (websocket.value) {
        try {
            websocket.value.close();
        } catch (error) {
            console.error('关闭WebSocket连接时发生错误:', error);
        } finally {
            websocket.value = null;
            isConnected.value = false;
        }
    }
};

// 添加消息到指定的消息组
const addMessage = (groupId: string, message: LogMessage): void => {
    if (!messageGroups.value[groupId]) {
        messageGroups.value[groupId] = [];
    }
    messageGroups.value[groupId].push(message);

    // 确保DOM更新后滚动到最新消息
    nextTick(() => {
        scrollToLatestMessage();
    });
};

// 滚动到最新消息
const scrollToLatestMessage = (): void => {
    if (logsConsole.value && logsConsole.value.length > 0) {
        logsConsole.value.forEach(console => {
            if (console) {
                console.scrollTop = console.scrollHeight;
            }
        });
    }
};

// 移除消息组
const removeGroup = (groupId: string): void => {
    if (messageGroups.value[groupId]) {
        delete messageGroups.value[groupId];
    }
};

// 获取消息组标题
const getGroupTitle = (groupId: string): string => {
    return `事件 ${groupId.substring(0, 8)}...`;
};

// 组件销毁前关闭WebSocket连接
onBeforeUnmount(() => {
    closeWebSocketConnection();
});
</script>

<style scoped>
.websocket-message {
    width: 100%;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
    z-index: 1000;
    padding: 0 20px;
}

.websocket-message.show {
    max-height: 400px;
    border-top: 1px solid #444;
}

.message-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.message-group {
    flex: 1;
    min-width: 300px;
    background-color: #222;
    color: wheat !important;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #333;
    border-bottom: 1px solid #444;
}

.group-title {
    font-weight: bold;
    font-size: 14px;
}

.close-btn {
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
}

.logs-console {
    height: 200px;
    overflow-y: auto;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 4px;
    display: flex;
}

.log-time {
    color: #888;
    margin-right: 8px;
    flex-shrink: 0;
}

.log-message {
    word-break: break-word;
}

.log-entry.info .log-message {
    color: #8bc34a;
}

.log-entry.error .log-message {
    color: #f44336;
}

.log-entry.warning .log-message {
    color: #ff9800;
}

.log-entry.success .log-message {
    color: #4caf50;
}
</style>
