<template>
    <div class="recommended-courses">
        <h2 class="section-title">推荐课程</h2>
        <div v-if="loading" class="loading-container">
            <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="courses.length === 0" class="empty-container">
            <el-empty description="暂无推荐课程" />
        </div>
        <div v-else class="course-list">
            <CourseCard v-for="course in courses" :key="course.id" :course="course" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import CourseCard from './CourseCard.vue';

const props = defineProps({
    limit: {
        type: Number,
        default: 4
    },
    excludeIds: {
        type: Array,
        default: () => []
    }
});

const courses = ref([]);
const loading = ref(false);

const fetchRecommendedCourses = async () => {
    loading.value = true;
    try {
        // 这里假设后端有一个获取推荐课程的API
        // 实际使用时需要根据后端API进行调整
        const response = await axios.get('/api/admin/resource-categories', {
            params: {
                limit: props.limit,
                exclude_ids: props.excludeIds.join(',')
            }
        });

        // 假设API返回的是类别列表，我们可以将其用作课程
        courses.value = response.data.categories.slice(0, props.limit);
    } catch (error) {
        console.error('获取推荐课程失败:', error);
        ElMessage.error('获取推荐课程失败');
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchRecommendedCourses();
});
</script>

<style scoped>
.recommended-courses {
    margin: 30px 0;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    position: relative;
    padding-left: 15px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 20px;
    background-color: #409eff;
    border-radius: 2px;
}

.loading-container,
.empty-container {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}
</style>
