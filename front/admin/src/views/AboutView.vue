<template>
  <div class="about">
    <div class="card-wrapper"><img class="card" src="@/assets/24lecture-main.png"></div>
    <div class="card-wrapper"><img class="card" src="@/assets/fa-main.png"></div>
    <div class="card-wrapper"><img class="card" src="@/assets/numpy-main.png"></div>
  </div>
</template>

<style>
.about {
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  /* 关键修复点1 */
  overflow-x: hidden;
}

.card {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.card-wrapper {
  width: 100%;
  margin: 10px 0;
  box-sizing: border-box;
}

@media (min-width: 1024px) {
  .about {
    position: fixed;
    right: 0;
    top: 0;
    width: 50%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
    box-sizing: border-box;
    background: white;
    z-index: 100;
  }
}
</style>
