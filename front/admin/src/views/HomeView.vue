<template>
    <div class="home">
        <h1>欢迎使用 QuantIDE</h1>
        <p>这是一个强大的量化交易开发环境</p>
        <div class="features">
            <div class="feature-card">
                <h3>用户管理</h3>
                <p>管理系统用户和权限</p>
                <RouterLink to="/customers" class="btn">进入管理</RouterLink>
            </div>

            <div class="feature-card">
                <h3>课程注册</h3>
                <p>管理所有课程注册记录</p>
                <RouterLink to="/registry" class="btn">查看注册</RouterLink>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 首页组件逻辑
</script>

<style scoped>
.home {
    padding: 40px 20px;
    text-align: center;
}

h1 {
    font-size: 2.5rem;
    color: #37474f;
    margin-bottom: 20px;
}

p {
    color: #546e7a;
    font-size: 1.2rem;
    margin-bottom: 40px;
}

.features {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.feature-card {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
    color: #1976d2;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.feature-card p {
    color: #607d8b;
    margin-bottom: 25px;
    font-size: 1rem;
}

.btn {
    display: inline-block;
    background-color: #1976d2;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.btn:hover {
    background-color: #1565c0;
}
</style>
