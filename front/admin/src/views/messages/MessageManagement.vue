<template>
    <div class="message-management">
        <div class="page-header">
            <h1>消息管理</h1>
            <el-button type="primary" @click="openCreateDialog">
                <el-icon>
                    <Plus />
                </el-icon>
                新建消息
            </el-button>
        </div>

        <!-- 消息列表 -->
        <el-card class="message-list-card">
            <template #header>
                <div class="card-header">
                    <span>消息列表</span>
                    <div class="header-actions">
                        <el-input v-model="searchQuery" placeholder="搜索消息..." prefix-icon="Search" clearable
                            @clear="handleSearch" @input="handleSearch" style="width: 250px" />
                        <el-button @click="refreshMessages">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                            刷新
                        </el-button>
                    </div>
                </div>
            </template>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <el-skeleton :rows="5" animated />
            </div>

            <!-- 消息表格 -->
            <el-table v-else :data="filteredMessages" style="width: 100%" border stripe
                :empty-text="messages.length === 0 ? '暂无消息' : '没有匹配的消息'">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="title" label="标题" min-width="150" />
                <el-table-column prop="content" label="内容" min-width="250" show-overflow-tooltip />
                <el-table-column label="接收者" min-width="120">
                    <template #default="scope">
                        <el-tag>{{ getRecipientName(scope.row.recipient_id) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.is_read ? 'success' : 'warning'">
                            {{ scope.row.is_read ? '已读' : '未读' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" min-width="180">
                    <template #default="scope">
                        {{ formatDate(scope.row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="scope">
                        <el-button-group>
                            <el-button type="primary" size="small" @click="editMessage(scope.row)">
                                <el-icon>
                                    <Edit />
                                </el-icon>
                                编辑
                            </el-button>
                            <el-button type="danger" size="small" @click="deleteMessage(scope.row)">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                                删除
                            </el-button>
                        </el-button-group>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                    :total="totalMessages" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </el-card>

        <!-- 创建/编辑消息对话框 -->
        <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑消息' : '创建新消息'" width="600px" destroy-on-close>
            <!-- 简化版表单，不使用 el-form -->
            <div class="simple-form">
                <div class="form-item">
                    <label>标题</label>
                    <input type="text" v-model="messageForm.title" placeholder="请输入消息标题" class="form-input" />
                </div>

                <div class="form-item">
                    <label>内容</label>
                    <textarea v-model="messageForm.content" placeholder="请输入消息内容" rows="5"
                        class="form-textarea"></textarea>
                </div>

                <!-- 移除接收者选择，默认发送给所有人 -->
                <div class="info-text">
                    <div class="alert-info">
                        <span>消息将发送给所有用户</span>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" :loading="submitting" @click="submitMessage">
                        {{ isEditMode ? '保存修改' : '创建消息' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import axios from '@/router/axios';
import { Delete, Edit, Plus, Refresh, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { DateTime } from 'luxon';
import { computed, onMounted, ref } from 'vue';

// 状态变量
const loading = ref(true);
const messages = ref([]);
const customers = ref([]);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const totalMessages = ref(0);
const dialogVisible = ref(false);
const isEditMode = ref(false);
const submitting = ref(false);
const editingMessageId = ref(null);

// 消息表单
const messageForm = ref({
    title: '',
    content: '',
    recipient_id: null
});

// 移除表单验证规则，只在提交时进行简单检查

// 计算属性：过滤后的消息列表
const filteredMessages = computed(() => {
    if (!searchQuery.value) {
        return messages.value;
    }

    const query = searchQuery.value.toLowerCase();
    return messages.value.filter(message =>
        message.title.toLowerCase().includes(query) ||
        message.content.toLowerCase().includes(query)
    );
});

// 获取消息列表
const fetchMessages = async () => {
    try {
        loading.value = true;
        const response = await axios.get('/api/admin/messages', {
            params: {
                page: currentPage.value,
                pageSize: pageSize.value
            }
        });

        messages.value = response.data.messages;
        totalMessages.value = response.data.total;
    } catch (error) {
        console.error('获取消息列表失败:', error);
        ElMessage.error('获取消息列表失败');
    } finally {
        loading.value = false;
    }
};

// 获取客户列表
const fetchCustomers = async () => {
    try {
        const response = await axios.get('/api/admin/customers');
        customers.value = response.data;
    } catch (error) {
        console.error('获取客户列表失败:', error);
        ElMessage.error('获取客户列表失败');
    }
};

// 根据ID获取接收者名称
const getRecipientName = (recipientId) => {
    const customer = customers.value.find(c => c.id === recipientId);
    return customer ? customer.nickname : `用户 ${recipientId}`;
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    return DateTime.fromISO(dateString).toFormat('yyyy-MM-dd HH:mm:ss');
};

// 打开创建消息对话框
const openCreateDialog = () => {
    isEditMode.value = false;
    editingMessageId.value = null;
    messageForm.value = {
        title: '',
        content: '',
        recipient_id: null
    };
    dialogVisible.value = true;
};

// 编辑消息
const editMessage = (message) => {
    isEditMode.value = true;
    editingMessageId.value = message.id;
    messageForm.value = {
        title: message.title,
        content: message.content,
        recipient_id: message.recipient_id
    };
    dialogVisible.value = true;
};

// 删除消息
const deleteMessage = async (message) => {
    try {
        await ElMessageBox.confirm(
            `确定要删除消息 "${message.title}" 吗？此操作不可恢复。`,
            '警告',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        await axios.delete(`/api/admin/messages/${message.id}`);
        ElMessage.success('消息已删除');
        refreshMessages();
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除消息失败:', error);
            ElMessage.error('删除消息失败');
        }
    }
};

// 提交消息表单
const submitMessage = async () => {
    try {
        submitting.value = true;

        // 简单验证
        if (!messageForm.value.title || !messageForm.value.title.trim()) {
            ElMessage.warning('请输入消息标题');
            return;
        }

        if (!messageForm.value.content || !messageForm.value.content.trim()) {
            ElMessage.warning('请输入消息内容');
            return;
        }

        if (isEditMode.value) {
            // 更新消息
            await axios.put(`/api/admin/messages/${editingMessageId.value}`, messageForm.value);
            ElMessage.success('消息已更新');
        } else {
            // 创建新消息，发送给所有人
            // 只需要发送一次请求，后端会自动处理发送给所有用户
            const data = {
                title: messageForm.value.title,
                content: messageForm.value.content,
                sender_id: 1 // 默认使用ID为1的管理员
            };

            // 发送请求
            const response = await axios.post('/api/admin/messages', data);

            // 检查响应
            if (response.data && response.data.status === 'success') {
                ElMessage.success(`消息已发送给 ${response.data.count || 0} 个用户`);
            } else {
                ElMessage.success('消息发送成功');
            }
        }

        dialogVisible.value = false;
        refreshMessages();
    } catch (error) {
        console.error('提交消息失败:', error);
        ElMessage.error('提交消息失败');
    } finally {
        submitting.value = false;
    }
};

// 刷新消息列表
const refreshMessages = () => {
    fetchMessages();
};

// 处理搜索
const handleSearch = () => {
    // 如果需要实时搜索，可以在这里添加逻辑
};

// 处理分页大小变化
const handleSizeChange = (size) => {
    pageSize.value = size;
    fetchMessages();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    currentPage.value = page;
    fetchMessages();
};

// 组件挂载时获取数据
onMounted(() => {
    fetchMessages();
    fetchCustomers();
});
</script>

<style scoped>
.message-management {
    padding: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h1 {
    margin: 0;
    font-size: 24px;
}

.message-list-card {
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.loading-container {
    padding: 20px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .header-actions {
        width: 100%;
    }

    .header-actions .el-input {
        width: 100% !important;
    }
}

/* 简化版表单样式 */
.simple-form {
    padding: 0 20px;
}

.form-item {
    margin-bottom: 20px;
}

.form-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #606266;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #409eff;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.alert-info {
    background-color: #f0f9eb;
    padding: 10px 15px;
    border-radius: 4px;
    border-left: 3px solid #67c23a;
    color: #67c23a;
    margin-top: 10px;
}
</style>
