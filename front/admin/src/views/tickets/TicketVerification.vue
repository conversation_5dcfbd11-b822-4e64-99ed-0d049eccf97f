<template>
    <div class="ticket-verification-container">
        <div class="page-header">
            <h1>核销优惠券</h1>
            <p>输入券码查看奖券信息并进行核销</p>
        </div>

        <div class="verification-form">
            <el-card>
                <div class="input-section">
                    <el-input v-model="ticketCode" placeholder="请输入5位券码" size="large" maxlength="5" show-word-limit
                        @keyup.enter="queryTicket" :disabled="loading">
                        <template #append>
                            <el-button type="primary" @click="queryTicket" :loading="loading">
                                查询
                            </el-button>
                        </template>
                    </el-input>
                </div>
            </el-card>
        </div>

        <!-- 奖券信息显示 -->
        <div v-if="ticketInfo" class="ticket-info">
            <el-card>
                <template #header>
                    <div class="card-header">
                        <span>奖券信息</span>
                        <el-tag :type="getStatusTagType(ticketInfo.status)">
                            {{ getStatusText(ticketInfo.status) }}
                        </el-tag>
                    </div>
                </template>

                <div class="ticket-details">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="券码">
                            <span class="ticket-code">{{ ticketInfo.ticket_code }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="课程">
                            {{ ticketInfo.course_title || ticketInfo.course }}
                        </el-descriptions-item>
                        <el-descriptions-item label="归属人账号">
                            {{ ticketInfo.owner_account }}
                        </el-descriptions-item>
                        <el-descriptions-item label="转让自" v-if="ticketInfo.transferred_from">
                            {{ ticketInfo.transferred_from }}
                        </el-descriptions-item>
                        <el-descriptions-item label="转让自" v-else>
                            <span class="empty-text">-</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="面值">
                            <span class="amount">{{ ticketInfo.discount }}元</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="可兑额">
                            <span class="amount">{{ ticketInfo.redeemable_amount }}元</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="有效期">
                            <span :class="{ 'expired': isExpired(ticketInfo.expires_at) }">
                                {{ formatDate(ticketInfo.expires_at) }}
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="最低限额">
                            {{ ticketInfo.base_fare }}元
                        </el-descriptions-item>
                        <el-descriptions-item label="最大叠加金额">
                            {{ ticketInfo.max_discount }}元
                        </el-descriptions-item>
                        <el-descriptions-item label="使用时间" v-if="ticketInfo.used_at">
                            {{ formatDateTime(ticketInfo.used_at) }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>

                <div class="action-section" v-if="canVerify(ticketInfo)">
                    <el-button type="danger" size="large" @click="verifyTicket" :loading="verifying"
                        :disabled="!canVerify(ticketInfo)">
                        核销此券
                    </el-button>
                </div>
            </el-card>
        </div>

        <!-- 空状态 -->
        <div v-if="!ticketInfo && !loading && searched" class="empty-state">
            <el-empty description="未找到相关奖券信息" />
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import axiosInstance from '@/services/axios';

// 响应式数据
const ticketCode = ref('');
const ticketInfo = ref(null);
const loading = ref(false);
const verifying = ref(false);
const searched = ref(false);

// 查询奖券信息
const queryTicket = async () => {
    if (!ticketCode.value || ticketCode.value.length !== 5) {
        ElMessage.warning('请输入5位券码');
        return;
    }

    try {
        loading.value = true;
        searched.value = true;

        const response = await axiosInstance.get(`/api/admin/lottery/ticket/${ticketCode.value}`);

        if (response.data.success) {
            ticketInfo.value = response.data.ticket;
        } else {
            ticketInfo.value = null;
            ElMessage.error(response.data.error || '查询失败');
        }
    } catch (error) {
        console.error('查询奖券失败:', error);
        ticketInfo.value = null;
        ElMessage.error(error.response?.data?.error || '查询失败');
    } finally {
        loading.value = false;
    }
};

// 核销奖券
const verifyTicket = async () => {
    try {
        await ElMessageBox.confirm(
            `确认核销券码 ${ticketCode.value} 吗？核销后此券将无法再次使用。`,
            '确认核销',
            {
                confirmButtonText: '确认核销',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        verifying.value = true;

        const response = await axiosInstance.post(`/api/admin/lottery/ticket/${ticketCode.value}/verify`);

        if (response.data.success) {
            ElMessage.success('核销成功');
            // 重新查询更新状态
            await queryTicket();
        } else {
            ElMessage.error(response.data.error || '核销失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('核销失败:', error);
            ElMessage.error(error.response?.data?.error || '核销失败');
        }
    } finally {
        verifying.value = false;
    }
};

// 工具函数
const getStatusTagType = (status) => {
    switch (status) {
        case 'unused': return 'success';
        case 'used': return 'info';
        case 'transferring': return 'warning';
        case 'transferred': return 'warning';
        default: return 'info';
    }
};

const getStatusText = (status) => {
    switch (status) {
        case 'unused': return '未使用';
        case 'used': return '已使用';
        case 'transferring': return '转让中';
        case 'transferred': return '已转让';
        default: return '未知状态';
    }
};

const canVerify = (ticket) => {
    return ticket && (ticket.status === 'unused' || ticket.status === 'transferred') && !isExpired(ticket.expires_at);
};

const isExpired = (expiresAt) => {
    return new Date(expiresAt) < new Date();
};

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

const formatDateTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};
</script>

<style scoped>
.ticket-verification-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h1 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 28px;
    font-weight: 600;
}

.page-header p {
    margin: 0;
    color: #606266;
    font-size: 16px;
}

.verification-form {
    margin-bottom: 30px;
}

.input-section {
    padding: 20px;
    text-align: center;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ticket-details {
    margin-bottom: 20px;
}

.ticket-code {
    font-family: monospace;
    font-size: 18px;
    font-weight: bold;
    color: #409EFF;
}

.amount {
    font-weight: bold;
    color: #E6A23C;
}

.expired {
    color: #F56C6C;
}

.empty-text {
    color: #C0C4CC;
}

.action-section {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #EBEEF5;
}

.empty-state {
    margin-top: 50px;
}
</style>
