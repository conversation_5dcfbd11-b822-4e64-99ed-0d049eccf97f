<template>
  <div class="registry-management">
    <div class="page-header">
      <h1>课程注册</h1>
      <div class="action-buttons">
        <el-button type="primary" @click="showAddRegistrationDialog = true">注册</el-button>
      </div>
      <div class="filter-container">
        <el-input v-model="accountSearchQuery" placeholder="按账号搜索" prefix-icon="Search" clearable
          @input="handleAccountSearch" @clear="handleAccountSearch" class="filter-item" />
        <el-select v-model="courseFilter" placeholder="课程筛选" clearable @change="handleFilter" class="filter-item">
          <el-option v-for="course in availableCourses" :key="course" :label="course" :value="course" />
        </el-select>
        <el-input v-model="planFilter" placeholder="套餐筛选" clearable @clear="handleFilter" @keyup.enter="handleFilter"
          class="filter-item" />
        <el-select v-model="activeFilter" placeholder="状态筛选" @change="handleFilter" class="filter-item">
          <el-option label="所有" :value="null" />
          <el-option label="正常" :value="true" />
          <el-option label="已删除" :value="false" />
        </el-select>
        <el-button type="primary" @click="handleFilter">筛选</el-button>
      </div>
    </div>

    <div class="content-container">
      <div v-if="loading" class="loading-state">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else>
        <el-table :data="registrations" style="width: 100%" border stripe size="small" v-loading="tableLoading"
          @sort-change="handleSortChange">
          <el-table-column prop="id" label="ID" width="40" />
          <el-table-column prop="course" label="课程" width="70" />
          <el-table-column prop="account" label="账号" width="100" sortable="custom" />
          <el-table-column prop="plan" label="套餐" width="100" sortable="custom" />

          <el-table-column label="注册时间" width="100" sortable="custom" prop="start_time">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'start_time'">
                <el-date-picker v-model="editingValue" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'start_time', scope.row.start_time)">
                {{ formatDate(scope.row.start_time) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="退款截止" width="100" sortable="custom" prop="refund_end">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'refund_end'">
                <el-date-picker v-model="editingValue" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'refund_end', scope.row.refund_end)">
                {{ formatDate(scope.row.refund_end) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="结课时间" width="100" sortable="custom" prop="prime_end">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'prime_end'">
                <el-date-picker v-model="editingValue" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'prime_end', scope.row.prime_end)">
                {{ formatDate(scope.row.prime_end) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="宽限期截止" width="120" sortable="custom" prop="grace_end">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'grace_end'">
                <el-date-picker v-model="editingValue" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'grace_end', scope.row.grace_end)">
                {{ formatDate(scope.row.grace_end) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="过期时间" width="100" sortable="custom" prop="expire_time">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'expire_time'">
                <el-date-picker v-model="editingValue" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'expire_time', scope.row.expire_time)">
                {{ formatDate(scope.row.expire_time) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="URL" width="100" prop="url">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'url'">
                <el-input v-model="editingValue" @blur="saveEdit(scope.row)" />
              </div>
              <div v-else @click="startEdit(scope.row, 'url', scope.row.url)">
                <a :href="scope.row.url" target="_blank" class="course-link">访问课程</a>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="80" sortable="custom" prop="is_active">
            <template #default="scope">
              <div v-if="editingCell.id === scope.row.id && editingCell.field === 'is_active'">
                <el-select v-model="editingValue" @blur="saveEdit(scope.row)">
                  <el-option :value="true" label="正常" />
                  <el-option :value="false" label="已删除" />
                </el-select>
              </div>
              <div v-else @click="startEdit(scope.row, 'is_active', scope.row.is_active)">
                <span :class="['status-badge', scope.row.is_active ? 'active' : 'expired']">
                  {{ scope.row.is_active ? '正常' : '已删除' }}
                </span>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
    </div>
  </div>

  <!-- 添加课程对话框 -->
  <div class="dialog-overlay" v-if="showAddRegistrationDialog" @click.self="closeAddRegistrationDialog">
    <div class="dialog">
      <div class="dialog-header">
        <h2>添加课程</h2>
        <button class="btn-close" @click="closeAddRegistrationDialog">&times;</button>
      </div>
      <div class="dialog-body">
        <form @submit.prevent="registerCourse">
          <div class="form-group">
            <label for="account">客户:</label>
            <div class="input-wrapper">
              <el-autocomplete v-model="newRegistration.account" :fetch-suggestions="queryCustomers"
                placeholder="请输入客户账号" @select="handleCustomerSelect" style="width: 100%" />
              <span class="required-mark">*</span>
            </div>
          </div>
          <div class="form-group">
            <label for="courseId">课程:</label>
            <select id="courseId" v-model="newRegistration.course" @change="loadPlans" required>
              <option value="" disabled selected>请选择课程</option>
              <option v-for="course in availableCoursesWithDisplay" :key="course.name" :value="course.name">
                {{ course.displayName }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="planId">计划:</label>
            <select id="planId" v-model="newRegistration.plan" required @change="onChangePlan">
              <option value="" disabled selected>请选择计划</option>
              <option v-for="plan in Object.keys(availablePlans)" :key="plan" :value="plan">
                {{ plan }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="startTime">注册时间:</label>
            <input type="date" id="startTime" v-model="startDateFormatted" required>
          </div>

          <div class="form-group">
            <label for="refundTime">可退款期 ({{ offset.refund }}天):</label>
            <input type="date" id="refundTime" v-model="refundTimeFormatted" min="0" required>
          </div>

          <div class="form-group">
            <label for="primeTime">结课时间 ({{ offset.prime }}天):</label>
            <input type="date" id="primeTime" v-model="primeTimeFormatted" min="0" required>
          </div>

          <div class="form-group">
            <label for="expireTime">结课后访问期 ({{ offset.afterPrime }}天):</label>
            <input type="date" id="expireTime" v-model="expireTimeFormatted" min="0" required>
          </div>

          <div class="dialog-footer">
            <button type="button" class="btn btn-secondary" @click="closeAddRegistrationDialog">取消</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              {{ isSubmitting ? '提交中...' : '添加' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>


  <!-- WebSocket消息组件 -->
  <WebSocketMessage :eventId="currentEventId" />
</template>

<script setup lang="ts">
import axios from '@/router/axios';
import { onMounted, ref, computed, provide } from 'vue';
import { ElMessage } from 'element-plus';
import { DateTime } from 'luxon';
import WebSocketMessage from '@/components/WebSocketMessage.vue';

const currentEventId = ref<string>('')

// 提供一个方法来设置当前的事件ID
const setEventId = (eventId: string) => {
  currentEventId.value = eventId
}

// 将方法通过provide提供给子组件
provide('setEventId', setEventId)

// 定义类型
interface Registration {
  id: number;
  course: string;
  customer_id: number;
  account: string;
  nickname: string;
  plan: string;
  start_time: string;
  refund_end: string;
  prime_end: string;
  grace_end: string;
  expire_time: string;
  url: string;
  is_active: boolean;
  [key: string]: any; // 允许索引访问
}

interface EditingCell {
  id: number | null;
  field: string;
}

interface SortColumn {
  prop?: string;
  order?: string;
}

// 数据加载状态
const loading = ref(true);
const tableLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 排序相关
const sortBy = ref('');
const sortOrder = ref('asc');

// 筛选相关
const courseFilter = ref('');
const planFilter = ref('');
const activeFilter = ref<boolean | null>(true); // 默认只显示正常状态
const accountSearchQuery = ref(''); // 添加账号搜索查询

// 可用课程列表
const availableCourses = ref<string[]>([]);
const availableCoursesWithDisplay = ref<{ name: string, displayName: string }[]>([]);

// 可用套餐列表
const availablePlans = ref<Record<string, any>>({});

// 注册对话框相关
const showAddRegistrationDialog = ref(false);
const isSubmitting = ref(false);
const customers = ref<{ id: number, account: string, nickname: string }[]>([]);
const selectedCustomerId = ref<number | null>(null);

// 新课程注册表单
const newRegistration = ref({
  account: '',
  customer_id: 0,
  course: '',
  plan: '',
  startTime: DateTime.now(),
  refundTime: DateTime.now().plus({ days: 7 }),
  primeTime: DateTime.now().plus({ days: 30 }),
  expireTime: DateTime.now().plus({ days: 365 }),
});

// 计算属性：处理开始日期的双向绑定
const startDateFormatted = computed({
  get: () => {
    return newRegistration.value.startTime.toFormat('yyyy-MM-dd');
  },
  set: (value: string) => {
    const dt = DateTime.fromISO(value);
    if (dt.isValid) {
      newRegistration.value.startTime = dt;
      // 如果已选择计划，更新结束时间
      const plan = newRegistration.value.plan
      if (plan && availablePlans.value[plan]) {
        const selectedPlan = availablePlans.value[plan];
        newRegistration.value.expireTime = newRegistration.value.startTime.plus({ days: selectedPlan.expire });
        newRegistration.value.refundTime = newRegistration.value.startTime.plus({ days: selectedPlan.refund });
        newRegistration.value.primeTime = newRegistration.value.startTime.plus({ days: selectedPlan.prime });
      }
    }
  }
});

// 计算属性：处理结束日期的双向绑定
const expireTimeFormatted = computed({
  get: () => {
    return newRegistration.value.expireTime.toFormat('yyyy-MM-dd');
  },
  set: (value: string) => {
    const dt = DateTime.fromISO(value);
    if (dt.isValid) {
      newRegistration.value.expireTime = dt;
    }
  }
});

const refundTimeFormatted = computed({
  get: () => {
    return newRegistration.value.refundTime.toFormat('yyyy-MM-dd');
  },
  set: (value: string) => {
    const dt = DateTime.fromISO(value);
    if (dt.isValid) {
      newRegistration.value.refundTime = dt;
    }
  }
});

const primeTimeFormatted = computed({
  get: () => {
    return newRegistration.value.primeTime.toFormat('yyyy-MM-dd');
  },
  set: (value: string) => {
    const dt = DateTime.fromISO(value);
    if (dt.isValid) {
      newRegistration.value.primeTime = dt;
    }
  }
});

const offset = computed(() => {
  try {
    const startTime = newRegistration.value.startTime;
    const obj = newRegistration.value;
    return {
      refund: Math.round(obj.refundTime.diff(startTime, 'days').days),
      prime: Math.round(obj.primeTime.diff(startTime, 'days').days),
      afterPrime: Math.round(obj.expireTime.diff(obj.primeTime, 'days').days)
    };
  } catch (error) {
    console.error('计算日期差异时出错:', error);
    return { refund: 0, prime: 0, afterPrime: 0 };
  }
});

// 编辑相关
const editingCell = ref<EditingCell>({ id: null, field: '' });
const editingValue = ref<any>(null);

// 课程注册记录
const registrations = ref<Registration[]>([]);

// 获取所有课程注册记录
const fetchRegistrations = async () => {
  try {
    loading.value = true;

    // 构建请求参数
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      sort_by: sortBy.value,
      sort_order: sortOrder.value,
      course: courseFilter.value || undefined,
      plan: planFilter.value || undefined,
      include_inactive: activeFilter.value === null ? true : undefined,
      is_active: activeFilter.value === null ? undefined : activeFilter.value
    };

    const response = await axios.get('/api/admin/registrations', { params });
    registrations.value = response.data.registrations;
    total.value = response.data.total;
  } catch (error) {
    console.error('获取课程注册记录失败:', error);
    ElMessage.error('获取课程注册记录失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 获取可用课程列表
const fetchAvailableCourses = async () => {
  try {
    const response = await axios.get('/api/admin/courses/available');
    if (response.data && Array.isArray(response.data)) {
      availableCourses.value = response.data.map((course: any) => course.name);
      availableCoursesWithDisplay.value = response.data;
    }
  } catch (error) {
    console.error('获取可用课程列表失败:', error);
  }
};

// 获取客户列表
const fetchCustomers = async () => {
  try {
    const response = await axios.get('/api/admin/customers');
    if (response.data && Array.isArray(response.data)) {
      customers.value = response.data.map((customer: any) => ({
        id: customer.id,
        account: customer.account,
        nickname: customer.nickname
      }));
    }
  } catch (error) {
    console.error('获取客户列表失败:', error);
  }
};

// 查询客户账号
const queryCustomers = (query: string, callback: (data: any[]) => void) => {
  if (query) {
    const results = customers.value.filter(customer =>
      customer.account.toLowerCase().includes(query.toLowerCase()) ||
      customer.nickname.toLowerCase().includes(query.toLowerCase())
    ).map(customer => ({
      value: customer.account,
      label: `${customer.account} (${customer.nickname})`,
      id: customer.id
    }));
    callback(results);
  } else {
    callback([]);
  }
};

// 处理客户选择
const handleCustomerSelect = (item: any) => {
  selectedCustomerId.value = item.id;
  newRegistration.value.customer_id = item.id;
};

// 加载课程对应的套餐
const loadPlans = async () => {
  if (!newRegistration.value.course) return;

  try {
    const response = await axios.get(`/api/admin/courses/plans/${newRegistration.value.course}`);
    availablePlans.value = response.data;

    // 重置套餐选择
    newRegistration.value.plan = '';
  } catch (error) {
    console.error('获取课程套餐失败:', error);
    ElMessage.error('获取课程套餐失败');
  }
};

// 处理套餐变化
const onChangePlan = () => {
  if (!newRegistration.value.plan || !availablePlans.value) return;

  const plan = availablePlans.value[newRegistration.value.plan];
  if (plan) {
    const startTime = newRegistration.value.startTime;
    newRegistration.value.refundTime = startTime.plus({ days: plan.refund });
    const primeTime = startTime.plus({ days: plan.prime });

    newRegistration.value.primeTime = primeTime
    newRegistration.value.expireTime = primeTime.plus({ days: plan.expire });
  }
};

// 关闭添加课程注册对话框
const closeAddRegistrationDialog = () => {
  showAddRegistrationDialog.value = false;
  resetRegistrationForm();
};

// 重置注册表单
const resetRegistrationForm = () => {
  newRegistration.value = {
    account: '',
    customer_id: 0,
    course: '',
    plan: '',
    startTime: DateTime.now(),
    refundTime: DateTime.now().plus({ days: 7 }),
    primeTime: DateTime.now().plus({ days: 30 }),
    expireTime: DateTime.now().plus({ days: 365 }),
  };
  selectedCustomerId.value = null;
  availablePlans.value = {};
};

// 处理页码变化
const handleCurrentChange = (newPage: number): void => {
  currentPage.value = newPage;
  fetchRegistrations();
};

// 处理每页显示数量变化
const handleSizeChange = (newSize: number): void => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
  fetchRegistrations();
};

// 处理排序变化
const handleSortChange = (column: SortColumn): void => {
  if (column.prop) {
    sortBy.value = column.prop;
    sortOrder.value = column.order === 'ascending' ? 'asc' : 'desc';
  } else {
    sortBy.value = '';
    sortOrder.value = 'asc';
  }
  fetchRegistrations();
};

// 处理筛选
const handleFilter = (): void => {
  currentPage.value = 1; // 重置到第一页
  accountSearchQuery.value = ''; // 清空账号搜索
  fetchRegistrations();
};

// 处理账号搜索
const handleAccountSearch = (): void => {
  // 如果有搜索查询，在前端筛选结果
  if (accountSearchQuery.value) {
    const query = accountSearchQuery.value.toLowerCase();

    // 先获取所有数据（不带筛选条件）
    axios.get('/api/admin/registrations', {
      params: {
        page: 1,
        page_size: 1000, // 获取足够多的数据以便在前端筛选
        include_inactive: activeFilter.value === null ? true : undefined,
        is_active: activeFilter.value === null ? undefined : activeFilter.value
      }
    }).then(response => {
      // 在前端筛选符合账号搜索条件的数据
      const allRegistrations = response.data.registrations;
      const filteredRegistrations = allRegistrations.filter((registration: Registration) =>
        registration.account.toLowerCase().includes(query)
      );

      // 更新显示的注册记录
      registrations.value = filteredRegistrations;
      total.value = filteredRegistrations.length;
    }).catch(error => {
      console.error('获取数据失败:', error);
      ElMessage.error('搜索失败，请重试');
    });
  } else {
    // 如果搜索查询为空，重新获取所有数据
    fetchRegistrations();
  }
};

// 开始编辑单元格
const startEdit = (row: Registration, field: string, value: any): void => {
  // 不允许编辑 course_id、id、customer_id、account 和 nickname
  if (field === 'course' || field === 'id' || field === 'customer_id' || field === 'account' || field === 'nickname') {
    return;
  }

  editingCell.value = { id: row.id, field };
  editingValue.value = value;
};

// 保存编辑
const saveEdit = async (row: Registration): Promise<void> => {
  if (editingCell.value.id === null || editingCell.value.field === '') {
    return;
  }

  try {
    tableLoading.value = true;

    // 构建更新数据
    const updateData = {
      [editingCell.value.field]: editingValue.value
    };

    // 发送更新请求
    await axios.put(`/api/admin/registrations/${row.id}`, updateData);

    // 更新本地数据
    const index = registrations.value.findIndex(item => item.id === row.id);
    if (index !== -1) {
      registrations.value[index][editingCell.value.field] = editingValue.value;
    }

    ElMessage.success('更新成功');
  } catch (error) {
    console.error('更新失败:', error);
    ElMessage.error('更新失败，请重试');
  } finally {
    // 重置编辑状态
    editingCell.value = { id: null, field: '' };
    editingValue.value = null;
    tableLoading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  // 使用更紧凑的格式：YYYY-MM-DD
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 注册课程
const registerCourse = async () => {
  // 验证表单
  if (!newRegistration.value.account) {
    ElMessage.warning('请输入客户账号');
    return;
  }

  if (!newRegistration.value.customer_id) {
    ElMessage.warning('请选择有效的客户账号');
    return;
  }

  if (!newRegistration.value.course) {
    ElMessage.warning('请选择课程');
    return;
  }

  if (!newRegistration.value.plan) {
    ElMessage.warning('请选择套餐');
    return;
  }

  try {
    isSubmitting.value = true;

    // 构建请求数据
    try {
      const courseData = {
        customer_id: newRegistration.value.customer_id,
        course: newRegistration.value.course,
        plan: newRegistration.value.plan,
        start_time: newRegistration.value.startTime.toISO(),
        refund_end: newRegistration.value.refundTime.toISO(),
        prime_end: newRegistration.value.primeTime.toISO(),
        expire_time: newRegistration.value.expireTime.toISO()
      };

      // 发送注册请求
      const response = await axios.post('/api/admin/courses/register', courseData);

      // 关闭对话框
      closeAddRegistrationDialog();

      // 然后再设置eventId，触发WebSocketMessage组件接收消息
      if (response.data && response.data.container_id) {
        // 延迟一点时间再设置eventId，确保UI已经完全更新
        setTimeout(() => {
          setEventId(response.data.container_id);
          currentEventId.value = response.data.container_id
        }, 500);
      }

      // 重新加载课程注册列表
      await fetchRegistrations();

      ElMessage.success('课程注册成功');

      // 重置表单
      resetRegistrationForm();
    } catch (error) {
      console.error('课程注册失败:', error);

      // 提取具体的错误信息
      let errorMessage = '请重试';
      if (error && typeof error === 'object' && 'response' in error) {
        const response = error.response as any;
        if (response && typeof response === 'object' && 'data' in response) {
          if (response.data && typeof response.data === 'object' && 'error' in response.data) {
            errorMessage = response.data.error;
          } else if (response.data && typeof response.data === 'object' && 'message' in response.data) {
            errorMessage = response.data.message;
          }
        }
      }

      ElMessage.error(`课程注册失败：${errorMessage}`);
    } finally {
      isSubmitting.value = false;
    }
  } catch (error) {
    console.error('注册课程时出错:', error);
    isSubmitting.value = false;
  }
};

onMounted(async () => {
  await Promise.all([
    fetchAvailableCourses(),
    fetchCustomers()
  ]);
  fetchRegistrations();
});
</script>

<style scoped>
.registry-management {
  padding: 0 20px;
  max-width: 100%;
  overflow-x: hidden;
}

.page-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeaea;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
  margin-right: 20px;
}

h1 {
  margin: 0;
  color: #37474f;
  font-size: 1.8rem;
  font-weight: 500;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.filter-item {
  width: 150px;
  margin-right: 10px;
}

/* 账号搜索输入框样式 */
.filter-container .el-input:first-child {
  width: 220px;
}

.content-container {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow-x: auto;
  max-width: 100%;
  position: relative;
}

/* 使表格更紧凑 */
:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table td),
:deep(.el-table th) {
  padding: 6px 0;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: #78909c;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #1976d2;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.expired {
  background-color: #ffebee;
  color: #c62828;
}

.course-link {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}

.course-link:hover {
  text-decoration: underline;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.dialog-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: #37474f;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #78909c;
  cursor: pointer;
}

.dialog-body {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-secondary {
  background-color: #eceff1;
  color: #455a64;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #455a64;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #e53935;
  margin-left: 5px;
}

select,
input[type="date"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cfd8dc;
  border-radius: 4px;
  font-size: 0.95rem;
  background-color: white;
}
</style>
