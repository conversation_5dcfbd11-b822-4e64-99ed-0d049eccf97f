<template>
  <div class="resource-whitelist-container">
    <div class="page-header">
      <h1>
        <el-icon>
          <el-icon-key />
        </el-icon> 资源授权管理
      </h1>
      <el-button type="primary" @click="openAddWhitelistDialog">
        <el-icon>
          <el-icon-plus />
        </el-icon> 添加授权
      </el-button>
    </div>

    <!-- 客户列表 -->
    <el-card class="card-with-shadow">
      <template #header>
        <div class="card-header">
          <span>
            <el-icon>
              <el-icon-user />
            </el-icon> 客户列表
          </span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <el-form :inline="true">
          <el-form-item label="客户账号">
            <el-input v-model="customerQuery" placeholder="输入客户账号进行精确匹配" clearable
              @keyup.enter="searchCustomerResources" prefix-icon="el-icon-search" style="width: 300px;" />
          </el-form-item>
          <el-form-item label="课程筛选">
            <el-select v-model="courseFilter" placeholder="选择课程" clearable style="width: 150px;"
              @change="applyCourseFilter">
              <el-option v-for="course in availableCourses" :key="course" :label="course" :value="course" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchCustomerResources" :loading="loading">
              <el-icon>
                <el-icon-search />
              </el-icon> 搜索客户资源
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table :data="customers" style="width: 100%" border stripe highlight-current-row v-loading="loading"
        element-loading-text="加载中..." class="custom-table" height="300px">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="account" label="账号" width="120" />
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="wx" label="微信" width="130" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewCustomerWhitelist(scope.row)" :icon="ElIconView" circle
              title="查看授权" />
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无客户数据" :image-size="100" />
        </template>
      </el-table>
    </el-card>

    <!-- 资源列表 -->
    <el-card class="card-with-shadow">
      <template #header>
        <div class="card-header">
          <span>
            <el-icon>
              <el-icon-files />
            </el-icon> 资源列表
          </span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-form">
        <el-form :inline="true">
          <el-form-item label="资源信息">
            <el-input v-model="resourceQuery" placeholder="输入资源标题、描述或路径" clearable @keyup.enter="searchResources"
              prefix-icon="el-icon-search" style="width: 300px;" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchResources" :loading="loading">
              <el-icon>
                <el-icon-search />
              </el-icon> 搜索
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table :data="resources" style="width: 100%" border stripe highlight-current-row v-loading="loading"
        element-loading-text="加载中..." class="custom-table" height="400px">
        <el-table-column label="ID" width="60">
          <template #default="scope">
            <div class="id-cell" :title="scope.row.is_public ? '资源已向所有人开放' : ''">
              {{ scope.row.id }}
              <span v-if="scope.row.is_public" class="public-star">*</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="course" label="课程" width="90" />
        <el-table-column prop="resource" label="资源类型" width="100" />
        <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip />
        <el-table-column prop="rel_path" label="路径" min-width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="selectResourceForWhitelist(scope.row)" :icon="ElIconKey"
              circle title="授权此资源" />
            <el-button type="info" size="small" @click="viewResourceCustomers(scope.row)" :icon="ElIconView" circle
              title="查看已授权客户" />
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无资源数据" :image-size="100" />
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination" v-if="resources.length > 0">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="totalResources" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" background />
      </div>
    </el-card>

    <!-- 资源授权管理对话框 -->
    <el-dialog v-model="addWhitelistDialogVisible" title="资源授权管理" width="600px" destroy-on-close>
      <!-- 资源信息 -->
      <div class="resource-info" v-if="selectedResourceForAuth">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="标题">{{ selectedResourceForAuth.title }}</el-descriptions-item>
          <el-descriptions-item label="课程">{{ selectedResourceForAuth.course }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="1">{{ selectedResourceForAuth.description
            }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 添加客户授权 -->
      <div class="add-customer-auth" style="margin-top: 20px;">
        <h3>添加客户授权</h3>
        <div style="display: flex; gap: 10px;">
          <el-select v-model="selectedCustomerId" filterable remote reserve-keyword placeholder="搜索并选择客户（账号或昵称）"
            :remote-method="searchCustomersForAuth" :loading="customerLoading" style="flex: 1;"
            @change="handleCustomerSelected" clearable>
            <el-option v-for="item in filteredCustomerOptions" :key="item.id"
              :label="`${item.account} (${item.nickname})`" :value="item.id" />
          </el-select>
          <el-button type="primary" @click="addCustomerAuth" :disabled="!canAddCustomer || resourceIsPublic">
            <el-icon><el-icon-plus /></el-icon> 添加
          </el-button>
        </div>
        <div v-if="resourceIsPublic" style="margin-top: 10px;">
          <el-alert title="资源已对所有人开放，无需添加特定客户授权" type="info" :closable="false" />
        </div>
      </div>

      <!-- 已授权客户列表 -->
      <div class="authorized-customers" style="margin-top: 20px;">
        <h3>已授权客户</h3>
        <el-alert v-if="resourceIsPublic" title="此资源已对所有人开放" type="success" :closable="false"
          style="margin-bottom: 10px;" />

        <div class="customer-list" v-loading="authCustomersLoading">
          <!-- 所有人选项 -->
          <div v-if="resourceIsPublic" class="customer-item">
            <span class="customer-name">所有人</span>
            <el-button type="danger" size="small" @click="removePublicAuth" :icon="ElIconDelete" circle
              title="取消对所有人开放" />
          </div>

          <!-- 特定客户列表 -->
          <div v-for="customer in authorizedCustomers" :key="customer.id" class="customer-item">
            <span class="customer-name">{{ customer.account }} ({{ customer.nickname }})</span>
            <el-button type="danger" size="small" @click="removeCustomerAuth(customer)" :icon="ElIconDelete" circle
              title="移除授权" />
          </div>

          <el-empty v-if="!resourceIsPublic && authorizedCustomers.length === 0 && !authCustomersLoading"
            description="暂无授权客户" />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addWhitelistDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="makeResourcePublic" v-if="!resourceIsPublic">
            <el-icon><el-icon-check /></el-icon> 对所有人开放
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 客户授权详情对话框 -->
    <el-dialog v-model="customerWhitelistDialogVisible" :title="`${selectedCustomer?.nickname || '客户'} 的资源授权`"
      width="700px">
      <div v-loading="customerWhitelistLoading">
        <div class="customer-info" v-if="selectedCustomer">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="账号">{{ selectedCustomer.account }}</el-descriptions-item>
            <el-descriptions-item label="昵称">{{ selectedCustomer.nickname }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ selectedCustomer.phone }}</el-descriptions-item>
            <el-descriptions-item label="微信">{{ selectedCustomer.wx }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="whitelist-resources">
          <h3>已授权资源 ({{ customerWhitelistResources.length }})</h3>
          <el-table :data="customerWhitelistResources" style="width: 100%" border stripe class="custom-table">
            <el-table-column label="ID" width="60">
              <template #default="scope">
                <div class="id-cell" :title="scope.row.is_public ? '资源已向所有人开放' : ''">
                  {{ scope.row.id }}
                  <span v-if="scope.row.is_public" class="public-star">*</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="course" label="课程" width="90" />
            <el-table-column prop="resource" label="资源类型" width="100" />
            <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="scope">
                <el-button type="danger" size="small" @click="removeWhitelist(scope.row)" :icon="ElIconDelete" circle
                  title="移除授权" />
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-if="customerWhitelistResources.length === 0 && !customerWhitelistLoading" description="暂无授权资源" />
        </div>
      </div>
    </el-dialog>

    <!-- 资源授权客户对话框 -->
    <el-dialog v-model="resourceCustomersDialogVisible" :title="`${selectedResource?.title || '资源'} 的授权客户`"
      width="700px">
      <div v-loading="resourceCustomersLoading">
        <div class="resource-info" v-if="selectedResource">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">{{ selectedResource.id }}</el-descriptions-item>
            <el-descriptions-item label="课程">{{ selectedResource.course }}</el-descriptions-item>
            <el-descriptions-item label="资源类型">{{ selectedResource.resource }}</el-descriptions-item>
            <el-descriptions-item label="标题">{{ selectedResource.title }}</el-descriptions-item>
          </el-descriptions>

          <!-- 显示公开状态 -->
          <el-alert v-if="resourceIsPublic" title="此资源已设置为对所有人免费开放" type="success" :closable="false"
            style="margin-top: 15px; margin-bottom: 15px;" />
        </div>

        <div class="whitelist-customers">
          <h3>已授权客户 ({{ resourceCustomers.length }})</h3>
          <el-table :data="resourceCustomers" style="width: 100%" border stripe class="custom-table">
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column prop="nickname" label="昵称" width="120" />
            <el-table-column prop="phone" label="手机号" width="120" />
            <el-table-column prop="wx" label="微信" min-width="120" />
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="scope">
                <el-button type="primary" size="small" @click="viewCustomerWhitelist(scope.row)" :icon="ElIconView"
                  circle title="查看客户授权" />
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-if="resourceCustomers.length === 0 && !resourceCustomersLoading" description="暂无授权客户" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  ref,
  reactive,
  onMounted,
  defineComponent
} from 'vue';
import {
  ElMessage,
  ElMessageBox
} from 'element-plus';
// 使用已配置的axios实例
import axiosInstance from '../../router/axios';
import {
  View as ElIconView,
  Key as ElIconKey,
  Delete as ElIconDelete,
  Search as ElIconSearch,
  Plus as ElIconPlus,
  Check as ElIconCheck,
  User as ElIconUser,
  Files as ElIconFiles
} from '@element-plus/icons-vue';

export default defineComponent({
  name: 'ResourceWhitelistManagement',
  components: {
    ElIconView,
    ElIconKey,
    ElIconDelete,
    ElIconSearch,
    ElIconPlus,
    ElIconCheck,
    ElIconUser,
    ElIconFiles
  },
  setup() {

    // 搜索参数
    const customerQuery = ref('');
    const resourceQuery = ref('');
    const courseFilter = ref('');
    const loading = ref(false);

    // 课程列表和筛选
    const availableCourses = ref<string[]>([]);
    const allCustomerResources = ref<any[]>([]);  // 存储客户的所有资源
    const currentCustomer = ref<any>(null);  // 当前查询的客户

    // 分页参数
    const currentPage = ref(1);
    const pageSize = ref(20);
    const totalResources = ref(0);

    // 搜索结果
    const customers = ref<any[]>([]);
    const resources = ref<any[]>([]);

    // 增加授权对话框
    const addWhitelistDialogVisible = ref(false);
    const customerOptions = ref<any[]>([]);
    const resourceOptions = ref<any[]>([]);
    const customerLoading = ref(false);
    const resourceLoading = ref(false);
    const submitting = ref(false);
    // 授权方式：public-所有人，specific-特定客户
    const authType = ref('public');

    // 客户授权详情对话框
    const customerWhitelistDialogVisible = ref(false);
    const selectedCustomer = ref<any>(null);
    const customerWhitelistResources = ref<any[]>([]);
    const customerWhitelistLoading = ref(false);

    // 资源授权客户对话框
    const resourceCustomersDialogVisible = ref(false);
    const selectedResource = ref<any>(null);
    const resourceCustomers = ref<any[]>([]);
    const resourceCustomersLoading = ref(false);
    const resourceIsPublic = ref(false);

    // 新的资源授权管理对话框
    const selectedResourceForAuth = ref<any>(null);
    const selectedCustomerId = ref<number | null>(null);
    const filteredCustomerOptions = ref<any[]>([]);
    const authorizedCustomers = ref<any[]>([]);
    const authCustomersLoading = ref(false);
    const canAddCustomer = ref(false);

    // 表单数据
    const whitelistForm = reactive({
      customerId: null as number | null,
      resourceId: null as number | null,
    });

    // 搜索客户的授权资源
    const searchCustomerResources = async () => {
      const searchTerm = customerQuery.value.trim();

      if (!searchTerm) {
        ElMessage.warning('请输入客户账号');
        return;
      }

      loading.value = true;
      try {
        const params: any = {
          customer: searchTerm,
        };

        // 如果有课程筛选，添加course参数
        if (courseFilter.value) {
          params.course = courseFilter.value;
        }

        const response = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params,
        });

        // 更新客户信息
        customers.value = response.data.customers || [];

        // 更新资源列表
        const customerResources = response.data.resources || [];
        allCustomerResources.value = customerResources;
        resources.value = customerResources;

        // 更新当前客户
        currentCustomer.value = customers.value[0] || null;

        // 提取可用的课程列表
        const courses = [...new Set(customerResources.map((r: any) => r.course))] as string[];
        availableCourses.value = courses.sort();

        if (customers.value.length === 0) {
          ElMessage.info('未找到该客户账号');
        } else if (customerResources.length === 0) {
          ElMessage.info('该客户暂无授权资源');
        } else {
          ElMessage.success(`找到 ${customerResources.length} 个授权资源`);
        }
      } catch (error) {
        console.error('搜索客户资源失败:', error);
        ElMessage.error('搜索客户资源失败');
      } finally {
        loading.value = false;
      }
    };

    // 应用课程筛选
    const applyCourseFilter = () => {
      if (!courseFilter.value) {
        // 如果没有选择课程，显示所有资源
        resources.value = allCustomerResources.value;
      } else {
        // 按课程筛选
        resources.value = allCustomerResources.value.filter(
          (resource: any) => resource.course === courseFilter.value
        );
      }
      ElMessage.info(`筛选后显示 ${resources.value.length} 个资源`);
    };

    // 搜索资源
    const searchResources = async () => {
      if (!resourceQuery.value) return;

      loading.value = true;
      try {
        const response = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params: {
            resource: resourceQuery.value,
            page: currentPage.value,
            page_size: pageSize.value,
          },
        });
        resources.value = response.data.resources;
        totalResources.value = response.data.total || response.data.resources.length; // 从后端获取总数
        if (resources.value.length === 0) {
          ElMessage.info('未找到匹配的资源');
        }
      } catch (error) {
        console.error('搜索资源失败:', error);
        ElMessage.error('搜索资源失败');
      } finally {
        loading.value = false;
      }
    };

    // 处理分页大小变化
    const handleSizeChange = (val: number) => {
      pageSize.value = val;
      searchResources();
    };

    // 处理页码变化
    const handleCurrentChange = (val: number) => {
      currentPage.value = val;
      searchResources();
    };

    // 加载客户列表
    const loadCustomers = async () => {
      customerLoading.value = true;
      try {
        const response = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params: {
            customer: '',
          },
        });
        customerOptions.value = response.data.customers || [];
      } catch (error) {
        console.error('加载客户列表失败:', error);
        ElMessage.error('加载客户列表失败');
      } finally {
        customerLoading.value = false;
      }
    };

    // 远程搜索资源
    const remoteSearchResources = async (query: string) => {
      if (query === '') {
        resourceOptions.value = [];
        return;
      }

      resourceLoading.value = true;
      try {
        const response = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params: {
            resource: query,
          },
        });
        resourceOptions.value = response.data.resources;
      } catch (error) {
        console.error('搜索资源失败:', error);
        ElMessage.error('搜索资源失败');
      } finally {
        resourceLoading.value = false;
      }
    };

    // 处理授权方式变更
    const handleAuthTypeChange = (value: string) => {
      if (value === 'public') {
        // 选择"所有人"时，设置 customerId 为 -1
        whitelistForm.customerId = -1;
      } else {
        // 选择"特定客户"时，清空 customerId
        whitelistForm.customerId = null;
        // 加载客户列表
        loadCustomers();
      }
    };

    // 打开增加授权对话框
    const openAddWhitelistDialog = () => {
      // 默认选择"所有人"选项
      authType.value = 'public';
      whitelistForm.customerId = -1;
      whitelistForm.resourceId = null;
      // 加载客户列表（以备用户切换到"特定客户"选项）
      loadCustomers();
      addWhitelistDialogVisible.value = true;
    };

    // 选择资源进行授权
    const selectResourceForWhitelist = async (resource: any) => {
      // 设置选中的资源
      selectedResourceForAuth.value = resource;

      // 重置客户选择
      selectedCustomerId.value = null;
      filteredCustomerOptions.value = [];

      // 加载客户列表
      await loadCustomers();
      filteredCustomerOptions.value = [...customerOptions.value];

      // 加载资源的授权客户
      await loadResourceAuthorizedCustomers(resource.id);

      // 显示对话框
      addWhitelistDialogVisible.value = true;
    };

    // 加载资源的授权客户
    const loadResourceAuthorizedCustomers = async (resourceId: number) => {
      authCustomersLoading.value = true;
      authorizedCustomers.value = [];
      resourceIsPublic.value = false;

      try {
        const response = await axiosInstance.get(`/api/admin/resource-whitelist/resource/${resourceId}/customers`);
        authorizedCustomers.value = response.data.customers || [];
        resourceIsPublic.value = response.data.is_public || false;
      } catch (error) {
        console.error('获取资源授权客户失败:', error);
        ElMessage.error('获取资源授权客户失败');
      } finally {
        authCustomersLoading.value = false;
      }
    };



    // 处理客户选择
    const handleCustomerSelected = (customerId: number) => {
      // 检查客户是否已授权
      const isAlreadyAuthorized = authorizedCustomers.value.some(customer => customer.id === customerId);
      canAddCustomer.value = !isAlreadyAuthorized && customerId !== null;
    };

    // 为客户添加授权
    const addCustomerAuth = async () => {
      if (!selectedCustomerId.value || !selectedResourceForAuth.value) {
        ElMessage.warning('请选择客户');
        return;
      }

      // 检查客户是否已授权
      const isAlreadyAuthorized = authorizedCustomers.value.some(customer => customer.id === selectedCustomerId.value);
      if (isAlreadyAuthorized) {
        ElMessage.info('该客户已有授权');
        return;
      }

      try {
        const requestData = {
          customer_id: selectedCustomerId.value,
          resource_id: selectedResourceForAuth.value.id,
          is_public: false
        };

        const response = await axiosInstance.post('/api/admin/resource-whitelist/add', requestData);

        if (response.data.success) {
          ElMessage.success('授权成功');
          // 重新加载授权客户列表
          await loadResourceAuthorizedCustomers(selectedResourceForAuth.value.id);
          // 清空选择
          selectedCustomerId.value = null;
        } else {
          ElMessage.error(response.data.error || '授权失败');
        }
      } catch (error) {
        console.error('添加授权失败:', error);
        ElMessage.error('添加授权失败');
      }
    };

    // 移除客户授权
    const removeCustomerAuth = async (customer: any) => {
      if (!selectedResourceForAuth.value) return;

      try {
        await ElMessageBox.confirm(
          `确定要移除 ${customer.nickname} 对资源 "${selectedResourceForAuth.value.title}" 的授权吗？`,
          '确认移除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
        );

        const response = await axiosInstance.delete(`/api/admin/resource-whitelist/customer/${customer.id}/resource/${selectedResourceForAuth.value.id}`);

        if (response.data.success) {
          ElMessage.success('移除授权成功');
          // 从列表中移除
          authorizedCustomers.value = authorizedCustomers.value.filter(
            item => item.id !== customer.id
          );
        } else {
          ElMessage.error(response.data.error || '移除授权失败');
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('移除授权失败:', error);
          ElMessage.error('移除授权失败');
        }
      }
    };

    // 移除公开授权
    const removePublicAuth = async () => {
      if (!selectedResourceForAuth.value) return;

      try {
        await ElMessageBox.confirm(
          `确定要取消资源 "${selectedResourceForAuth.value.title}" 对所有人的开放吗？`,
          '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
        );

        const response = await axiosInstance.delete(`/api/admin/resource-whitelist/public/${selectedResourceForAuth.value.id}`);

        if (response.data.success) {
          ElMessage.success('已取消对所有人开放');
          resourceIsPublic.value = false;
        } else {
          ElMessage.error(response.data.error || '操作失败');
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('取消公开授权失败:', error);
          ElMessage.error('取消公开授权失败');
        }
      }
    };

    // 将资源设为公开
    const makeResourcePublic = async () => {
      if (!selectedResourceForAuth.value) return;

      try {
        await ElMessageBox.confirm(
          `确定要将资源 "${selectedResourceForAuth.value.title}" 设置为对所有人开放吗？`,
          '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
        );

        const requestData = {
          resource_id: selectedResourceForAuth.value.id,
          is_public: true
        };

        const response = await axiosInstance.post('/api/admin/resource-whitelist/add', requestData);

        if (response.data.success) {
          ElMessage.success('资源已设置为对所有人开放');
          resourceIsPublic.value = true;
        } else {
          ElMessage.error(response.data.error || '操作失败');
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('设置公开授权失败:', error);
          ElMessage.error('设置公开授权失败');
        }
      }
    };

    // 为资源授权对话框搜索客户
    const searchCustomersForAuth = async (query: string) => {
      if (query === '') {
        filteredCustomerOptions.value = [...customerOptions.value];
        return;
      }

      customerLoading.value = true;
      try {
        const response = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params: {
            customer: query,
          },
        });

        filteredCustomerOptions.value = response.data.customers || [];
      } catch (error) {
        console.error('搜索客户失败:', error);
        ElMessage.error('搜索客户失败');
      } finally {
        customerLoading.value = false;
      }
    };

    // 添加白名单
    const addWhitelist = async () => {
      // 根据授权方式设置 customerId
      if (authType.value === 'public') {
        whitelistForm.customerId = -1;
      }

      if (!whitelistForm.resourceId) {
        ElMessage.warning('请选择资源');
        return;
      }

      if (authType.value === 'specific' && !whitelistForm.customerId) {
        ElMessage.warning('请选择客户');
        return;
      }

      submitting.value = true;
      try {
        // 处理"所有人"选项
        if (authType.value === 'public') {
          // 确认是否要将资源设为对所有人开放
          try {
            await ElMessageBox.confirm(
              '您选择了"所有人"选项，这将使该资源对所有用户免费开放。确定要继续吗？',
              '确认开放资源', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
            );
          } catch (error) {
            submitting.value = false;
            return; // 用户取消了操作
          }
        }

        // 构建请求数据
        const requestData = {
          customer_id: whitelistForm.customerId,
          resource_id: whitelistForm.resourceId,
          is_public: authType.value === 'public' // 添加标志表示是否为公开资源
        };

        console.log('Sending request data:', requestData);

        const response = await axiosInstance.post('/api/admin/resource-whitelist/add', requestData);

        if (response.data.success) {
          if (whitelistForm.customerId === -1) {
            ElMessage.success('资源已设置为对所有人免费开放');
          } else {
            ElMessage.success('授权成功');
          }
          addWhitelistDialogVisible.value = false;

          // 如果当前正在查看该客户的授权，则刷新
          if (selectedCustomer.value && selectedCustomer.value.id === whitelistForm.customerId) {
            viewCustomerWhitelist(selectedCustomer.value);
          }
        } else {
          ElMessage.error(response.data.error || '授权失败');
        }
      } catch (error) {
        console.error('添加授权失败:', error);
        ElMessage.error('添加授权失败');
      } finally {
        submitting.value = false;
      }
    };

    // 查看客户授权
    const viewCustomerWhitelist = async (customer: any) => {
      selectedCustomer.value = customer;
      customerWhitelistDialogVisible.value = true;
      customerWhitelistLoading.value = true;
      customerWhitelistResources.value = [];

      try {
        // 获取客户的所有白名单资源
        const whitelistItems = await fetchCustomerWhitelistItems(customer.id);

        // 获取资源详情
        const resourcePromises = whitelistItems.map(async (item: any) => {
          try {
            const response = await axiosInstance.get(`/api/admin/resources/${item.resource_id}`);
            return response.data;
          } catch (error) {
            console.error(`获取资源 ${item.resource_id} 详情失败:`, error);
            return null;
          }
        });

        const resources = await Promise.all(resourcePromises);

        // 获取所有公开资源的ID
        const publicResourcesResponse = await axiosInstance.get('/api/admin/resource-whitelist/search', {
          params: { resource: '' }
        });
        const publicResources = publicResourcesResponse.data.resources || [];
        const publicResourceIds = publicResources
          .filter((r: any) => r.is_public)
          .map((r: any) => r.id);

        // 为每个资源添加是否公开的标志
        const resourcesWithPublicFlag = resources
          .filter(Boolean)
          .map((resource: any) => ({
            ...resource,
            is_public: publicResourceIds.includes(resource.id)
          }));

        customerWhitelistResources.value = resourcesWithPublicFlag;
      } catch (error) {
        console.error('获取客户授权资源失败:', error);
        ElMessage.error('获取客户授权资源失败');
      } finally {
        customerWhitelistLoading.value = false;
      }
    };

    // 获取客户的白名单项
    const fetchCustomerWhitelistItems = async (customerId: number) => {
      try {
        const response = await axiosInstance.get(`/api/admin/resource-whitelist/customer/${customerId}`);
        return response.data.whitelist_items || [];
      } catch (error) {
        console.error('获取客户白名单项失败:', error);
        throw error;
      }
    };

    // 移除白名单
    const removeWhitelist = async (resource: any) => {
      if (!selectedCustomer.value) return;

      try {
        await ElMessageBox.confirm(
          `确定要移除 ${selectedCustomer.value.nickname} 对资源 "${resource.title}" 的授权吗？`,
          '确认移除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
        );

        const response = await axiosInstance.delete(`/api/admin/resource-whitelist/customer/${selectedCustomer.value.id}/resource/${resource.id}`);

        if (response.data.success) {
          ElMessage.success('移除授权成功');
          // 从列表中移除
          customerWhitelistResources.value = customerWhitelistResources.value.filter(
            (item) => item.id !== resource.id
          );
        } else {
          ElMessage.error(response.data.error || '移除授权失败');
        }
      } catch (error: any) {
        if (error !== 'cancel') {
          console.error('移除授权失败:', error);
          ElMessage.error('移除授权失败');
        }
      }
    };

    // 查看资源授权客户
    const viewResourceCustomers = async (resource: any) => {
      selectedResource.value = resource;
      resourceCustomersDialogVisible.value = true;
      resourceCustomersLoading.value = true;
      resourceCustomers.value = [];
      resourceIsPublic.value = false; // 重置公开状态

      try {
        const response = await axiosInstance.get(`/api/admin/resource-whitelist/resource/${resource.id}/customers`);
        resourceCustomers.value = response.data.customers || [];
        // 设置资源是否公开
        resourceIsPublic.value = response.data.is_public || false;
      } catch (error) {
        console.error('获取资源授权客户失败:', error);
        ElMessage.error('获取资源授权客户失败');
      } finally {
        resourceCustomersLoading.value = false;
      }
    };

    // 初始化
    onMounted(() => {
      // 加载初始数据
      loadCustomers(); // 确保客户列表中包含"所有人"选项
    });

    // 返回所有需要在模板中使用的变量和函数
    return {
      // 搜索参数
      customerQuery,
      resourceQuery,
      courseFilter,
      loading,

      // 课程列表和筛选
      availableCourses,
      allCustomerResources,
      currentCustomer,

      // 分页参数
      currentPage,
      pageSize,
      totalResources,

      // 搜索结果
      customers,
      resources,

      // 增加授权对话框
      addWhitelistDialogVisible,
      customerOptions,
      resourceOptions,
      customerLoading,
      resourceLoading,
      submitting,
      authType,

      // 客户授权详情对话框
      customerWhitelistDialogVisible,
      selectedCustomer,
      customerWhitelistResources,
      customerWhitelistLoading,

      // 资源授权客户对话框
      resourceCustomersDialogVisible,
      selectedResource,
      resourceCustomers,
      resourceCustomersLoading,
      resourceIsPublic,

      // 新的资源授权管理对话框
      selectedResourceForAuth,
      selectedCustomerId,
      filteredCustomerOptions,
      authorizedCustomers,
      authCustomersLoading,
      canAddCustomer,

      // 表单数据
      whitelistForm,

      // 函数
      searchCustomerResources,
      applyCourseFilter,
      searchResources,
      handleSizeChange,
      handleCurrentChange,
      loadCustomers,
      remoteSearchResources,
      handleAuthTypeChange,
      openAddWhitelistDialog,
      selectResourceForWhitelist,
      addWhitelist,
      viewCustomerWhitelist,
      fetchCustomerWhitelistItems,
      removeWhitelist,
      viewResourceCustomers,

      // 新的资源授权管理函数
      loadResourceAuthorizedCustomers,
      handleCustomerSelected,
      addCustomerAuth,
      removeCustomerAuth,
      removePublicAuth,
      makeResourcePublic,
      searchCustomersForAuth,

      // 图标
      ElIconView,
      ElIconKey,
      ElIconDelete,
      ElIconSearch,
      ElIconPlus,
      ElIconCheck,
      ElIconUser,
      ElIconFiles
    };
  }
});
</script>

<style scoped>
.resource-whitelist-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #303133;
}

.search-form {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

.card-with-shadow {
  margin-bottom: 20px;
  transition: all 0.3s;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.card-with-shadow:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.card-header span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-table {
  margin-bottom: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.custom-table :deep(th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  padding: 12px 0;
}

.custom-table :deep(td) {
  padding: 10px 0;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f0f9eb !important;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.customer-info {
  margin-bottom: 20px;
}

.whitelist-resources h3 {
  margin: 20px 0 15px;
  font-size: 16px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 公开资源标识 */
.id-cell {
  position: relative;
  display: flex;
  align-items: center;
}

.public-star {
  color: #f56c6c;
  font-weight: bold;
  margin-left: 2px;
  font-size: 16px;
}

/* 客户项样式 */
.customer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.customer-item:hover {
  background-color: #f5f7fa;
}

.customer-item:last-child {
  border-bottom: none;
}

.customer-name {
  font-size: 14px;
  color: #606266;
}

.add-customer-auth h3,
.authorized-customers h3 {
  margin: 0 0 10px;
  font-size: 16px;
  color: #606266;
  font-weight: bold;
}

.customer-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-top: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .resource-whitelist-container {
    padding: 10px;
  }

  .el-form-item {
    margin-bottom: 10px;
  }

  .customer-item {
    padding: 8px;
  }
}
</style>
