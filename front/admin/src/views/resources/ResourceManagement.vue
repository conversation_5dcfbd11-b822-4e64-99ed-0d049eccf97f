<template>
    <div class="resource-management">
        <div class="page-header">
            <h1>资源管理</h1>
            <el-button type="primary" @click="openCreateDialog">
                <el-icon>
                    <Plus />
                </el-icon>
                新建资源
            </el-button>
        </div>

        <!-- 资源列表 -->
        <el-card class="resource-list-card">
            <template #header>
                <div class="card-header">
                    <span>资源列表</span>
                    <div class="header-actions">
                        <el-input v-model="searchQuery" placeholder="搜索资源..." prefix-icon="Search" clearable
                            @clear="handleSearch" @input="handleSearch" style="width: 250px" />
                        <el-select v-model="courseFilter" placeholder="课程筛选" clearable @change="handleSearch"
                            style="width: 150px">
                            <el-option v-for="course in courses" :key="course" :label="course" :value="course" />
                        </el-select>
                        <el-button @click="fetchResources">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                            刷新
                        </el-button>
                    </div>
                </div>
            </template>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <el-skeleton :rows="5" animated />
            </div>

            <!-- 资源表格 -->
            <el-table v-else :data="resources" style="width: 100%" border stripe>
                <el-table-column prop="id" sortable label="ID" class-name="small-column" width="60" />
                <el-table-column prop="course" label="课程" class-name="small-column" width="80" />
                <el-table-column prop="resource" label="资源类型" class-name="small-column" width="100" />
                <el-table-column prop="seq" label="序号" class-name="small-column" width="60">
                    <template #default="scope">
                        <el-input-number v-if="scope.row.editing" v-model="scope.row.seq" size="small" :min="0"
                            controls-position="right" />
                        <span v-else>{{ scope.row.seq }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="title" label="标题" class-name="medium-column" width="150">
                    <template #default="scope">
                        <el-input v-if="scope.row.editing" v-model="scope.row.title" size="small" />
                        <span v-else>{{ scope.row.title }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" class-name="flexible-column" min-width="180">
                    <template #default="scope">
                        <el-input v-if="scope.row.editing" v-model="scope.row.description" size="small" />
                        <span v-else>{{ scope.row.description || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="rel_path" label="相对路径" class-name="flexible-column" min-width="150" />
                <el-table-column prop="publish_date" sortable label="发布日期" class-name="medium-column" width="150">
                    <template #default="scope">
                        <span>{{ formatDate(scope.row.publish_date) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="price" label="价格" class-name="small-column" width="100">
                    <template #default="scope">
                        <el-select v-if="scope.row.editing" v-model="scope.row.price_tier_id" size="small"
                            placeholder="选择价格">
                            <el-option v-for="tier in priceTiers" :key="tier.id" :label="`¥${tier.price}`"
                                :value="tier.id" />
                        </el-select>
                        <span v-else>{{ getPriceDisplay(scope.row.price_tier_id) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" class-name="operation-column" width="150" fixed="right">
                    <template #default="scope">
                        <div v-if="scope.row.editing">
                            <el-button type="primary" size="small" @click="saveResource(scope.row)">保存</el-button>
                            <el-button size="small" @click="cancelEdit(scope.row)">取消</el-button>
                        </div>
                        <div v-else>
                            <el-button type="primary" size="small" @click="editResource(scope.row)">编辑</el-button>
                            <el-popconfirm title="确定要删除这个资源吗？" @confirm="deleteResource(scope.row.id)">
                                <template #reference>
                                    <el-button type="danger" size="small">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total"
                    @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </el-card>

        <!-- 创建资源对话框 -->
        <el-dialog v-model="createDialogVisible" title="新建资源" width="600px">
            <el-form :model="newResource" label-width="100px" :rules="resourceRules" ref="createFormRef">
                <el-form-item label="课程" prop="course">
                    <el-select v-model="newResource.course" placeholder="选择课程" style="width: 100%">
                        <el-option v-for="course in courses" :key="course" :label="course" :value="course" />
                    </el-select>
                </el-form-item>
                <el-form-item label="资源类型" prop="resource">
                    <el-input v-model="newResource.resource" placeholder="资源类型，如 courseware" />
                </el-form-item>
                <el-form-item label="序号" prop="seq">
                    <el-input-number v-model="newResource.seq" :min="0" style="width: 100%" />
                </el-form-item>
                <el-form-item label="标题" prop="title">
                    <el-input v-model="newResource.title" placeholder="资源标题" />
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input v-model="newResource.description" type="textarea" :rows="3" placeholder="资源描述" />
                </el-form-item>
                <el-form-item label="相对路径" prop="rel_path">
                    <el-input v-model="newResource.rel_path" placeholder="相对路径，如 courseware/01.ipynb" />
                </el-form-item>
                <el-form-item label="价格" prop="price_tier_id">
                    <el-select v-model="newResource.price_tier_id" placeholder="选择价格档位" style="width: 100%">
                        <el-option :value="null" label="免费" />
                        <el-option v-for="tier in priceTiers" :key="tier.id" :label="`¥${tier.price}`"
                            :value="tier.id" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="createDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="createResource" :loading="submitting">创建</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import axios from 'axios';

// 价格档位类型定义
interface PriceTier {
    id: number;
    price: number;
    wechat_url?: string;
}

// 资源列表数据
const resources = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const searchQuery = ref('');
const courseFilter = ref('');
const courses = ref([]);
const priceTiers = ref<PriceTier[]>([]);

// 创建资源对话框
const createDialogVisible = ref(false);
const submitting = ref(false);
const createFormRef = ref<FormInstance>();
const newResource = reactive({
    course: '',
    resource: '',
    seq: 0,
    title: '',
    description: '',
    rel_path: '',
    price: 0,
    price_tier_id: null as number | null,
    publish_date: new Date()
});

// 表单验证规则
const resourceRules = {
    course: [{ required: true, message: '请选择课程', trigger: 'blur' }],
    resource: [{ required: true, message: '请输入资源类型', trigger: 'blur' }],
    seq: [{ required: true, message: '请输入序号', trigger: 'blur' }],
    title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    rel_path: [{ required: true, message: '请输入相对路径', trigger: 'blur' }]
};

// 获取价格档位
const fetchPriceTiers = async () => {
    try {
        const response = await axios.get('/api/price-tiers');
        priceTiers.value = response.data;
    } catch (error) {
        console.error('获取价格档位失败:', error);
        ElMessage.error('获取价格档位失败');
    }
};

// 获取价格显示文本
const getPriceDisplay = (priceTierId: number | null) => {
    if (!priceTierId) return '免费';
    const tier = priceTiers.value.find(t => t.id === priceTierId);
    return tier ? `¥${tier.price}` : '未知价格';
};

// 获取资源列表
const fetchResources = async () => {
    loading.value = true;
    try {
        const params = {
            page: currentPage.value,
            page_size: pageSize.value,
            search_query: searchQuery.value || undefined,
            course: courseFilter.value || undefined
        };

        const response = await axios.get('/api/admin/resources', { params });
        resources.value = response.data.resources.map((resource: any) => ({
            ...resource,
            editing: false,
            originalData: { ...resource }
        }));
        total.value = response.data.total;
        courses.value = response.data.courses;
    } catch (error) {
        console.error('获取资源列表失败:', error);
        ElMessage.error('获取资源列表失败');
    } finally {
        loading.value = false;
    }
};

// 搜索处理
const handleSearch = () => {
    currentPage.value = 1;
    fetchResources();
};

// 分页处理
const handleSizeChange = (size: number) => {
    pageSize.value = size;
    fetchResources();
};

const handleCurrentChange = (page: number) => {
    currentPage.value = page;
    fetchResources();
};

// 格式化日期
const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString();
};

// 打开创建对话框
const openCreateDialog = () => {
    Object.assign(newResource, {
        course: '',
        resource: '',
        seq: 0,
        title: '',
        description: '',
        rel_path: '',
        price: 0,
        price_tier_id: null,
        publish_date: new Date()
    });
    createDialogVisible.value = true;
};

// 创建资源
const createResource = async () => {
    if (!createFormRef.value) return;

    await createFormRef.value.validate(async (valid) => {
        if (!valid) return;

        submitting.value = true;
        try {
            const resourceData = { ...newResource };
            await axios.post('/api/admin/resources', resourceData);
            ElMessage.success('资源创建成功');
            createDialogVisible.value = false;
            fetchResources();
        } catch (error) {
            console.error('创建资源失败:', error);
            ElMessage.error('创建资源失败');
        } finally {
            submitting.value = false;
        }
    });
};

// 编辑资源
const editResource = (resource: any) => {
    resource.editing = true;
    resource.originalData = { ...resource };
};

// 取消编辑
const cancelEdit = (resource: any) => {
    Object.assign(resource, resource.originalData);
    resource.editing = false;
};

// 保存资源
const saveResource = async (resource: any) => {
    try {
        const { id, title, description, seq, price_tier_id } = resource;
        await axios.put(`/api/admin/resources/${id}`, { title, description, seq, price_tier_id });
        resource.editing = false;
        ElMessage.success('资源更新成功');
    } catch (error) {
        console.error('更新资源失败:', error);
        ElMessage.error('更新资源失败');
    }
};

// 删除资源
const deleteResource = async (id: number) => {
    try {
        await axios.delete(`/api/admin/resources/${id}`);
        ElMessage.success('资源删除成功');
        fetchResources();
    } catch (error) {
        console.error('删除资源失败:', error);
        ElMessage.error('删除资源失败');
    }
};

// 组件挂载时获取资源列表和价格档位
onMounted(() => {
    fetchResources();
    fetchPriceTiers();
});
</script>

<style scoped>
.resource-management {
    padding: 0 20px 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eaeaea;
}

h1 {
    margin: 0;
    color: #37474f;
    font-size: 1.8rem;
    font-weight: 500;
}

.resource-list-card {
    margin-bottom: 30px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.loading-container {
    padding: 20px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}
</style>
