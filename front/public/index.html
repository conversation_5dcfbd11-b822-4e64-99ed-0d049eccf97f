<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匡醍 - 传播知识与希望</title>
    <link rel="icon" href="/favicon.ico">
    <style>
        :root {
            --primary-color: #C850C0;
            --secondary-color: #4158D0;
            --text-color: #f0f0f0;
            --light-text: #c0c0c0;
            --bg-color: #121212;
            --card-bg: #1e1e1e;
            --gradient: linear-gradient(135deg, #4158D0, #C850C0, #FFCC70);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 页眉样式 */
        header {
            background-color: rgba(30, 30, 30, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
            padding: 20px 0;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            height: 40px;
        }

        .login-btn {
            background: var(--gradient);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* 标题样式 */
        .main-title {
            font-size: 2rem;
            margin: 80px 0 60px;
            text-align: center;
            color: var(--text-color);
            position: relative;
        }

        .main-title::after {
            content: '';
            display: block;
            width: 80px;
            height: 4px;
            background: var(--gradient);
            margin: 15px auto 0;
            border-radius: 2px;
        }

        /* 内容卡片通用样式 */
        .content-card {
            display: flex;
            background-color: var(--card-bg);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            margin-bottom: 120px; /* 增加卡片间距 */
            transition: transform 0.5s ease, box-shadow 0.5s ease;
            position: relative;
        }

        .content-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* 光束效果 */
        .content-card::before {
            content: '';
            position: absolute;
            width: 150%;
            height: 150%;
            background: radial-gradient(
                circle at center,
                rgba(200, 80, 192, 0.3) 0%,
                rgba(65, 88, 208, 0.2) 30%,
                rgba(0, 0, 0, 0) 70%
            );
            opacity: 0;
            z-index: 0;
            transition: opacity 0.8s ease, transform 0.8s ease;
            pointer-events: none;
        }

        .content-card.visible::before {
            opacity: 1;
            transform: scale(1);
        }

        .content-card.leaving::before {
            opacity: 0;
            transform: scale(0.5);
        }

        /* 奇数卡片（图片在左） */
        .content-card.odd {
            flex-direction: row;
        }

        .content-card.odd::before {
            left: -25%;
            top: -25%;
        }

        /* 偶数卡片（图片在右） */
        .content-card.even {
            flex-direction: row-reverse;
        }

        .content-card.even::before {
            right: -25%;
            top: -25%;
        }

        .card-image {
            flex: 1;
            min-width: 300px;
            overflow: hidden;
            position: relative;
            z-index: 1;
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.8s ease, filter 0.8s ease;
        }

        .content-card:hover .card-image img {
            transform: scale(1.05);
            filter: brightness(1.2);
        }

        .card-content {
            flex: 2;
            padding: 40px;
            position: relative;
            z-index: 1;
        }

        .card-title {
            font-size: 1.8rem;
            margin: 0 0 15px;
            color: var(--text-color);
        }

        .card-level {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: white;
        }

        .level-basic {
            background-color: #4BC0C0;
        }

        .level-intermediate {
            background-color: #FF9843;
        }

        .level-advanced {
            background-color: #FF4B91;
        }

        .card-description {
            color: var(--light-text);
            line-height: 1.8;
            margin-bottom: 25px;
        }

        .card-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .card-link {
            display: inline-block;
            padding: 10px 20px;
            background: var(--gradient);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 600;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transition: left 0.5s ease;
        }

        .card-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .card-link:hover::before {
            left: 100%;
        }

        .preview-link {
            background: linear-gradient(135deg, #36D1DC, #5B86E5);
        }

        /* 使命宣言样式 */
        .mission {
            text-align: center;
            padding: 60px 0;
            background: var(--gradient);
            border-radius: 15px;
            margin-bottom: 100px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .mission::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .mission-title {
            font-size: 2.5rem;
            color: white;
            margin: 0;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        /* 页脚样式 */
        footer {
            background-color: #0a0a0a;
            color: white;
            padding: 60px 0 30px;
            text-align: center;
            margin-top: 100px;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: var(--gradient);
        }

        .contact-container {
            display: flex;
            justify-content: center;
            gap: 60px;
            margin-bottom: 40px;
        }

        .contact-item {
            transition: transform 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-10px);
        }

        .contact-item h3 {
            margin-bottom: 20px;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-top: 2em;
        }

        .qr-code:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
        }

        .copyright {
            font-size: 0.9rem;
            color: #666;
        }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .content-card, .content-card.odd, .content-card.even {
            flex-direction: column;
        }

        .card-image {
            min-height: 250px;
        }

        .contact-container {
            flex-direction: column;
            gap: 30px;
        }
    }
    .map-container {
        flex: 1.5;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .map-link {
        display: block;
        width: 100%;
        text-decoration: none;
    }

    .map {
        width: 100%;
        height: 200px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
    }

    .map:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    }

    .map-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .map-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        pointer-events: none;
    }

    .map-pin {
        width: 20px;
        height: 20px;
        background-color: #C850C0;
        border-radius: 50% 50% 50% 0;
        transform: rotate(-45deg);
        position: relative;
        z-index: 2;
    }

    .map-pin::after {
        content: '';
        width: 10px;
        height: 10px;
        background-color: white;
        border-radius: 50%;
        position: absolute;
        top: 5px;
        left: 5px;
    }

    .map-pulse {
        position: absolute;
        width: 50px;
        height: 50px;
        background-color: rgba(200, 80, 192, 0.3);
        border-radius: 50%;
        z-index: 1;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(0.5);
            opacity: 0.5;
        }
        70% {
            transform: scale(1.5);
            opacity: 0;
        }
        100% {
            transform: scale(0.5);
            opacity: 0;
        }
    }

    .address {
        margin-top: 10px;
        font-size: 0.9rem;
        color: var(--light-text);
    }

    /* 调整响应式布局 */
    @media (max-width: 992px) {
        .contact-container {
            flex-direction: column;
            align-items: center;
        }

        .map-container {
            width: 100%;
            max-width: 400px;
            margin-top: 30px;
        }
    }
    </style>
</head>
<body>
    <!-- 页眉 -->
    <header>
        <div class="container header-container">
            <img src="/assets/logo.png" alt="Quantide Logo" class="logo">
            <button class="login-btn" onclick="window.location.href='/academy/login'">登录</button>
        </div>
    </header>

    <div class="container">
        <!-- 标语 -->
        <div class="mission">
            <h2 class="mission-title">传播知识和希望</h2>
        </div>

        <!-- 量化24课 -->
        <div class="content-card odd" id="card-1">
            <div class="card-image">
                <img src="/assets/24lectures_1000.png" alt="量化24课">
            </div>
            <div class="card-content">
                <h3 class="card-title">量化24课</h3>
                <div class="card-level level-intermediate">中级</div>
                <p class="card-description">
                    本课程是量化交易的入门课程，它面向打算进入量化交易领域的学生、程序员和正在从事主观交易的机构投资者和个人投资者。课程涵盖了量化交易的全流程，即如何获取数据，如何考察数据的分布、关联性，因子和模式的发现和提取，如何编写策略、进行回测和评估，最终将策略接入实盘。
                </p>
                <div class="card-buttons">
                    <a href="https://www.xiaohongshu.com/goods-detail/67ff60dec496ce00017a0906" target="_blank" class="card-link">了解更多</a>
                    <a href="/academy/login?preview=true&course=l24" class="card-link preview-link">课程预览</a>
                </div>
            </div>
        </div>

        <!-- 因子分析与机器学习策略 -->
        <div class="content-card even" id="card-2">
            <div class="card-image">
                <img src="/assets/fa_1000.png" alt="因子分析与机器学习策略">
            </div>
            <div class="card-content">
                <h3 class="card-title">因子分析与机器学习策略</h3>
                <div class="card-level level-advanced">高级</div>
                <p class="card-description">
                    本课面向决心以专业、严谨的态度研究量化策略的人。学完本课，您将具有4年以上经验的量化研究员同样的研究能力。
                </p>
                <div class="card-buttons">
                    <a href="https://www.xiaohongshu.com/goods-detail/67e673f279c93d0001f23092" target="_blank" class="card-link">了解更多</a>
                    <a href="/academy/login?preview=true&course=fa" class="card-link preview-link">课程预览</a>
                </div>
            </div>
        </div>

        <!-- 量化人的Numpy&Pandas -->
        <div class="content-card odd" id="card-3">
            <div class="card-image">
                <img src="/assets/pandas_1000.png" alt="量化人的Numpy&Pandas">
            </div>
            <div class="card-content">
                <h3 class="card-title">量化人的Numpy&Pandas</h3>
                <div class="card-level level-basic">基础</div>
                <p class="card-description">
                    您可能对Numpy和Pandas都不陌生。但在量化日常中，您可能仍然在大量写一些低效的代码，全然不知Numpy&Pandas已经提供了更高效的API。
                </p>
                <div class="card-buttons">
                    <a href="https://www.xiaohongshu.com/goods-detail/67f4d677ab6a3e0001e5bb84" target="_blank" class="card-link">购买课程</a>
                    <a href="https://www.jieyu.ai/articles/python/numpy%26pandas/01-introduction/" class="card-link preview-link">免费阅读</a>
                </div>
            </div>
        </div>

        <!-- Python高效编程实战指南 -->
        <div class="content-card even" id="card-4">
            <div class="card-image">
                <img src="https://images.jieyu.ai/images/hot/mybook/girl-on-sofa.jpg" alt="Python高效编程实战指南">
            </div>
            <div class="card-content">
                <h3 class="card-title">Python高效编程实战指南</h3>
                <p class="card-description">
                    为推动国内Python软件工程质量的进步，我们出版了这本书。<br>

                    本书清楚、完整地诠释了为什么Python可以，或者说，Python应该是核心编程和系统开发的首选，从语言特性、便捷程度和可拓展性，到纵向的流程设计分析，再到横向的工具、资源和社区支持介绍，组织了大量的实例的技巧。在组织上，本书以宁缺毋滥的背景知识、主次分明的节奏、俯拾皆是的技巧和转页即遇的幽默感将知识、经验、目的和实用结合得严丝合缝，更给阅读增加了轻快愉悦的成分。
                </p>
                <p style="text-align:right;width: 100%; font-style: italic; font-size: 0.8em"> -- 勤远投资合伙人 朱灵引博士</p>
            </div>
        </div>

        <!-- 关于匡醍 -->
        <div class="content-card odd" id="card-5">
            <div class="card-image">
                <img src="/assets/aaron.png" alt="关于匡醍">
            </div>
            <div class="card-content">
                <h3 class="card-title">关于匡醍</h3>
                <p class="card-description">
                    匡醍是一家专注于量化交易的科技公司，由前 IBM/Oracle 高级软件开发经理、海豚浏览器副总裁创立。我们的使命是通过高质量的教育内容和量化软件，帮助更多人掌握量化交易技能，推动金融平民化。
                </p>
                <p class="card-description">
                    我们开发了量化开源框架 Zillionare，为量化交易爱好者提供了强大的技术支持。我们的创始人是 Python 技术专家，《Python高效编程实战指南》作者，在量化交易和软件开发领域拥有丰富的经验。
                </p>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="contact-container">
                <div class="contact-item">
                    <h3>客服微信</h3>
                    <img src="/assets/quantfans.jpg" alt="宽粉微信" class="qr-code">
                </div>
                <div class="contact-item">
                    <h3>公众号</h3>
                    <img src="/assets/gzh_512.jpg" alt="公众号" class="qr-code">
                </div>
                <div class="contact-item map-container">
                    <h3>武汉.光谷</h3>
                    <a href="https://map.baidu.com/poi/%E5%85%89%E8%B0%B7%E6%97%B6%E4%BB%A3%E5%B9%BF%E5%9C%BA/@12725692.565,3551259.79,19z" target="_blank" class="map-link">
                        <div class="map">
                            <img src="https://images.jieyu.ai/images/2025/04/optical-valley.png" class="map-image">
                            <div class="map-overlay">
                                <div class="map-pin"></div>
                                <div class="map-pulse"></div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="copyright">
                © <span id="current-year"></span> Quantide. All rights reserved. | Build with augment!
            </div>
        </div>
    </footer>

    <script>
        // 设置当前年份
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // 处理滚动动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 获取所有内容卡片
            const cards = document.querySelectorAll('.content-card');
            const mission = document.querySelector('.mission');

            // 初始化 IntersectionObserver
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    // 当元素进入视口
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        entry.target.classList.remove('leaving');
                    } else {
                        // 当元素离开视口
                        if (entry.target.classList.contains('visible')) {
                            entry.target.classList.add('leaving');
                            setTimeout(() => {
                                if (!entry.target.classList.contains('visible')) {
                                    entry.target.classList.remove('leaving');
                                }
                            }, 800);
                        }
                    }
                });
            }, {
                root: null, // 使用视口作为根
                rootMargin: '-10% 0px', // 当元素进入视口10%时触发
                threshold: 0.1 // 当元素10%可见时触发
            });

            // 观察所有卡片
            cards.forEach(card => {
                observer.observe(card);
            });

            // 观察使命宣言
            observer.observe(mission);

            // 添加滚动效果
            window.addEventListener('scroll', function() {
                const scrollPosition = window.scrollY;

                // 为每个卡片添加视差效果
                cards.forEach(card => {
                    const cardTop = card.offsetTop;
                    const cardHeight = card.offsetHeight;
                    const cardCenter = cardTop + cardHeight / 2;
                    const distanceFromCenter = scrollPosition + window.innerHeight / 2 - cardCenter;
                    const normalizedDistance = distanceFromCenter / (window.innerHeight / 2);

                    // 根据卡片位置调整光束效果
                    if (Math.abs(normalizedDistance) < 1) {
                        const scale = 1 - Math.abs(normalizedDistance) * 0.3;
                        const opacity = 1 - Math.abs(normalizedDistance) * 0.7;

                        // 设置光束效果
                        card.style.setProperty('--beam-scale', scale);
                        card.style.setProperty('--beam-opacity', opacity);
                    }
                });
            });
        });
    </script>
</body>
</html>
