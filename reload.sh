# check if virtual env is provision
cwd=`pwd`
ENV_NAME=$(conda info --envs | grep '*' | awk '{print $1}')
if [[ "$ENV_NAME" != "provision" ]]; then
    echo "[错误] 当前 conda 环境为 '$ENV_NAME'，请先激活 provision 环境"
    echo "激活命令：conda activate provision"
    exit 1
fi

front/build_and_deploy.sh admin
front/build_and_deploy.sh academy

echo "构建和替换provision"
pip uninstall -y provision

for file in `ls $cwd/provision/conf/*.yaml`; do
    if [ $file == "$cwd/provision/conf/plans.yaml" ]; then
        continue
    fi
    echo "正在将配置文件 $file 转换为py"
    datamodel-codegen --input $file --output $cwd/provision/conf/$(basename $file .yaml).py --input-file-type yaml --reuse-model --disable-timestamp --base-class provision.common.custom_base_model.CustomBaseModel
done
poetry build
poetry install
