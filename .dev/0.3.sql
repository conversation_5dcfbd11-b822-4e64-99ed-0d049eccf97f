 -- 手动升级时使用
 CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        account VARCHAR NOT NULL UNIQUE,
        password VARCHAR NOT NULL,
        nickname VARCHAR NOT NULL,
        salt VARCHAR NOT NULL,
        phone VARCHAR,
        wx VARCHAR,
        occupation VARCHAR,
        channel VARCHAR,
        register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE
    );

    CREATE TABLE IF NOT EXISTS registry (
        id SERIAL PRIMARY KEY,
        course VARCHAR NOT NULL,
        customer_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        plan VARCHAR NOT NULL,
        start_time TIMESTAMP NOT NULL,
        refund_end TIMESTAMP NOT NULL,
        prime_end TIMESTAMP NOT NULL,
        retention_end TIMESTAMP NOT NULL,
        expire_time TIMESTAMP NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        division VARCHAR NOT NULL
    );

    CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        title VARCHAR NOT NULL,
        content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sender_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        recipient_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        is_read BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE
    );

    CREATE TABLE IF NOT EXISTS resources (
        id SERIAL PRIMARY KEY,
        course VARCHAR NOT NULL,
        resource VARCHAR NOT NULL,
        seq INTEGER NOT NULL,
        title VARCHAR NOT NULL,
        description TEXT,
        rel_path VARCHAR NOT NULL,
        publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        price FLOAT DEFAULT 0.0,
        division VARCHAR NOT NULL,
        img VARCHAR,
        UNIQUE (course, rel_path)
    );

    CREATE TABLE IF NOT EXISTS resource_whitelist (
        id SERIAL PRIMARY KEY,
        customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        resource_id INTEGER NOT NULL REFERENCES resources(id) ON UPDATE CASCADE ON DELETE CASCADE,
        course VARCHAR NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (customer_id, resource_id)
    );


    CREATE TABLE IF NOT EXISTS whitelist_for_all (
        id SERIAL PRIMARY KEY,
        resource_id INTEGER NOT NULL REFERENCES resources(id) ON UPDATE CASCADE ON DELETE CASCADE,
        course VARCHAR NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (resource_id)
    );


    CREATE TABLE IF NOT EXISTS lottery_tickets (
        id SERIAL PRIMARY KEY,
        ticket_code VARCHAR(5) NOT NULL,
        customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        course VARCHAR NOT NULL,
        discount INTEGER NOT NULL,
        base_fare INTEGER NOT NULL,
        max_discount INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        status VARCHAR NOT NULL DEFAULT 'unused',
        transferred_to INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE SET NULL,
        transferred_at TIMESTAMP,
        used_at TIMESTAMP,
        order_id VARCHAR,
        UNIQUE (ticket_code)
    );


    CREATE TABLE IF NOT EXISTS lottery_logs (
        id SERIAL PRIMARY KEY,
        customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
        course VARCHAR NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        result VARCHAR NOT NULL
    );


    CREATE TABLE IF NOT EXISTS visits (
        id SERIAL PRIMARY KEY,
        customer_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE SET NULL,
        ip_address VARCHAR NOT NULL,
        user_agent TEXT,
        device_info TEXT,
        url_path VARCHAR,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_visits_customer_id ON visits (customer_id);
    CREATE INDEX IF NOT EXISTS idx_visits_ip_address ON visits (ip_address);
    CREATE INDEX IF NOT EXISTS idx_visits_timestamp ON visits (timestamp);

    SELECT SETVAL('customers_id_seq', (SELECT MAX(id) FROM customers) + 1);
    SELECT SETVAL('registry_id_seq', (SELECT MAX(id) from registry) + 1);
    SELECT SETVAL('messages_id_seq', (SELECT MAX(id) from messages) + 1);
  
