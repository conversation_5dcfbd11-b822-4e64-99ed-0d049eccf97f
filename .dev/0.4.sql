CREATE TABLE IF NOT EXISTS price_tiers (
    id SERIAL PRIMARY KEY,
    price DECIMAL(10,2) NOT NULL UNIQUE,
    wechat_url VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

insert into price_tiers (price, wechat_url) values (9.9, 'https://mp.weixin.qq.com/s/O1zIS1ljDjQ5Ic-8xhShJQ');
insert into price_tiers (price, wechat_url) values (19.9, 'https://mp.weixin.qq.com/s/AmrvvEPhQtCGMH8kdgNGzw');
insert into price_tiers (price, wechat_url) values (39.9, 'https://mp.weixin.qq.com/s/oGKu81JcEM1bCYpvIGZdYA');
insert into price_tiers (price, wechat_url) values (79.9, 'https://mp.weixin.qq.com/s/D89F9mSbZi64L4GV4HOCDA');
insert into price_tiers (price, wechat_url) values (99.9, 'https://mp.weixin.qq.com/s/_CHq6v_0NQs1pS3_yQw48Q');
insert into price_tiers (price, wechat_url) values (199.9, 'https://mp.weixin.qq.com/s/UBd45aNYQN-L6uwUo0JJfg');
insert into price_tiers (price, wechat_url) values (360, 'https://mp.weixin.qq.com/s/9hzsTA3xGwei_1qQYf5x6w');
