# provision系统需求

## 1. 系统概述

这是一个课程客户管理系统和访问控制系统，主要功能是：
1. 客户信息管理，包括基本信息的录入、查找、修改和删除。这
2. 课程信息管理，客户报名的课程，服务级别，开课时间、结课时间。
3. 资源访问控制。根据服务级别，决定客户能访问的课程资源。比如，是使用共享服务器、还是专属服务器；是只解锁部分课程，还是能访问所有课程。客户生命期定义为engage(还未付费)、refund（已付费，可退款）、prime(已付费，不可退款，可访问套餐内所有课程)、expired(过期)。对有专属服务器的客户，在prime之后，还有一个grace time(对所有用户都一样)，在此期间不能访问专属容器，但容器仍然保留。在prime之后，expire之前，用户仍可访问课程，但用户不再享有专属服务器，也不再享有答疑服务。

### 1.1. 概念解释

#### 1.1.1. 客户
客户(customer)是指quantide的课程客户。用户是指使用本系统的人，即quantide的员工。

#### 1.1.2. 课程
课程(course)是指quantide提供的课程产品。

每门课程都有一个代码(id)，比如，l24表明是量化24课。同时，它还有一个显示名称，比如量化24课。这个映射结构定义在provision.yaml中的courses字段下。

```yaml
courses:
  - id: "l24"
    title: "量化24课"
    cover: "/src/assets/24lectures.png"
    level: 中级
    start: 1
    end: 24
```

#### 1.1.3. 套餐
套餐（plan）是每门课可以提供的不同的服务分级。一门课程可能有好几个服务级别，每个级别能访问的文件资源（url路径）、服务器（共享或者专属）、答疑服务不一样。

每门课程允许定义不同的套餐。定义在plans.yaml中。

```yaml
fa:
  plans: [bronze, silver, gold_a, gold_b, platinum, diamond]
  resources: [courseware, assignments]
  own_container: [platinum, diamond]
  # 专属容器过期后的保留时间
  retention: 30
  refund: 15
  initial: 3
  # 在refund期间，多久（天数）释放一个资源
  release_every: 2
  division: course
  exclude:
    bronze:
      - assignments
      - courseware/19.ipynb
      - courseware/20.ipynb
    silver:
      - courseware/19.ipynb
      - courseware/20.ipynb
    gold_a:
      - courseware/19.ipynb
      - assignments/questions/19.ipynb
      - assignments/answers/19.ipynb
    gold_b:
      - courseware/20.ipynb
      - assignments/questions/20.ipynb
      - assignments/answers/20.ipynb
  prime:
    bronze: 60
    silver: 90
    gold_a: 120
    gold_b: 120
    platinum: 365
    diamond: 365
  grace:
    bronze: 305
    silver: 305
    gold_a: 365
    gold_b: 365
    platinum: 365
    diamond: 365
```
这里为因子分析与机器学习策略定义了5个套餐，并详细列举了bronze套餐的配置。根据该配置，因子课所有专有容器客户的grace time是30天，platinum和diamon客户有专属容器。

bronze套餐的客户在refund期间（付款后7天内），只能访问第1-4，第8课的内容。

bronze套餐的完课期(prime)是90天。在该期间，可以访问第1-18课和第21课，但不能访问第19，20课。

## 使用场景



### 客户使用场景

#### 1. 客户注册

我们提供给客户的第一个页面，是 ke.quantide.cn/academy (测试中使用test.quantide.cn)。如果用户没有登录（localstorage中没有customerid），则跳转到academy#login，否则，应该进入academy#customerid页面。该页面将使用Academy.vue视图。
### 员工使用场景

### 1.2. 系统需求
1. 系统使用前后端分离架构。前端由vue3构建，后端由python 3.13, blacksheep, duckdb, nginx, docker容器来构建。
2. 后端开发中，使用conda来管理虚拟环境。
3. 使用tox来管理测试。
4. nginx安装在容器中，为学生创建的课程也安装在容器中，它们之间通过docker网络进行通信。provision服务安装在host机器上。
5. 只使用pathlib，而不要使用os.path。


## 2. 功能需求

### 2.1. 课程容器的构建
课程容器构建由containers.yaml来驱动。该文件中的每一个一级条目，对应course中定义的一门课程的ID。

每个课程都有home设置。这是容器进行目录映射时，相对路径的根目录。
课程容器都加入到course网络中。
#### 映射
目录映射通过container > volumes来指定。容器可能存在不同的角色（instructor, student），所以，在volumes下，又可能有all, instructor, student三个条目，其下是一个列表，每个列表都可能有from, to, mode三项。

为简洁起见，如果一个volume mapping项没有指定to, 则默认映射到容器内/home/<USER>/notebooks根目录。如果没有指定mode，则默认为ro。

缺省地，我们将额外增加以下目录映射：

```md
home/.startup > /root/.ipython/profile_default/startup
home/.config/jupyter_lab_config.py > "/root/.jupyter/jupyter_lab_config.py"
home/.data > /data
```

这里的home是指课程的根目录。在进行目录映射解析时，会进行以下宏替换：

1. from中的路径如果为相对路径，则解释为home下的相对路径。
2. to中的{account}解释为客户账号。

### nginx容器
nginx运行在容器中，也加入到course网络。它的设置通过目录映射来实现。


## 资源管理
2025年5月11日更新

#### 初始化
1. resourcemanager 会从resources.yaml中读取数据，解析资源，加载到resources表中。并且进行双向比较更新。
2. 制作受控资源 id 缓存。只有在受控制资源集合中的资源，才需要进行访问控制。
3. 制作每个cutomer_id关联的可访问资源列表 customer_resouce_id_cache （customer_id -> set(resource_id)），从而快速决定一个客户是否能访问某个资源。

#### 访问控制更新

customer_resouce_id_cache 每小时更新一次，以使得客户能访问的资源按进度更新。
在新增资源（特别是blog）时，及时进行更新。

## 我的课程

显示客户已订阅课程、推荐课程及已完结课程。

## 升级是如何完成的？

系统的主要数据存在postgres中，另有一个session文件在~/.local/run/provision/provision.sessions(该目录下还有provision.pid)。

系统的配置默认放在~/.config/provision下，有provision.yaml, resources.yaml, containers.yaml, plans.yaml, gallery.json等，以及nginx文件。

升级时，先在旧版本下，实现停止服务，备份文件（旧版本知道如何备份自己的文件），安装新版本。然后调用 provision upgrade_run_new_version来进行数据库、配置升级。这将运行在新版本上。

