这是系统的架构图。

```mermaid
graph LR
    classDef process fill:#E5F6FF,stroke:#73A6FF,stroke-width:2px;
    classDef dockerNet fill:#E6FFE6,stroke:#73FF73,stroke-width:2px;

    subgraph Internet["外网区域"]
    A([student]):::process --req1--> B
    end

    subgraph Intranet["内网区域"]
    subgraph DockerNetwork["Docker Network (course)"]
        subgraph 容器["Docker 容器"]
        B(Nginx):::process
        C(Course Container):::process
        B --req--> C
        C --response --> B
        end
    end
    B --auth--> E([Provision Server]):::process
    F([admin])  -- 管理 -->E
    end

    class DockerNetwork dockerNet
```

对此架构，有以下说明：
1. 正式部署时，通过ke.quantide.cn访问，开发测试时，通过test.quantide.cn访问。如果仅仅是为了测试前端与provision服务器（即前端通过npm run dev运行时），前端直接访问localhost:8403端口。
2. 在开发环境下，通过front/build_and_deploy.sh脚本，将前端代码打包，并部署到provision服务器的static目录中。其中academy目录被软链接到nginx容器/var/html/www/academy目录
3. 用户登录时，通过ke.quantide.cn/academy/login或者ke.quantide.cn/academy来访问
4. 登录成功后，返回front/academy/src/views/Academy.vue页面。
5. 当用户点击Academy.vue页面中的课程链接时，nginx将请求发给provision服务器，provision服务器会根据用户是否登录、当前用户所处的阶段和套餐是否允许访问该资源来决定返回状态。
6. 如果Provision允许此次请求，则nginx将请求转发给Course容器中的jupyter lab服务器。注意：访问课程容器的路径仍然使用/course前缀。

因此，在开发测试时，如果修改了前端(front/academy)下的代码，将需要运行build_and_deploy.sh脚本以部署修改后的代码。
