## story

1. 优化容器构建过程，使得我们可以在macos上也能运行课件容器。
2. 修改容器的内存配置，使其能够根据账户类型自动选择合适的内存配置。

## 容器内存修改概述

修改了 `provision/containers/env.py` 中的 `create_course_container` 函数，使其能够根据 `containers.yaml` 中的内存配置和账户类型自动选择合适的内存参数。

## 配置文件结构

在 `containers.yaml` 中，每个课程的内存配置结构如下：

```yaml
fa:
  memory:
    adhoc:
      low: "1.5g"
      high: "4g"
    share:
      low: "2g"
      high: "8g"
```

## 双镜像构建优化

在打出基础包py312时，就支持双镜像（amd64和arm64）。以此为基础构建fav3。使用时，应该将配置文件中的镜像地址改为：

```yaml
  container:
    image: fav3
```
在macos下，设置为:

```yaml
  container:
    image: fav3-arm64
```

因为目前macos为开发机，可以要求开发者手动修改这一配置。
