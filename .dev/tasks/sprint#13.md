现在访问/academy是需要登录的。希望有一个不需要登录即可访问的页面，该页面的内容有：

## 页眉

与/academy页面一样，有quantide的logo，在右侧有一个“登录”按钮，点击后跳转到登录页面。注意，这里不应该有消息提示。

## 页面内容

这里请使用比较酷炫的前端技巧，比如各种reveal特效，并且图文并茂。内容分这样几块：

一是逐一介绍介绍我们的课程，图片来知_1000.png系列。课程介绍来自于provision.yaml中的课程简介。

再介绍我们的书《Python高效编程实战指南》，图片来自 https://images.jieyu.ai/images/hot/mybook/girl-on-sofa.jpg

简介是

```
本书清楚、完整地诠释了为什么Python可以，或者说，Python应该是核心编程和系统开发的首选，从语言特性、便捷程度和可拓展性，到纵向的流程设计分析，再到横向的工具、资源和社区支持介绍，组织了大量的实例的技巧。在组织上，本书以宁缺毋滥的背景知识、主次分明的节奏、俯拾皆是的技巧和转页即遇的幽默感将知识、经验、目的和实用结合得严丝合缝，更给阅读增加了轻快愉悦的成分。
```

然后介绍创始人，请使用aaron.png那张图，简介是：

```
匡醍创始人，前 IBM/Oracle 高级软件开发经理，海豚浏览器副总裁，量化开源框架 Zillionare 开发者，Python技术专家。《Python高效编程实战指南》作者，连续创业者。

在担任职业经理人期间，曾组织开发过Verizon项目。在海豚浏览器期间，带领业务团队实现年营收过亿。
```


我们的使命是：传播知识和希望。请在合适的地方，加上这个slogan

## 页脚

显示我们的联系方式，包括：

宽粉微信（二维码， quantfans.jpg），公众号(quantide, 二维码gzh_512.jpg)

## 技术要求

当用户访问ke.quantide.cn时，提供这个页面。这个页面可以是纯粹的html页面。
