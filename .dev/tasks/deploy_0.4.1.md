## story

本次修订主要目标：
1. index.html中引用了google fonts,导致加载慢
2. 预览指向的jupyterlab地址不存在
3. 引入新构建的py312和fav3。
4. tushare账号使用代理

## 部署

1. 运行 provision upgrade ~/.backups/provision/provision-0.4.1-py3-none-any.whl --overwrite-config
2. 将~/course/下,fa/l24等目录中的.config, .startup目录复制到~/.backups/provision目录下
3. 将/root/miniconda3/envs/provision/lib/python3.12/site-packages/provision/conf/startup下的fa.py等文件拷贝到~courses对应目录下的.startup目录下
4. 从~/.backups/provision/fav3-amd64.tar中导入image
5. 删除 fa, blog, pandas下的kernel.json文件
6. 重启各course_fa_*容器
7. 登录一台fa容器，看能否取到tushare数据。
8. 删除course_fa_quantide容器和course_l24_quantide容器
