## 1. story

为了督促客户购课，需要在客户注册、登录之后，立即弹出一个抽奖对话框。根据在resources.yaml中的配置，每门课程可以抽得的奖金额不一样，并且都只可用以本门课程。除此之外，我们还需要实现导航栏的促销活动，进入后，显示已抽得的奖券，有效期，是否使用过，剩余抽奖次数。两者之间能复用的组件应该尽量复用。

每个用户一个月有若干次抽奖机会，用完之后，则不再能抽奖。

客户抽得的奖券可以转让。转让后，奖券状态显示为已转让。当客户转让抽奖券被其它客户兑现使用后，该客户可以额外获得奖券一半的价值作为引荐奖励。接受者可以按奖券面值的一半折抵。

每张券都有最低消费额，最大叠加金额，这些需要显示在奖券上，模板是：本券有最低消费额{base_fare}，可叠加，最大叠加金额{max_discount}元。本券可转让一次，转让后，您和受让者都可获得一半面值即{discount}的奖励。其中discount即为奖券实际面值。

## 2. 技术细节
### 2.1. 基本配置
resource.yaml中增加一个字段，作为配置：
```
fa:
  lottery:
    times: 5
    min: 100
    max: 500
    step: 50
    base_fare: 2000
    max_discount: 700
    expire:3
```
在配置中，times是每门课、每个人能使用的总的抽奖次数，min是抽奖券的最小面值，max是抽奖券的最大面值, step是抽奖券的面值的步进，base_fare是最低消费额，max_discount是最大叠加金额，expire是抽中的奖卷的有效期。在抽奖中，times, min, max是要使用的，而base_fare, max_discount，expire主要用于展示。

如果用户抽中奖券，必须在expire天内使用，否则视为无效。过期券应该显示在我的奖券的已过期列表中。

### 2.2. 抽奖

当客户执行抽奖时，应该在min/max间，按step随机生成一个数字。比如，在100和500之间，50的步进，则应该从100, 150, 200, 250, 300, 350, 400, 450, 500中随机选择一个数字。随机分布应该为半正态分布，即低价的概率大，高价的概率小。

每个奖券都有唯一的5位数字、大写字母组合的编号，称为券码。在生成时，不使用容易混淆的字符，比如0, O, I, L等。

### 2.3. 促销活动界面

当用户点击导航栏上的促销活动时，应该显示我的抽奖，我的奖券。

在『我的抽奖』处，首先显示活动说明。然后以卡片形式展示各门课程的抽奖活动。

图片来自resources.yaml中的cover图，标题为课程名称（title），显示已抽奖次数，剩余抽奖次数，和抽奖按钮。当点击抽奖按钮时，抽奖立即开始。抽奖以独立对话框方式显示，当它显示时，用户无法点击其它页面。


在『我的奖券』处，显示『我的奖券』，『转让给我的』两个tab。

我的奖券中，列出全部、未使用、已使用、已转让、已过期的奖券。

### 2.4. 动画

当抽奖开始时，可用抽奖次数减1，显示quantide logo旋转动画，时间为5秒。在动画下方，显示可能的随机数，当动画结束，显示最终结果，用户点击『收下』按钮后，关闭界面，保存到我的奖券中。

### 2.5. 转让

在我的奖券的『未使用』奖券中，增加一个转让按钮。点击转让后，显示券码，而不是转让者ID。由客户自行完成券码的发送。

但在 我的奖券 界面中，增加一个 接受转让 的按钮（与 我的奖券、转让给我的 在一行，右对齐）。当点击后，出现对话框，输入券码，完成转让。

完成转让后，该券码应该能在转让给我的tab下看到。

受让方可以不是注册用户。需要新增一张表格来记录在途的转让信息（初始状态1），直到受让方在系统中注册并领取奖券后，删除记录（状态置为0）

### 2.6. 使用奖券

将现在的『使用』改为『查看券码』。奖券的使用在系统外部完成。

在admin界面的导航栏，增加一个『核销优惠券』菜单项。点击后，出现对话框，输入券码，显示以下信息：

1. 归属人账号
2. 转让自（账号），如果是转让的话。否则显示为空
3. 面值/可兑额（如果是受让的，面值为原值discount，可兑额为discount/2）
4. 有效期
5. 最低限额
6. 最大叠加金额

点击核销后，将券的状态（数据库状态）置为已使用，并记录使用时间（数据库：used_at）。
