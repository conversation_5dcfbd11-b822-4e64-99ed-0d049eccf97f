我们将重构plans.py中allow_access的逻辑，以及与之相关的配置和前端显示。

由nginx传递到provision服务器进行auth_check的url可能有以下形式：

```md
http://ke.quantide.cn:8080/course/fa/aaron/lab/tree/courseware/01.ipynb
```

其中, path 是/course/fa/aaron/lab/tree/courseware/01.ipynb，这里的/course是固定前缀， /fa/aaron对应/{course_id}/{user_account}。/lab/tree是jupyterlab的前缀之一，经观察，前缀可能有：

```md
/lab/tree
/api/contents
```

未来jupyter lab也可能改变这些前缀，所以，它们应该配置在provision.yaml中：

```yaml
jupyterlab:
  prefix: ["/lab/tree", "/api/contents"]
```

在jupyterlab的前缀之后，则是资源的路径，它们对应着容器中/home/<USER>/notebooks目录下的文件。而这里的notebooks又是映射到host中的~/course/{course_id}/目录的（配置在container.yaml中）。

## 问题定义

当前的逻辑是，限制访问的资源列举在provision.yaml中，通过resources来列举。

```yaml
courses:
  - id: "l24"
    resources: [courseware, assignments/questions, assignments/answers]
    start: 1
    end: 24
```

然后在plans.yaml中，通过available来列举其中哪些章节可以访问：

```yaml
l24:
  plans: [gold, diamond]
  grace: 30
  own_container: [diamond]
  whitelist: # 所有人都可以访问的白名单
    - notebook入门.ipynb
    - 课程指南.ipynb
    - supplements
    - wip # for developers only
  gold:
    - phase: refund
      period: 15
      available: 1-7
    - phase: prime
      period: 90
      available: 1-24
    - phase: expire
      period: 180
      available: 1-24
```

根据这里的配置， 如果用户aaron当前是gold级别，处在refund期间，则 url /course/l24/aaron/lab/tree/courseware/01.ipynb和 /course/l24/aaron/lab/tree/assignments/questions/01.ipynb 是不允许访问的，而/course/l24/aaron/lab/tree/courseware/08.ipynb是不允许访问的。

这里的问题是，对不同的资源，比如courseware和assignments，它们的文件都必须遵循一样的编码，而且不能允许只访问courseware，不访问assignments。对其它文件，还要指定whitelist才能决定是否允许访问。

## 新的方案

我们定义的新方案中，把resources解释为restricted（并且重命名）,即只有在这些目录下的资源才需要进行访问控制，对不在这些目录下的，一律允许。

```yaml
courses:
  - id: "l24"
    restricted: [courseware, assignments]
    start: 1
    end: 24
```

现在，courseware和assignments目录下的所有文件，除非在根据plans.yaml中定义的availabl中可以访问的文件之外，都不可以访问。而在restricted之外的文件，全部都默认可以访问。

新的plans.yaml定义如下：

```yaml
l24:
  plans: [gold, diamond]
  gold:
    - phase: refund
      period: 15
      available: courseware/(01-04,08).ipynb
    - phase: prime
    - phase: prime
      period: 90
      available:
        - courseware/lesson[01-24].ipynb
        - assignments/questions/lesson[01-24].ipynb
        - assignments/answers/lesson[01-24].ipynb
```

available字段后面是一个资源表达式。它可能是单个字符串，也可能是一个集合。示例已经展示了这一点。资源表达式的格式是{restrict_resource_name}/{prefix}({start}-{end},{seq}).ipynb，其中{start}-{end}表示范围，{seq}表示单个资源，它们在一对()中可以出现多次。

在ServicePlan初始化时，需要将资源表达式`courseware/(01-04,08, 10-12).ipynb`展开为courseware/lesson01.ipynb, ..., courseware/lesson04.ipynb, courseware/lesson08.ipynb, courseware/lesson10.ipynb,...,courseware/lesson12.ipynb的集合。并且，各类资源都应该加载到同一个集合中，以减少分支判断。

注意，在allow规则解析时，要判断 courseware/lesson..ipynb这样的路径中，前缀courseware是否能在restricted中找到，如果不能，则意味着规则定义错误，需要抛出异常，中止程序。

如果资源表达式中序号以前缀0开头，则意味着展开后的资源名，也应该包含前缀0。比如，[01-24]应该展开为[01, 02, ..., 24]

prime之后，expire之前可访问的资源不再单独定义，它们总是与prime阶段一样。不同的时，此时应该限制/{course}/{user}/的访问，转向/{course}/{share_account}/

你需要根据以上要求对代码进行修改，并且做到前端显示、数据库（如果涉及到）和后端功能一致。应该不涉及到nginx配置的修改，如果需要修改，请说明原因，等我批准。
