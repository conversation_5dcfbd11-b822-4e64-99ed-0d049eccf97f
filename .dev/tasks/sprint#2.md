# Sprint #2: 用户认证与重定向修复

## 完成的任务

### 1. 修复登录后无法正确重定向的问题

#### 问题描述
用户在登录页面输入用户名和密码后，服务器收到 POST 请求，但客户端仍停留在登录页面，无法正确重定向到主页面。

#### 解决方案
1. **修改 `auth.py` 中的 cookie 设置**：
   - 使用直接设置 `Set-Cookie` 头的方式，确保 cookie 能够正确地传递给客户端
   - 添加了更多的调试日志，以便更好地调试问题
   ```python
   # 直接设置 Set-Cookie 头
   cookie_value = f"qsession-token={token}; Path=/; Max-Age={3600 * 24 * 30}; HttpOnly"
   response.headers.add(b'Set-Cookie', cookie_value.encode('utf-8'))
   ```

2. **修改 nginx 配置**：
   - 更新了 `/sso/customer/login` 和 `/sso/` 位置块，添加了 cookie 相关的配置
   - 添加了 `proxy_cookie_path`、`proxy_cookie_domain` 和 `proxy_pass_header Set-Cookie` 指令
   - 更新了 `proxy_params` 文件，添加了全局的 cookie 相关配置
   ```nginx
   # 确保 cookie 正确传递
   proxy_cookie_path / /;
   proxy_cookie_domain auth_backend $host;
   proxy_pass_header Set-Cookie;
   ```

3. **修改前端的登录逻辑**：
   - 确保使用 `credentials: 'include'` 选项，以便正确发送和接收 cookie
   - 添加了更多的日志记录，以便更好地调试问题
   ```javascript
   const response = await fetch('/sso/customer/login', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json'
     },
     // 确保发送和接收cookie
     credentials: 'include',
     body: JSON.stringify({
       account: this.username,
       password: this.password
     })
   });
   ```

### 2. 修复访问 `/academy` 路径时的认证问题

#### 问题描述
当访问 `/academy` 路径时，即使用户已登录，也会被重定向到登录页面，导致无限重定向循环。

#### 解决方案
1. **修改 `auth_course_request` 函数**：
   - 添加了对 URL 路径的分类处理
   - 如果请求的 URL 是以 `/academy` 开头的，只要用户已登录，就允许访问
   - 如果请求的 URL 是以 `/course` 开头的，需要通过 `sp.allow_access` 检查
   - 其他路径，默认允许访问
   ```python
   # 如果请求的 URL 是以 /academy 开头的，只要用户已登录，就允许访问
   if url.startswith('/academy'):
       logger.info(f"Access allowed for {url}: user is authenticated")
       return json({"status": "authorized"}, status=200)
   ```

2. **修复 `plans.py` 中的 `allow_access` 方法**：
   - 添加了对 `/academy` 路径的特殊处理，直接允许访问
   - 修复了路径解析逻辑，确保路径格式正确
   - 修复了规则匹配逻辑，使用 `allow_rules` 而不是 `cache_rules`
   - 添加了更多的错误处理和默认值，提高代码的健壮性
   ```python
   # 如果是 /academy 路径，则直接允许访问
   if len(path_parts) >= 2 and path_parts[1] == "academy":
       return True
   ```

### 3. 修复登录后重定向到 `/academy/` 时出现 500 错误的问题

#### 问题描述
登录后重定向到 `/academy/` 时，nginx 返回 500 错误，错误日志显示存在重定向循环。

#### 解决方案
1. **修改 nginx 配置中的位置块**：
   - 将 `alias /var/html/www/academy/` 改为 `root /var/html/www`
   - 将 `try_files $uri $uri/ /academy/index.html` 改为 `try_files /academy/index.html =404`
   - 这些修改避免了重定向循环，确保 nginx 能够正确地提供 `/academy/index.html` 文件
   ```nginx
   # 使用 root 而不是 alias，避免重定向循环
   root /var/html/www;
   index index.html;
   try_files /academy/index.html =404;
   ```

2. **修改了所有相关的位置块**：
   - `/academy/` 位置块
   - `/academy/academy` 位置块
   - `/academy/academy/` 位置块
   - 匹配 `/academy/` 路径下其他请求的位置块

## 总结

在这个 sprint 中，我们成功修复了用户认证和重定向相关的多个问题，确保用户能够正确登录并访问 `/academy` 页面。主要的改进包括：

1. 修复了 cookie 设置和传递的问题，确保用户登录状态能够正确保存和验证
2. 优化了认证逻辑，对不同路径实施不同的认证策略
3. 修复了 nginx 配置中的重定向循环问题，确保页面能够正确加载

这些修改大大提高了用户体验，使用户能够顺利完成登录流程并访问系统功能。
