## story

需要为博客文章增加一个发布功能。实现是：

1. 为admin后台增加API token认证模式。
2. 当用户把博客文章放到指定目标(~/course/blog/article)之后，可以调用admin_resource.py中的一个接口，来单个增加资源
3. 这个接口通过token认证，来保证只有admin用户才能调用这个接口。

这个接口接收 json格式的参数，其中：

- meta包含了course, resource, title, description, rel_path, publish_date, price, division, img等字段，与数据库表 resource 相对应。当接口收到请求之后，应该验证并保存记录到数据库中的resource表中。

其中，division目前只允许为『blog』。seq应该按 course, resource, division筛选出来的记录中，最大的seq值再加1. 

- allow_all,bool。如果它为ture，还要重建cache，以便让所有人能立即访问资源。

