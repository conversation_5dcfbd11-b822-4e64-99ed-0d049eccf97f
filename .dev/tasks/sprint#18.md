## story

这个功能被称为重要公告，用来显示用户协议、帮助等重要文档。它将通过xhr读取markdown文件，并在内容区渲染。

要实现这一点，要将某种markdownjs框架引入到academy中。前端会预先定义好几种component id，后端编辑时，将为这些component提供必要的文本、图片等配置。

这是一个academy侧的功能。

## 导航

在分割条之下，关于匡醍之上，增加一个『重要公告』菜单。它是一个Accrodin设计，通过点击可以展开和折叠，方便容纳更多的内容。

需要设计一个通用markdown显示组件，以便向重要公告栏目配置多篇文章。该组件只有一个配置项，即markdown的路径。

## 重要公告内容

作为示例，你先实现一个【用户协议】子菜单。最初『重要公告』栏是折叠的，点击后，展开显示【用户协议】子项目。点击用户协议子项目时，右边的内容区显示用户协议。

你需要同时考虑pc端和移动端展示。

## markdown语法支持

要支持commonmark的所有语法，特别是admonition.


