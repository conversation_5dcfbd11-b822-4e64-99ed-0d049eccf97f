# Sprint #3: 课程过滤优化与消息通知功能

## 需求概述

Sprint #3 包含两个主要任务：

1. **修复课程加载 bug**：优化客户正在学习的课程加载逻辑，过滤掉已完结和非活跃课程。
2. **实现消息通知功能**：在 academy 端展示消息，在 admin 端提供消息管理功能。

## 详细需求

### 1. 修复课程加载 bug

**当前问题**：
- 在加载客户正在学习的课程时，没有正确过滤掉已完结（expire < now）和 is_active 不为 True 的记录。

**需求**：
- 修改课程加载逻辑，确保只显示：
  - 未过期的课程（expire_time > 当前时间）
  - 活跃状态的课程（is_active = True）

**涉及文件**：
- `provision/store/registry.py`：修改 `find_registration_by_customer` 方法
- `provision/api/academy.py`：修改 `get_academy_data` 方法中的课程过滤逻辑

### 2. 实现消息通知功能

**功能概述**：
- 实现一个完整的消息通知系统，包括消息的创建、展示、标记已读和删除功能。

**Academy 端需求**：
- 在导航菜单中添加消息通知图标
- 在图标上以红色小字显示未读消息数量
- 点击图标展开消息列表，按时间倒序排列（最新消息在最上面）
- 单击消息可将其标记为已读
- 双击消息可删除消息（移动端通过左滑显示删除按钮）

**Admin 端需求**：
- 提供消息管理界面，包括：
  - 添加新消息
  - 删除现有消息
  - 编辑消息内容

**数据结构设计**：
- 消息表（messages）：
  - id：消息唯一标识
  - title：消息标题
  - content：消息内容
  - created_at：创建时间
  - sender_id：发送者ID（管理员）
  - recipient_id：接收者ID（用户）
  - is_read：是否已读
  - is_deleted：是否已删除（用户端）

**API 设计**：
1. 获取用户消息列表：
   - 路径：`/api/messages/{customer_id}`
   - 方法：GET
   - 返回：用户的消息列表，包括已读和未读消息

2. 标记消息为已读：
   - 路径：`/api/messages/{message_id}/read`
   - 方法：PUT
   - 功能：将指定消息标记为已读

3. 删除消息（用户端）：
   - 路径：`/api/messages/{message_id}`
   - 方法：DELETE
   - 功能：将消息标记为已删除（用户端）

4. 创建新消息（管理端）：
   - 路径：`/api/admin/messages`
   - 方法：POST
   - 功能：创建新消息并发送给指定用户

5. 编辑消息（管理端）：
   - 路径：`/api/admin/messages/{message_id}`
   - 方法：PUT
   - 功能：编辑现有消息内容

6. 删除消息（管理端）：
   - 路径：`/api/admin/messages/{message_id}`
   - 方法：DELETE
   - 功能：从数据库中完全删除消息

**UI 设计**：
- Academy 端：
  - 导航栏中添加消息图标，未读消息数显示为红色小字
  - 消息列表使用下拉菜单形式展示
  - 每条消息显示标题、内容预览和时间
  - 未读消息使用不同样式（如背景色）标识
  - 移动端支持左滑显示删除按钮

- Admin 端：
  - 消息管理页面使用表格展示所有消息
  - 提供添加、编辑、删除按钮
  - 消息编辑表单包含标题、内容、接收者选择等字段

## 实施计划

### 阶段 1：修复课程加载 bug
1. 修改 `find_registration_by_customer` 方法，添加过滤条件
2. 更新 `get_academy_data` 方法中的课程处理逻辑
3. 测试修复效果

### 阶段 2：数据库和后端 API 实现
1. 创建消息表结构
2. 实现消息相关的 API 端点
3. 编写消息处理逻辑

### 阶段 3：Academy 端 UI 实现
1. 添加消息通知图标和未读消息计数
2. 实现消息列表下拉菜单
3. 添加消息交互功能（标记已读、删除）

### 阶段 4：Admin 端 UI 实现
1. 创建消息管理页面
2. 实现消息添加、编辑、删除功能
3. 添加用户选择和消息预览功能

### 阶段 5：测试和优化
1. 测试所有功能点
2. 修复发现的问题
3. 优化用户体验
