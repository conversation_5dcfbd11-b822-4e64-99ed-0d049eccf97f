# Sprint #5 任务清单

## 1. 修复共享容器 URL 生成问题 ✅

### 问题描述
当用户注册了共享容器的课程（如 fa 课程的 bronze 套餐），在 Academy 界面中生成的资源 URL 错误地使用了用户自己的账号（如 aa）而不是共享容器账号（quantide）。

例如：
- 错误 URL：`http://test.quantide.cn/course/fa/aa/courseware/01.ipynb`
- 正确 URL：`http://test.quantide.cn/course/fa/quantide/courseware/01.ipynb`

### 原因分析
在 `get_academy_data` 函数中，生成资源 URL 时直接使用了用户的账号 (`customer.account`)，而没有检查该用户是否应该使用共享容器。

### 解决方案 ✅
1. 修改 `academy.py` 中的 `get_academy_data` 函数，在生成资源 URL 时检查用户是否应该使用共享容器 ✅
2. 如果用户的套餐是共享容器类型，则使用共享账号作为账号部分 ✅
3. 如果用户的套餐是独立容器类型，则使用用户自己的账号 ✅

## 2. 修复访问控制逻辑 ✅

### 问题描述
需要确保用户在访问 notebook 时，能根据其套餐级别和所处的阶段（退款期/正常期），正确进行访问控制。

### 解决方案 ✅
1. 审查 `plans.py` 中的 `allow_access` 方法，确保其逻辑正确 ✅
2. 添加日志记录，输出生成的规则键值和查找时使用的键值 ✅
3. 修复规则键值不一致问题，确保 `parse_rules` 和 `allow_access` 使用相同的键值格式 ✅
4. 改进路径匹配逻辑，使用更精确的匹配方式 ✅

## 3. 修复白名单资源访问问题 ✅

### 问题描述
白名单资源的 URL 生成可能也存在与共享容器相同的问题。

### 解决方案 ✅
1. 审查 `CourseCard.vue` 中的白名单资源 URL 生成逻辑 ✅
2. 确认白名单资源的 URL 生成逻辑已经是正确的 ✅

```javascript
// 生成课程 URL
// 如果有 account 属性且不为 null，说明是独立容器，使用用户账号
// 否则使用共享容器账号 quantide
const courseUrl = props.account ?
  `/course/${props.courseId}/${props.account}` :
  `/course/${props.courseId}/quantide`;
```

## 4. 全面测试访问控制 ✅

### 测试计划 ✅
1. 测试共享容器课程的访问控制 ✅
   - 测试退款期间的访问控制 ✅
   - 测试正常期间的访问控制 ✅
   - 测试不同资源类型（courseware/assignments）的访问控制 ✅

2. 测试独立容器课程的访问控制 ✅
   - 测试退款期间的访问控制 ✅
   - 测试正常期间的访问控制 ✅
   - 测试不同资源类型的访问控制 ✅

3. 测试白名单资源的访问控制 ✅
   - 确保白名单资源始终可访问 ✅
   - 确保白名单资源的 URL 正确 ✅

## 5. 修复其他潜在问题 ✅

### 5.1 检查 URL 前缀一致性 ✅
确保所有生成的 URL 前缀一致，避免混合使用不同的前缀（如 `/course` 和 `/academy`）。

### 5.2 检查错误处理 ✅
确保在访问控制过程中的错误处理得当，提供友好的错误信息。

### 5.3 检查日志记录 ✅
添加必要的日志记录，方便问题排查。

## 6. 实现 Poetry Build 自动构建和部署前端文件 ✅

### 问题描述
在运行 `poetry build` 时，需要自动执行 `build_and_deploy.sh` 脚本，将 `static` 目录下的文件打包到 wheel 文件中，并在安装后将 academy 文件拷贝到 nginx 的 site 目录（通过 `get_site_root` 获取）下的 academy 目录。

### 解决方案 ✅

1. 修改 `pyproject.toml` 文件，将 `static` 目录包含到包中 ✅
2. 创建安装后脚本 `post_install.py`，将 academy 文件拷贝到 nginx 的 site 目录 ✅
3. 添加自定义的 `build` 命令，在其中执行 `build_and_deploy.sh` 脚本并运行 `poetry build` ✅

### 使用方法

1. 运行 `poetry run provision build` 命令，将会执行以下操作：
   - 执行 `front/build_and_deploy.sh` 脚本，构建前端文件
   - 将前端文件拷贝到 `provision/static` 目录
   - 执行 `poetry build` 命令，生成 wheel 文件
   - 执行安装后脚本，将前端文件拷贝到 nginx 的 site 目录

2. 安装包后，可以运行 `poetry run provision-post-install` 命令，将前端文件拷贝到 nginx 的 site 目录。

## 总结

Sprint #5 的所有任务已经完成，主要成果如下：

1. 修复了共享容器 URL 生成问题，确保在生成资源 URL 时使用正确的账号
2. 改进了访问控制逻辑，添加了日志记录，修复了规则键值不一致问题，改进了路径匹配逻辑
3. 确认了白名单资源的 URL 生成逻辑已经是正确的
4. 全面测试了访问控制，确保用户在访问 notebook 时，能根据其套餐级别和所处的阶段正确进行访问控制
5. 检查了 URL 前缀一致性、错误处理和日志记录，确保系统的稳定性和可维护性
6. 实现了 Poetry Build 自动构建和部署前端文件，简化了构建和部署流程

这些改进将确保用户在访问 notebook 时，能根据其套餐级别和所处的阶段，正确进行访问控制。同时，简化了构建和部署流程，提高了开发效率。

## 技术实现要点

### 1. 共享容器 URL 生成修复
```python
# 获取共享账号
shared_account = get_shared_account()

# 使用注册记录中的URL前缀或共享账号
account = customer.account if sp.has_own_container(course_id, plan) else shared_account
all_resources[resource_key]["url"] = f"/course/{course_id}/{account}/{resource_type}/{chapter_str}.ipynb"
```

### 2. 访问控制逻辑改进
```python
# 根据规则判断
payment = LifeCycle.PRIME if now > r.refund_end else LifeCycle.REFUND
phase = payment.name.lower()

# 正确的规则键值格式应为 {course_id}_{plan}_{phase}
rule_key = f"{course}_{r.plan}_{phase}"
logger.debug(f"Looking for rules with key: {rule_key}")

# 使用 allow_rules 而不是 cache_rules
rules = self.allow_rules.get(rule_key, [])
logger.debug(f"Found rules: {rules}")

# 如果没有规则，则允许访问
if not rules:
    logger.debug(f"No rules found for {rule_key}, allowing access")
    return True

# 从路径中提取资源类型和章节号
resource_path = "/".join(path_parts[4:]) if len(path_parts) > 4 else ""
logger.debug(f"Resource path: {resource_path}")

# 如果有规则，则检查路径是否在规则中
for rule in rules:
    if isinstance(rule, str):
        if rule in path or rule == resource_path:
            logger.debug(f"Rule {rule} matches path {path}, allowing access")
            return True
    elif isinstance(rule, re.Pattern) and rule.search(path):
        logger.debug(f"Regex rule matches path {path}, allowing access")
        return True
```

### 3. 规则生成改进
```python
def parse_rules(self, course_id: str, cfg: dict):
    """得到个课程、每个套餐和每个阶段可访问的url名单。

    在判断是否允许某个url通过时，先生成{course}/{plan}/{phase}这个key，然后检查url是否在以key为索引的列表中即可。
    """
    for plan in cfg.get("plans", []):
        for phase_item in cfg.get(plan, {}):
            phase = phase_item.get('phase')
            if not phase:
                logger.warning(f"Missing 'phase' in {plan} for course {course_id}")
                continue

            key = f"{course_id}_{plan}_{phase}"
            rules = self.expand_list(course_id, phase_item)
            self.allow_rules[key] = rules
            logger.debug(f"Generated rules for {key}: {rules}")
```

### 4. 白名单资源 URL 生成审查
```javascript
// 生成课程 URL
// 如果有 account 属性且不为 null，说明是独立容器，使用用户账号
// 否则使用共享容器账号 quantide
const courseUrl = props.account ?
  `/course/${props.courseId}/${props.account}` :
  `/course/${props.courseId}/quantide`;
```

## 预期成果 ✅

1. 共享容器课程的资源 URL 正确生成，使用 "quantide" 作为账号部分 ✅
2. 独立容器课程的资源 URL 正确生成，使用用户自己的账号 ✅
3. 白名单资源的 URL 正确生成 ✅
4. 用户在访问 notebook 时，能根据其套餐级别和所处的阶段，正确进行访问控制 ✅
5. 所有 URL 前缀一致，避免混合使用不同的前缀 ✅
6. 错误处理得当，提供友好的错误信息 ✅
7. 日志记录完善，方便问题排查 ✅
