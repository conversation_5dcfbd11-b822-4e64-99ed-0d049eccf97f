# Sprint #6 任务清单

## 修改 Academy 登录页面

### 需求描述
修改 Academy 登录页面，增加以下重要信息和功能：

1. PC 端：
   - 在登录对话框右侧，放置一个图片框，随机显示 `front/academy/src/assets` 下的 24lectures.png、fa.png、pandas.png 中的一张
   - 在对话框下部，增加宽粉的微信二维码 `src/assets/quantfans.jpg`
   - 将匡醒的 logo 也移到对话框下部，与微信二维码水平对齐
   - 在二维码和 logo 的下方，显示 slogan，随机从文件 `provision/cfg/slogan.md` 中读取一行
   - 移除登录界面的 header

2. 移动端：
   - 只显示登录对话框
   - 显示宽粉二维码、logo 及 slogan

### 实现方案
1. 修改 `front/academy/src/views/Login.vue` 文件，重新设计登录页面布局
2. 实现随机图片显示功能
3. 添加读取 slogan 的功能
4. 根据屏幕尺寸实现响应式设计，区分 PC 端和移动端显示

### 技术要点
1. 使用 Vue 的 `computed` 属性实现随机图片选择
2. 使用 `fetch` API 读取 slogan 文件内容
3. 使用 CSS 媒体查询实现响应式设计
4. 使用 flexbox 或 grid 布局实现页面结构
