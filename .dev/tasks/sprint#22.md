## story

这个sprint我们将支持blog中单篇文章的购买。在研报博文菜单下，显示的各个文章的card，会在现有基础上，增加定价标签和购买按钮。当点击购买按钮时，弹出提示框，提示用户将通过 公众号 文章付费进行购买，并告知操作细节。用户确认后，跳转到公众号付费文章链接。

用户购买后，需要自行联系客服开通文章权限。此部分不在本次sprint的实现范围内。

## 设置

文章的定价采用固定的档位，分别是：

9.9, url
19.9, url
39.9, url
79.9, url
99.9,url
199.9,url
360,url

这些档位将定义在数据库表中。不需要提供修改这些设置的界面。我们将通过数据库管理工具来直接修改它们。但需要提供API可以查询它们，返回json格式。

在资源管理中，定价栏改为选择框，从9.9/19.9这样的数字中进行选择。

## 文章展示和访问控制

根据用户是否订阅blog，文章的访问控制逻辑不同：

1. price_tier_id为NUll的资源，当成公开资源看，允许任何人访问。
2. 如果用户已获得某个资源的授权，则无论他有没有订阅博客，都允许访问。
3. 如果用户没有订阅博客，并且没有获得资源的授权，则不允许访问。
4. 如果用户已订阅博客，则按plans中的授权逻辑，与course类似处理（即，一开始允许访问intial个资源，然后按release_every来锁，但是，排序方式不同）
5. 对订阅博客，按initial和release_every来处理时，需要先排除用户已经被允许访问（授权、公开资源）的资源。

根据用户是否订阅blog，academy中卡片的展示逻辑不同：

1. 如果用户被允许访问某个资源，则显示『阅读文章』按钮，点击后入该资源
2. 如果用户不允许访问某个资源
   1. 并且用户订阅了博客，表明是进度控制原因，请显示[等待解锁]，通过tooltip提示『将于{date}解锁』
   2. 并且用户没有订阅博客，则显示价格按钮，当用户点击时，弹出付费提示框。

扩大blog菜单下的文章描述字符数，允许最多120个字符。

## 购买

用户点击价格时，弹出对话框，提示：

即将跳转到公众号平台完成付款。付款完成后，请联系助理开通文章权限。

在用户点周确认之后，在新的窗口打开链接。
