## story

现在，我们将实现一个重构：


![](https://images.jieyu.ai/images/2025/05/20250524195445.png)


在图中，右侧是一个菜单项，通过读取/academy/bulletin.json（这是服务器路径，你可以直接使用）来获菜单项。
示例如下：

{
    "重要公告": [
         {"title": "抽奖规则", "url": "/academy/markdown/lucky-draw-readme.md"}
         {"title": "用户协议", "url": "/academy/markdown/term-of-service.md"}
         {"title": "隐私政策", "url": "/academy/markdown/privacy-policy.md"}
    ],
    "资源推荐": [
         {"title": "量化24课", "url": "/academy/markdown/l24-details.md"},
         {"title": "因子分析与机器学习策略", "url": "/academy/markdown/fa-details.md"},
         {"title": "量化人的Numpy&Pandas", "url": "/academy/markdown/pandas-details.md"}
    ],  
    "帮助中心": [
         {"title": "常见问题", "url": "/academy/markdown/faq.md"}
    ]"
}

这里的重要公告、资源推荐等是一级菜单，下面的条目是二级菜单（显示项为title）。宽度不够的使用...

之前方案的问题在于，不能实现动态扩展。在这个sprint中，我们至少要实现三级菜单可以随意增加。

## 实现

在实现中，可以将![](https://images.jieyu.ai/images/2025/05/20250524195445.png)图中，右侧的菜单挪到重要公告下。重要公告是一个according组件（而是不是之前的普通元素），通过单击来展开或者折叠。
