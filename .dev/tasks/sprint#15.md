## 1. story

之前的access control是通过章节和available list来组成的，这种资源控制的方式不太灵活。在这个sprint中，我们将实现更灵活的控制方法。

受控资源都是*.ipynb文件，客户访问它们的路径为 {scheme}{host}:{port}/{path}/{title}.ipynb?resourceId={resourceId}，其中, {path}/{title}.ipynb在nginx中，被称为request_uri。

每一个客户，都有一个可访问资源组，资源组是一个资源id组成的集合(set)。如果链接带resourceId查询参数，则优先查询customer_id对应的资源组是否有此资源；否则，查询customer_id对应的资源hash集合是否有此request_uri。

每个客户的可访问资源组，将在以下时刻进行动态生成：

1. 注册课程或者修改课程时
2. 每小时运行scheduled job，动态调整资源组
3. 在admin界面手动调整

<!-- ## 增加文章

Provision服务器应该提供一个免登录的接口 post articles/，用来增加文章。参数为：

```
title: 文章标题
date: 发表时间
desc: 文章简介
price: 文章价格
path: 文件在本地的路径
```

返回文章的访问链接：{scheme}{host}:{port}/articles/{path}/{title}.ipynb?articleId={articleId} -->

## 2. 技术细节
### 2.1. 资源编号

我们通过一个预处理过程对所有受控资源进行编号，保存到数据库中，在之后访问资源时，通过资源编号来确定是否允许访问。

resources表由以下字段组成：

1. id
2. divisioin，资源的种类，即 l24, fa, pandas等。
3. restriced: 受控的子目录。该子目录下所有*.ipynb都将受控。
4. seq，资源的序号。在课程中，我们会按seq逐步放开控制。
5. title, 资源的标题，通过front yaml中的title属性来获取，如果不存在，则使用文件名（去掉.ipynb）
6. desc，可选。资源简介，从front yaml中的title属性来获取。如果不存在，寻找第一个<!--readmore-->，或者 --- 之前，不足120字的部分。
7. rel_path，是资源在本地的路径，但去掉该资源所属的course的home前缀之后的路径。比如资源在本地是~/courses/l24/assignments/questions/assignment1.ipynb，course中home定义为~/courses/l24，则路径为assignments/questions/assignment1.ipynb

## 3. 资源管理

### 3.1. 资源初始化
在程序启动时，需要truncate resources表格，然后根据resource.yaml中的定义，初始化表格。

### 3.2. 显示所有的 resources 记录

在admin后台的资源管理页面初始化时，需要读取 resources 表格的全部记录，并在 course, restricted 列的表头，增加按 course 和restricted 进行 filter 的功能。

这一步显示的表格不要求可编辑。

### 3.3. 更新 resources记录

在 3.1 中生成的视图中，增加一个按钮， scan， 点击后，它将：

1. 读取 resources.yaml 文件，进行遍历。
2. 对其中每一个一级的key(比如l24, fa)，通过cfg.get_course_home找到该course的home目录。
3. 根据 restricted 列表中的值，对home/{restriced_item} 文件夹进行递归搜索，找到所有的*.ipynb为受控资源
4. 对每一个文件的stem部分，如果能转换为整数，则用转换后的数字作为seq，否则用它们的创建时间进行排序，从1开始往后递增。
5. 从*.ipynb中提取title, desc，由 {restriced_item} / path /to/*.ipynb级成rel_path，以及key(作为course字段)

将上述信息组成一个表格在前端显示。其中seq字段可编辑。资源按每页20条显示。表格要能按 {course}, {restriced_item}进行过滤。使用表头过滤的样式。

在点击保存按钮时，先将 resources表格 truncated掉，再插入记录。

### 3.4. 课程和研究平台注册

在为客户注册课程时，根据客户选择的套餐，通过avaliable来生成访问控制，在存入数据库之前完成到资源id的转换。与之前的课程注册不同的是，注册对话框除了显示现有条目之外，还需要显示所有受控资源，并在每一个资源之前，加一个单选框，以便可以修改默认配置。

在页面布局上，建议横向扩展现有的对话框，在右侧放该门课程的受控资源列表。

在保存时，之前的课程注册表仍然保留。

### 3.5. hourly job

在后台运行一个每小时刷新的定时任务。如果当前客户仍在refund期内，则按照 plans.release_every,每隔release_every天，增加一个可访问资源。在增加资源时，先通过orderby和order对资源组进行排序，再在initila分配的资源的基础上，依次增加。

### 3.6. admin 资源授权页面

在该页面，有一个客户搜索框和 course 搜索框，当搜索、选定客户和course之后，加载该course下所有资源（通过plans中的 resources 字段。在id列之后，显示一个checkbox，表明该资源是否可访问。

分页显示，每页显示20个资源。通过保存按钮统一进行保存。

此更新不涉及 registry 更新。在admin处额外授权的资源，将存入该客户的资源白名单（resource_whitelist）

此页面可用以研究平台单个资源授权。

## 4. 其它

在本功能实现之后，为客户 我的课程 页面生成的课程列表的link,应该都带上resourceId查询参数。课程解锁时间应该根据refund time和progress来进行计算。过了refund时间后，全部解锁。

## story

在sprint#15之后，用户访问受控资源时，可能是通过我们给出的url，这种url会带resourceId查询参数。另一种是通过 jupyterlab列表给出的url，不带查询参数。

对于前者，如果resourceId在资源列表中，并且也在客户的资源组中，则允许通过；否则block。对于后者，我们先通过request_uri来查询资源id，再照前者逻辑进行判断.
