## customers

| 字段          | 说明             |
| ------------- | ---------------- |
| id            | 自增             |
| account       | 账号，唯一       |
| password      | 密码             |
| nickname      | 昵称             |
| salt          | 盐，服务器生成   |
| phone         | 手机号           |
| wx            | 微信号           |
| occupation    | 职业，可为空     |
| channel       | 注册渠道，可为空 |
| register_time | 注册时间         |
| is_active     | 是否活跃         |

## registry

| 字段        | 说明                        |
| ----------- | --------------------------- |
| id          | 自增                        |
| course      | 课程                        | 字符串 |
| customer_id | 整数，外键，关联customer.id |
| plan        | 字符串，套餐                |
| start_time  | 开始时间                    |
| refund_end  | 退款截止时间                |
| prime_end   | 课程服务时间                |
| grace_end   | 专属容器时间                |
| expire_time | 课程过期时间                |
| is_active   | 记录是否有效                |


### resources 表格

``` md
    id 自增
    course
    resource， 比如courseware, assignments/questions, assignments/answers
    seq 资源排序后的序号，用于进度控制
    title 资源名
    description 资源描述，从notebook的的metadata中获取 
    rel_path 资源在磁盘上的相对路径，会成为url的一部分，包含了resouce字段信息
    publish_date 资源发布日期，从notebook的的metadata中获取
    price 资源价格
    division: 课程的门类，即course, blog。两者展示机制不同、资源控制进度方式不同。
```
course + rel_path 唯一确定一个资源。
course + resource 确定了一个可排序的类别

## resource-plan

用来记录每个plan可访问的资源
