# 博客文章发布 API 文档

## 概述

这个 API 接口用于发布博客文章到资源表。它通过 API token 认证，确保只有授权用户才能调用。

## 接口信息

- **URL**: `/api/admin/resources/publish`
- **方法**: `POST`
- **认证**: API Token

## 认证方式

API token 可以通过以下三种方式提供：

1. **Authorization 头** (推荐):
   ```
   Authorization: Bearer <token>
   ```

2. **X-API-Token 头**:
   ```
   X-API-Token: <token>
   ```

3. **查询参数**:
   ```
   ?api_token=<token>
   ```

## 请求参数

### 请求体 (JSON)

```json
{
  "meta": {
    "course": "blog",
    "resource": "articles",
    "title": "文章标题",
    "description": "文章描述",
    "rel_path": "article.ipynb",
    "division": "blog",
    "publish_date": "2024-01-01T00:00:00",
    "price": 0.0,
    "price_tier_id": null,
    "img": null
  },
  "allow_all": true
}
```

### 参数说明

#### meta (必需)
包含资源表中的字段信息：

- `course` (string, 必需): 课程ID，通常为 "blog"
- `resource` (string, 必需): 资源类型，如 "articles"
- `title` (string, 必需): 文章标题
- `description` (string, 可选): 文章描述
- `rel_path` (string, 必需): 相对路径
- `division` (string, 必需): 资源门类，通常为 "blog"
- `publish_date` (string, 可选): 发布时间 (ISO 格式)
- `price` (number, 可选): 价格，默认 0.0
- `price_tier_id` (integer, 可选): 价格档位ID
- `img` (string, 可选): 封面图片URL

**注意**: `seq` 字段会根据相同 `course`、`resource`、`division` 的现有记录自动生成（最大值+1），无需客户端提供。

#### allow_all (可选)
- 类型: boolean
- 默认值: false
- 说明: 是否允许所有人访问。如果为 true，资源会被添加到公开白名单并重建缓存

## 响应

### 成功响应 (200)

```json
{
  "id": 123,
  "message": "Resource published successfully",
  "public": false
}
```

如果 `allow_all=true`：

```json
{
  "id": 123,
  "message": "Resource published successfully and made public",
  "public": true
}
```

### 错误响应

#### 401 Unauthorized
```json
{
  "error": "API token required"
}
```

#### 400 Bad Request
```json
{
  "error": "Missing required field: meta"
}
```

```json
{
  "error": "Missing required field in meta: title"
}
```

#### 500 Internal Server Error
```json
{
  "error": "Database error message"
}
```

## 使用示例

### Python 示例

```python
import requests
import json
from datetime import datetime

# 配置
API_BASE_URL = "http://localhost:403"
API_TOKEN = "your_api_token_here"

# 准备数据
data = {
    "meta": {
        "course": "blog",
        "resource": "articles",
        "title": "我的第一篇博客",
        "description": "这是一篇测试博客文章",
        "rel_path": "my_first_blog.ipynb",
        "division": "blog",
        "publish_date": datetime.now().isoformat(),
        "price": 0.0
    },
    "allow_all": True  # 设为公开文章
}

# 发送请求
response = requests.post(
    f"{API_BASE_URL}/api/admin/resources/publish",
    headers={
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_TOKEN}"
    },
    json=data
)

# 处理响应
if response.status_code == 200:
    result = response.json()
    print(f"文章发布成功，ID: {result['id']}")
    if result['public']:
        print("文章已设为公开")
else:
    print(f"发布失败: {response.json()}")
```

### curl 示例

```bash
curl -X POST http://localhost:403/api/admin/resources/publish \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_token_here" \
  -d '{
    "meta": {
      "course": "blog",
      "resource": "articles",
      "title": "测试文章",
      "rel_path": "test.ipynb",
      "division": "blog"
    },
    "allow_all": true
  }'
```

## 注意事项

1. **API Token 安全**: 请妥善保管 API token，不要在客户端代码中硬编码
2. **资源路径**: `rel_path` 应该是相对于课程根目录的路径
3. **缓存更新**: 当 `allow_all=true` 时，系统会自动重建资源访问缓存，可能需要一些时间
4. **重复资源**: 如果相同的 `course` 和 `rel_path` 已存在，会更新现有记录而不是创建新记录
