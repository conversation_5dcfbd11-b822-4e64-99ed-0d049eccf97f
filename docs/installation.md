# Installation

## Stable release

To install provision, run this command in your
terminal:

``` console
pip install provision
```

This is the preferred method to install provision, as it will always install the most recent stable release.

If you don't have [pip][] installed, this [Python installation guide][]
can guide you through the process.

## From source

The source for provision can be downloaded from
the [Github repo][].

You can either clone the public repository:

``` console
git clone git://github.com/zillionare/provision
```

Or download the [tarball][]:

``` console
curl -OJL https://github.com/zillionare/provision/tarball/master
```

Once you have a copy of the source, you can install it with:

``` console
pip install .
```

  [pip]: https://pip.pypa.io
  [Python installation guide]: http://docs.python-guide.org/en/latest/starting/installation/
  [Github repo]: https://github.com/%7B%7B%20cookiecutter.github_username%20%7D%7D/%7B%7B%20cookiecutter.project_slug%20%7D%7D
  [tarball]: https://github.com/%7B%7B%20cookiecutter.github_username%20%7D%7D/%7B%7B%20cookiecutter.project_slug%20%7D%7D/tarball/master
