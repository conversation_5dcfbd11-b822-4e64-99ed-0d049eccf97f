# 事务安全性修复

## 问题描述

在最初的实现中，查询最大 seq 值和插入新记录是分别执行的，没有使用事务保护。这在并发环境下可能导致竞态条件：

```
线程A: 查询 MAX(seq) = 3
线程B: 查询 MAX(seq) = 3  (同时执行)
线程A: 插入 seq = 4
线程B: 插入 seq = 4  (重复!)
```

这会导致 seq 字段重复，违反业务逻辑。

## 解决方案

使用数据库事务和行级锁来确保 seq 生成和插入的原子性：

### 1. 事务包装

将查询和插入操作包装在同一个事务中：

```python
conn = pg.conn_pool.getconn()
try:
    with conn:  # 自动事务管理
        with conn.cursor() as cursor:
            # 查询和插入在同一事务中
            ...
finally:
    pg.conn_pool.putconn(conn)
```

### 2. 行级锁

使用 `FOR UPDATE` 锁定相关行，防止并发读取：

```sql
SELECT COALESCE(MAX(seq), 0) as max_seq 
FROM resources 
WHERE course = %s AND resource = %s AND division = %s
FOR UPDATE
```

### 3. 原子操作

在同一事务中完成查询和插入：

```python
# 1. 锁定并查询最大 seq
cursor.execute(max_seq_sql, [course, resource_type, division])
max_seq = cursor.fetchone()[0]

# 2. 生成新 seq
new_seq = max_seq + 1

# 3. 插入新记录
cursor.execute(insert_sql, [..., new_seq, ...])
```

## 技术实现

### 完整的事务代码

```python
# 使用事务确保 seq 生成和插入的原子性
conn = pg.conn_pool.getconn()
try:
    with conn:
        with conn.cursor() as cursor:
            # 在事务中查询最大 seq 值，使用 FOR UPDATE 锁定相关行
            max_seq_sql = """
                SELECT COALESCE(MAX(seq), 0) as max_seq 
                FROM resources 
                WHERE course = %s AND resource = %s AND division = %s
                FOR UPDATE
            """
            cursor.execute(max_seq_sql, [course, resource_type, division])
            result = cursor.fetchone()
            max_seq = result[0] if result else 0
            
            # 新的 seq 值为最大值加 1
            new_seq = max_seq + 1
            meta["seq"] = new_seq
            
            # 在同一事务中插入新记录
            insert_sql = """
                INSERT INTO resources (course, resource, seq, title, description, rel_path, publish_date, price, price_tier_id, division, img)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            
            cursor.execute(insert_sql, [
                meta["course"], meta["resource"], meta["seq"],
                meta["title"], meta.get("description"), meta["rel_path"],
                meta.get("publish_date"), meta.get("price", 0.0),
                meta.get("price_tier_id"), meta["division"], meta.get("img")
            ])
            
            result = cursor.fetchone()
            resource_id = result[0] if result else None
            
            if not resource_id:
                raise ValueError("Failed to insert resource")
                
except Exception as e:
    logger.error(f"Error in transaction: {e}")
    raise
finally:
    pg.conn_pool.putconn(conn)
```

## 并发安全性分析

### 场景 1: 正常并发

```
时间线:
T1: 线程A开始事务
T2: 线程A执行 SELECT ... FOR UPDATE (锁定)
T3: 线程B开始事务
T4: 线程B尝试 SELECT ... FOR UPDATE (等待锁)
T5: 线程A插入记录并提交
T6: 线程B获得锁，读取到更新后的最大值
T7: 线程B插入记录并提交
```

结果：seq 值正确递增，无重复。

### 场景 2: 空表初始化

```
时间线:
T1: 线程A查询空表，MAX(seq) = 0
T2: 线程B查询空表，等待锁
T3: 线程A插入 seq = 1
T4: 线程B获得锁，MAX(seq) = 1
T5: 线程B插入 seq = 2
```

结果：正确处理空表情况。

### 场景 3: 异常回滚

```
时间线:
T1: 线程A开始事务
T2: 线程A查询 MAX(seq) = 5
T3: 线程A准备插入 seq = 6
T4: 插入失败（如约束违反）
T5: 事务自动回滚
T6: 锁释放，其他线程可以继续
```

结果：异常情况下正确回滚，不影响数据一致性。

## 性能考虑

### 锁的影响

- **锁定范围**: 只锁定匹配 `course`、`resource`、`division` 的行
- **锁定时间**: 仅在事务执行期间，通常几毫秒
- **并发度**: 不同课程/资源类型可以并发执行

### 优化建议

1. **索引优化**: 在 `(course, resource, division)` 上创建复合索引
2. **事务时间**: 保持事务尽可能短
3. **连接池**: 使用连接池避免连接开销

## 测试验证

### 单元测试

```bash
python -m pytest tests/test_publish_resource_api.py -v
```

### 并发测试

```bash
python test_transaction_safety.py
```

这个测试会：
- 启动多个线程同时创建资源
- 验证所有资源 ID 的唯一性
- 检查 seq 值的正确性

### 压力测试

可以增加线程数和迭代次数来进行压力测试：

```python
num_threads = 10
iterations_per_thread = 5
```

## 总结

通过引入事务和行级锁，我们解决了并发环境下 seq 字段重复的问题：

1. ✅ **原子性**: 查询和插入在同一事务中
2. ✅ **一致性**: 使用 FOR UPDATE 防止并发读取
3. ✅ **隔离性**: 事务隔离确保数据一致性
4. ✅ **持久性**: 事务提交后数据持久化

这个修复确保了在高并发环境下 seq 字段的正确性和唯一性，满足了生产环境的要求。
