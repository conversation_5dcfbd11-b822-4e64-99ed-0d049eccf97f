[mypy]
# Ensure we know what we do
warn_redundant_casts = true
warn_unused_ignores = true
warn_unused_configs = true

# Imports management
ignore_missing_imports = false

# Ensure full coverage
disallow_untyped_defs = true
#disallow_incomplete_defs = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
# Restrict dynamic typing (a little)
# e.g. `x: List[Any]` or x: List`
disallow_any_generics = true

# Show errors codes
show_error_codes = true

# From functions not declared to return Any
warn_return_any = true

[mypy-fire]
ignore_missing_imports = true
