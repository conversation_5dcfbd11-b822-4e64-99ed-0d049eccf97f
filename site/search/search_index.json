{"config": {"indexing": "full", "lang": ["en"], "min_search_length": 3, "prebuild_index": false, "separator": "[\\s\\-]+"}, "docs": [{"location": "", "text": "provision ¶ Skeleton project created by Python Project Wizard (ppw) Features ¶ TODO Credits ¶ This package was created with the ppw tool. For more information, please visit the project page .", "title": "home"}, {"location": "#provision", "text": "Skeleton project created by Python Project Wizard (ppw)", "title": "provision"}, {"location": "#features", "text": "TODO", "title": "Features"}, {"location": "#credits", "text": "This package was created with the ppw tool. For more information, please visit the project page .", "title": "Credits"}, {"location": "api/", "text": "Top-level package for provision. app ¶ Main module. cli ¶ Console script for provision. help () ¶ help Source code in provision/cli.py def help () -> None : \"\"\"help\"\"\" print ( \"provision\" ) print ( \"=\" * len ( \"provision\" )) print ( \"Skeleton project created by Python Project Wizard (ppw)\" ) main () ¶ help Source code in provision/cli.py def main () -> None : \"\"\"help\"\"\" fire . Fire ({ \"help\" : help })", "title": "modules"}, {"location": "api/#provision.app", "text": "Main module.", "title": "app"}, {"location": "api/#provision.cli", "text": "Console script for provision.", "title": "cli"}, {"location": "api/#provision.cli.help", "text": "help Source code in provision/cli.py def help () -> None : \"\"\"help\"\"\" print ( \"provision\" ) print ( \"=\" * len ( \"provision\" )) print ( \"Skeleton project created by Python Project Wizard (ppw)\" )", "title": "help()"}, {"location": "api/#provision.cli.main", "text": "help Source code in provision/cli.py def main () -> None : \"\"\"help\"\"\" fire . Fire ({ \"help\" : help })", "title": "main()"}, {"location": "authors/", "text": "Credits ¶ Development Lead ¶ <NAME_EMAIL> Contributors ¶ None yet. Why not be the first?", "title": "authors"}, {"location": "authors/#credits", "text": "", "title": "Credits"}, {"location": "authors/#development-lead", "text": "<NAME_EMAIL>", "title": "Development Lead"}, {"location": "authors/#contributors", "text": "None yet. Why not be the first?", "title": "Contributors"}, {"location": "contributing/", "text": "Contributing ¶ Contributions are welcome, and they are greatly appreciated! Every little bit helps, and credit will always be given. You can contribute in many ways: Types of Contributions ¶ Report Bugs ¶ Report bugs at https://github.com/zillionare/provision/issues. If you are reporting a bug, please include: Your operating system name and version. Any details about your local setup that might be helpful in troubleshooting. Detailed steps to reproduce the bug. Fix Bugs ¶ Look through the GitHub issues for bugs. Anything tagged with \"bug\" and \"help wanted\" is open to whoever wants to implement it. Implement Features ¶ Look through the GitHub issues for features. Anything tagged with \"enhancement\" and \"help wanted\" is open to whoever wants to implement it. Write Documentation ¶ provision could always use more documentation, whether as part of the official provision docs, in docstrings, or even on the web in blog posts, articles, and such. Submit Feedback ¶ The best way to send feedback is to file an issue at https://github.com/zillionare/provision/issues. If you are proposing a feature: Explain in detail how it would work. Keep the scope as narrow as possible, to make it easier to implement. Remember that this is a volunteer-driven project, and that contributions are welcome :) Get Started! ¶ Ready to contribute? Here's how to set up provision for local development. Fork the provision repo on GitHub. Clone your fork locally 1 $ <NAME_EMAIL>:your_name_here/provision.git Ensure poetry is installed. Install dependencies and start your virtualenv: 1 $ poetry install -E test -E doc -E dev Create a branch for local development: 1 $ git checkout -b name-of-your-bugfix-or-feature Now you can make your changes locally. When you're done making changes, check that your changes pass the tests, including testing other Python versions, with tox: 1 $ tox Commit your changes and push your branch to GitHub: 1 2 3 $ git add . $ git commit -m \"Your detailed description of your changes.\" $ git push origin name-of-your-bugfix-or-feature Submit a pull request through the GitHub website. Pull Request Guidelines ¶ Before you submit a pull request, check that it meets these guidelines: The pull request should include tests. If the pull request adds functionality, the docs should be updated. Put your new functionality into a function with a docstring, and add the feature to the list in README.md. The pull request should work for Python 3.6, 3.7, 3.8, 3.9 and for PyPy. Check https://github.com/zillionare/provision/actions and make sure that the tests pass for all supported Python versions. Tips``` ¶ 1 $ pytest tests.test_provision ```To run a subset of tests. Deploying ¶ A reminder for the maintainers on how to deploy. Make sure all your changes are committed (including an entry in HISTORY.md). Then run: 1 2 3 $ poetry patch # possible: major / minor / patch $ git push $ git push --tags Github Actions will then deploy to PyPI if tests pass.", "title": "contributing"}, {"location": "contributing/#contributing", "text": "Contributions are welcome, and they are greatly appreciated! Every little bit helps, and credit will always be given. You can contribute in many ways:", "title": "Contributing"}, {"location": "contributing/#types-of-contributions", "text": "", "title": "Types of Contributions"}, {"location": "contributing/#report-bugs", "text": "Report bugs at https://github.com/zillionare/provision/issues. If you are reporting a bug, please include: Your operating system name and version. Any details about your local setup that might be helpful in troubleshooting. Detailed steps to reproduce the bug.", "title": "Report Bugs"}, {"location": "contributing/#fix-bugs", "text": "Look through the GitHub issues for bugs. Anything tagged with \"bug\" and \"help wanted\" is open to whoever wants to implement it.", "title": "Fix Bugs"}, {"location": "contributing/#implement-features", "text": "Look through the GitHub issues for features. Anything tagged with \"enhancement\" and \"help wanted\" is open to whoever wants to implement it.", "title": "Implement Features"}, {"location": "contributing/#write-documentation", "text": "provision could always use more documentation, whether as part of the official provision docs, in docstrings, or even on the web in blog posts, articles, and such.", "title": "Write Documentation"}, {"location": "contributing/#submit-feedback", "text": "The best way to send feedback is to file an issue at https://github.com/zillionare/provision/issues. If you are proposing a feature: Explain in detail how it would work. Keep the scope as narrow as possible, to make it easier to implement. Remember that this is a volunteer-driven project, and that contributions are welcome :)", "title": "Submit <PERSON>"}, {"location": "contributing/#get-started", "text": "Ready to contribute? Here's how to set up provision for local development. Fork the provision repo on GitHub. Clone your fork locally 1 $ <NAME_EMAIL>:your_name_here/provision.git Ensure poetry is installed. Install dependencies and start your virtualenv: 1 $ poetry install -E test -E doc -E dev Create a branch for local development: 1 $ git checkout -b name-of-your-bugfix-or-feature Now you can make your changes locally. When you're done making changes, check that your changes pass the tests, including testing other Python versions, with tox: 1 $ tox Commit your changes and push your branch to GitHub: 1 2 3 $ git add . $ git commit -m \"Your detailed description of your changes.\" $ git push origin name-of-your-bugfix-or-feature Submit a pull request through the GitHub website.", "title": "Get Started!"}, {"location": "contributing/#pull-request-guidelines", "text": "Before you submit a pull request, check that it meets these guidelines: The pull request should include tests. If the pull request adds functionality, the docs should be updated. Put your new functionality into a function with a docstring, and add the feature to the list in README.md. The pull request should work for Python 3.6, 3.7, 3.8, 3.9 and for PyPy. Check https://github.com/zillionare/provision/actions and make sure that the tests pass for all supported Python versions.", "title": "Pull Request Guidelines"}, {"location": "contributing/#tips", "text": "1 $ pytest tests.test_provision ```To run a subset of tests.", "title": "Tips```"}, {"location": "contributing/#deploying", "text": "A reminder for the maintainers on how to deploy. Make sure all your changes are committed (including an entry in HISTORY.md). Then run: 1 2 3 $ poetry patch # possible: major / minor / patch $ git push $ git push --tags Github Actions will then deploy to PyPI if tests pass.", "title": "Deploying"}, {"location": "history/", "text": "History ¶ 0.1.0 (2025-03-19) ¶ First release on PyPI.", "title": "history"}, {"location": "history/#history", "text": "", "title": "History"}, {"location": "history/#010-2025-03-19", "text": "First release on PyPI.", "title": "0.1.0 (2025-03-19)"}, {"location": "installation/", "text": "Installation ¶ Stable release ¶ To install provision, run this command in your terminal: 1 pip install provision This is the preferred method to install provision, as it will always install the most recent stable release. If you don't have pip installed, this Python installation guide can guide you through the process. From source ¶ The source for provision can be downloaded from the Github repo . You can either clone the public repository: 1 git clone git://github.com/zillionare/provision Or download the tarball : 1 curl -OJL https://github.com/zillionare/provision/tarball/master Once you have a copy of the source, you can install it with: 1 pip install .", "title": "installation"}, {"location": "installation/#installation", "text": "", "title": "Installation"}, {"location": "installation/#stable-release", "text": "To install provision, run this command in your terminal: 1 pip install provision This is the preferred method to install provision, as it will always install the most recent stable release. If you don't have pip installed, this Python installation guide can guide you through the process.", "title": "Stable release"}, {"location": "installation/#from-source", "text": "The source for provision can be downloaded from the Github repo . You can either clone the public repository: 1 git clone git://github.com/zillionare/provision Or download the tarball : 1 curl -OJL https://github.com/zillionare/provision/tarball/master Once you have a copy of the source, you can install it with: 1 pip install .", "title": "From source"}, {"location": "usage/", "text": "Usage ¶ To use provision in a project 1 import provision", "title": "usage"}, {"location": "usage/#usage", "text": "To use provision in a project 1 import provision", "title": "Usage"}]}