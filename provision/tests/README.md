# 单元测试说明

本目录包含 provision 项目的单元测试和集成测试。

## 测试文件

- `test_sms.py`: 短信验证码功能的单元测试
- `test_api_sms.py`: 短信验证码 API 端点的集成测试
- `run_tests.py`: 测试运行脚本

## 运行测试

### 运行所有测试

```bash
cd provision
python tests/run_tests.py
```

### 运行特定测试文件

```bash
cd provision
python -m unittest tests/test_sms.py
```

### 运行特定测试用例

```bash
cd provision
python -m unittest tests.test_sms.TestSMSFunctions.test_send_verification_code_success
```

## 测试覆盖率

要生成测试覆盖率报告，需要安装 `coverage` 包：

```bash
pip install coverage
```

然后运行：

```bash
cd provision
coverage run tests/run_tests.py
coverage report -m
```

生成 HTML 格式的覆盖率报告：

```bash
coverage html
```

然后在浏览器中打开 `htmlcov/index.html` 查看报告。

## 注意事项

1. 测试使用了 `unittest.mock` 模块来模拟外部依赖，如阿里云 SMS 客户端。
2. 集成测试使用了 `blacksheep.testing.TestClient` 来模拟 HTTP 请求。
3. 测试会自动清理缓存，不会影响生产环境。
4. 运行测试不会发送实际的短信，所有外部调用都被模拟。
