"""
Simple CAPTCHA generation and verification for human verification.
"""

import base64
import io
import random
import string
import time
from typing import Dict, <PERSON><PERSON>

from loguru import logger
from PIL import Image, ImageDraw, ImageFont

# Cache to store CAPTCHA codes with expiration time and usage status
# Format: {captcha_id: (code, expiration_timestamp, is_verified)}
captcha_cache: Dict[str, Tuple[str, float, bool]] = {}

# Initialize with a test entry to ensure the cache is working
test_id = "test_captcha_id"
test_code = "TEST"
test_expiry = time.time() + 3600  # 1 hour from now
captcha_cache[test_id] = (test_code, test_expiry, False)
logger.info(
    f"Initialized CAPTCHA cache with test entry. Cache size: {len(captcha_cache)}"
)

# CAPTCHA expiration time in seconds (5 minutes)
CAPTCHA_EXPIRY = 300


def generate_captcha_code(length: int = 4) -> str:
    """Generate a random CAPTCHA code."""
    # Use only uppercase letters and numbers that are less ambiguous
    characters = string.ascii_uppercase + string.digits
    # Remove ambiguous characters
    characters = (
        characters.replace("0", "").replace("O", "").replace("1", "").replace("I", "")
    )

    return "".join(random.choice(characters) for _ in range(length))


def generate_captcha_image(
    code: str, width: int = 120, height: int = 40
) -> Image.Image:
    """Generate a CAPTCHA image with the given code."""
    # Create a blank image
    image = Image.new("RGB", (width, height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    try:
        # Try to use a font if available, otherwise use default
        font = ImageFont.truetype("Arial", 24)
    except IOError:
        # Use default font if Arial is not available
        font = ImageFont.load_default()

    # Draw text
    # Use textbbox instead of textsize (which is deprecated in newer Pillow versions)
    try:
        # For newer Pillow versions
        bbox = draw.textbbox((0, 0), code, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
    except AttributeError:
        # Fallback for older Pillow versions
        try:
            text_width, text_height = draw.textsize(code, font=font)
        except AttributeError:
            # If both methods fail, use a reasonable default
            text_width, text_height = len(code) * 15, 20

    x = (width - text_width) // 2
    y = (height - text_height) // 2
    draw.text((x, y), code, font=font, fill=(0, 0, 0))

    # Add noise (random dots)
    for _ in range(width * height // 20):
        x = random.randint(0, width - 1)
        y = random.randint(0, height - 1)
        draw.point(
            (x, y),
            fill=(
                random.randint(0, 200),
                random.randint(0, 200),
                random.randint(0, 200),
            ),
        )

    # Add random lines
    for _ in range(5):
        x1 = random.randint(0, width - 1)
        y1 = random.randint(0, height - 1)
        x2 = random.randint(0, width - 1)
        y2 = random.randint(0, height - 1)
        draw.line(
            [(x1, y1), (x2, y2)],
            fill=(
                random.randint(0, 200),
                random.randint(0, 200),
                random.randint(0, 200),
            ),
        )

    return image


def generate_captcha() -> Tuple[str, str, str]:
    """
    Generate a CAPTCHA image and code.

    Returns:
        Tuple of (captcha_id, captcha_code, base64_image)
    """
    # Generate a random CAPTCHA code
    code = generate_captcha_code()

    # Generate a unique ID for this CAPTCHA
    captcha_id = "".join(random.choices(string.ascii_letters + string.digits, k=16))

    # Store the code in cache with expiration time and verification status (False = not verified)
    expiration_time = time.time() + CAPTCHA_EXPIRY
    captcha_cache[captcha_id] = (code, expiration_time, False)

    # Log cache state for debugging
    logger.debug(f"CAPTCHA cache after adding new entry: {captcha_cache}")
    logger.debug(f"Current cache size: {len(captcha_cache)}")

    # Generate the CAPTCHA image
    image = generate_captcha_image(code)

    # Convert the image to base64 for sending to the client
    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()

    return captcha_id, code, f"data:image/png;base64,{img_str}"


def verify_captcha(
    captcha_id: str, user_input: str, remove_if_valid: bool = False
) -> bool:
    """
    Verify the CAPTCHA code provided by the user.

    Args:
        captcha_id: The ID of the CAPTCHA to verify
        user_input: The user's input to check against the stored code
        remove_if_valid: Whether to remove the CAPTCHA from cache if valid (default: False)

    Returns:
        True if the code is valid, False otherwise
    """
    logger.debug(f"Verifying CAPTCHA with ID: {captcha_id}, user input: {user_input}")
    logger.debug(f"Current CAPTCHA cache: {captcha_cache}")
    logger.debug(f"Current cache size: {len(captcha_cache)}")

    if captcha_id not in captcha_cache:
        logger.warning(f"No CAPTCHA found for ID: {captcha_id}")
        logger.warning(f"Available CAPTCHA IDs: {list(captcha_cache.keys())}")
        return False

    stored_code, expiration_time, is_verified = captcha_cache[captcha_id]
    logger.debug(
        f"Found stored code: {stored_code}, expiration: {expiration_time}, verified: {is_verified}"
    )

    # If already verified and we're not removing it, just return True
    if is_verified and not remove_if_valid:
        logger.info(f"CAPTCHA for ID {captcha_id} was already verified")
        return True

    # Check if the CAPTCHA has expired
    current_time = time.time()
    if current_time > expiration_time:
        logger.warning(
            f"CAPTCHA for ID {captcha_id} has expired. Current time: {current_time}, Expiration: {expiration_time}"
        )
        del captcha_cache[captcha_id]
        return False

    # Check if the code matches (case-insensitive)
    if stored_code.upper() != user_input.upper():
        logger.warning(
            f"Invalid CAPTCHA code for ID {captcha_id}. Expected: {stored_code.upper()}, Got: {user_input.upper()}"
        )
        return False

    # Code is valid
    logger.info(f"CAPTCHA for ID {captcha_id} validated successfully")

    if remove_if_valid:
        # Remove from cache if requested
        del captcha_cache[captcha_id]
        logger.debug(f"Removed CAPTCHA {captcha_id} from cache")
    else:
        # Mark as verified but keep in cache
        captcha_cache[captcha_id] = (stored_code, expiration_time, True)
        logger.debug(f"Marked CAPTCHA {captcha_id} as verified")

    return True
