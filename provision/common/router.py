from blacksheep.server.routing import Router
from loguru import logger

router = Router()


def register_routes():
    try:
        # pylint: disable=unused-import
        from provision.api import (
            academy,
            admin_auth,
            admin_blog,
            admin_message,
            admin_registry,
            admin_resource,
            admin_resource_whitelist,
            admin_token_auth,
            auth,
            course,
            customer,
            device_info,
            lottery,
            message,
            price_tiers,
            register,
            websocket,
        )

        router.add_ws("/api/admin/ws", websocket.handle_message)
    except Exception as e:
        logger.error(f"Error registering routes: {e}")
        import traceback
        logger.error(traceback.format_exc())


__all__ = ["router"]
