"""
Aliyun SMS service integration for customer registration verification.
"""

import random
import time
from typing import Dict, <PERSON><PERSON>

from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
from alibabacloud_dysmsapi20170525.models import SendSmsRequest
from alibabacloud_tea_openapi.models import Config
from loguru import logger

from ..conf import cfg

# Cache to store verification codes with expiration time and verification status
# Format: {phone_number: (verification_code, expiration_timestamp, is_verified)}
verification_cache: Dict[str, Tuple[str, float, bool]] = {}

# Verification code expiration time in seconds (5 minutes)
VERIFICATION_CODE_EXPIRY = 300


def create_client() -> DysmsapiClient:
    """Create Aliyun SMS client."""
    try:
        config = cfg.provision.sms

        client_config = Config(
            access_key_id=config.access_key_id,
            access_key_secret=config.access_key_secret,
            endpoint=config.endpoint,
        )

        return DysmsapiClient(client_config)
    except Exception as e:
        logger.error(f"Failed to create SMS client: {e}")
        raise


def generate_verification_code() -> str:
    """Generate a 6-digit verification code."""
    return str(random.randint(100000, 999999))


def send_verification_code(phone_number: str) -> Tuple[bool, str]:
    """
    Send verification code to the specified phone number.

    Args:
        phone_number: The phone number to send the verification code to

    Returns:
        Tuple of (success, message)
    """
    try:
        # Generate a verification code
        code = generate_verification_code()

        expiration_time = time.time() + VERIFICATION_CODE_EXPIRY
        verification_cache[phone_number] = (code, expiration_time, False)

        # Get SMS configuration
        config = cfg.provision.sms

        # Create client
        client = create_client()

        # Create request
        request = SendSmsRequest(
            phone_numbers=phone_number,
            sign_name=config.sign_name,
            template_code=config.template_code,
            template_param=f'{{"code":"{code}"}}',
        )

        # Send SMS
        response = client.send_sms(request)

        if response.body.code == "OK":
            logger.info(f"Verification code sent to {phone_number}")
            return True, "Verification code sent successfully"
        else:
            logger.error(f"Failed to send verification code: {response.body.message}")
            # Remove the code from cache if sending failed
            if phone_number in verification_cache:
                del verification_cache[phone_number]
            return False, f"Failed to send verification code: {response.body.message}"

    except Exception as e:
        logger.error(f"Error sending verification code: {e}")
        # Remove the code from cache if sending failed
        if phone_number in verification_cache:
            del verification_cache[phone_number]
        return False, f"Error sending verification code: {str(e)}"


def verify_code(phone_number: str, code: str, remove_if_valid: bool = False) -> bool:
    """
    Verify the provided code against the stored verification code.

    Args:
        phone_number: The phone number to verify
        code: The verification code to check
        remove_if_valid: Whether to remove the code from cache if valid (default: False)

    Returns:
        True if the code is valid, False otherwise
    """
    logger.debug(
        f"Verifying code for {phone_number}, remove_if_valid: {remove_if_valid}"
    )
    logger.debug(f"Current verification cache: {verification_cache}")

    if phone_number not in verification_cache:
        logger.warning(f"No verification code found for {phone_number}")
        return False

    stored_code, expiration_time, is_verified = verification_cache[phone_number]
    logger.debug(
        f"Found stored code: {stored_code}, expiration: {expiration_time}, verified: {is_verified}"
    )

    # If already verified and we're not removing it, just return True
    if is_verified and not remove_if_valid:
        logger.info(f"Verification code for {phone_number} was already verified")
        return True

    # Check if the code has expired
    current_time = time.time()
    if current_time > expiration_time:
        logger.warning(
            f"Verification code for {phone_number} has expired. Current time: {current_time}, Expiration: {expiration_time}"
        )
        del verification_cache[phone_number]
        return False

    # Check if the code matches
    if stored_code != code:
        logger.warning(
            f"Invalid verification code for {phone_number}. Expected: {stored_code}, Got: {code}"
        )
        return False

    # Code is valid
    logger.info(f"Verification code for {phone_number} validated successfully")

    if remove_if_valid:
        # Remove from cache if requested
        del verification_cache[phone_number]
        logger.debug(f"Removed verification code for {phone_number} from cache")
    else:
        # Mark as verified but keep in cache
        verification_cache[phone_number] = (stored_code, expiration_time, True)
        logger.debug(f"Marked verification code for {phone_number} as verified")

    return True
