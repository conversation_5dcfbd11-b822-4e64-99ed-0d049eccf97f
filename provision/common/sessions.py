"""Simple session management"""

import datetime
import json
from pathlib import Path

import jwt
from blacksheep import Request
from loguru import logger

from ..conf import cfg


class SessionManager:
    def __init__(self):
        self.sessions = {}
        self._path = Path("~/.local/run/provision/provision.session")

    def load_sessions(self):
        """启动时，加载所有的session"""
        try:
            with open(self._path, "r", encoding="utf-8") as f:
                self.sessions = json.load(f)
        except FileNotFoundError:
            pass
        except Exception as e:
            logger.error(f"加载session失败: {e}")

    def save_sessions(self):
        """保存所有的session"""
        self._path.parent.mkdir(parents=True, exist_ok=True)
        with open(self._path, "w", encoding="utf-8") as f:
            json.dump(self.sessions, f)

    def is_active(self, customer_id: str):
        """是否为有效的session?"""
        # 添加日志记录
        logger.info(f"Checking session for customer {customer_id}")
        logger.debug(f"Current sessions: {self.sessions}")

        if customer_id not in self.sessions:
            logger.warning(f"Customer {customer_id} not in sessions")
            return False

        expire = self.sessions.get(customer_id)
        if expire is None:
            logger.warning(f"Expire time for customer {customer_id} is None")
            del self.sessions[customer_id]
            return False

        # 将字符串转换为 datetime 对象
        if isinstance(expire, str):
            try:
                expire = datetime.datetime.fromisoformat(expire)
            except ValueError:
                logger.error(f"Invalid expire time format: {expire}")
                del self.sessions[customer_id]
                return False

        now = datetime.datetime.now()
        result = expire > now
        logger.debug(f"Session active for customer {customer_id}: {result}")
        return result

    def add(self, customer_id, expire_days=30):
        """添加用户的session"""
        expire = datetime.datetime.now() + datetime.timedelta(days=expire_days)
        self.sessions[customer_id] = expire.isoformat()
        self.save_sessions()
        logger.info(f"Added session for customer {customer_id}, expires at {expire}")
        return True

    def remove(self, customer_id):
        """移除用户的session"""
        if customer_id in self.sessions:
            del self.sessions[customer_id]
            self.save_sessions()
            logger.info(f"Removed session for customer {customer_id}")
            return True
        return False

    def get_customer_id_from_token(self, request: Request):
        """从token中获取customer_id，不检查session状态"""
        token = request.cookies.get("qsession-token")
        if token:
            try:
                data = jwt.decode(
                    token, cfg.provision.server.secret, algorithms=["HS256"]
                )
                return data.get("account_id")
            except jwt.InvalidTokenError as e:
                logger.error("Invalid token: {} on request {}", e, request.url)
        return None

    def get_customer_id(self, request: Request):
        """获取用户的customer_id（需要验证session有效性）"""
        customer_id = self.get_customer_id_from_token(request)
        if customer_id and customer_id in self.sessions:
            return customer_id
        elif customer_id:
            logger.warning(
                "possible malicious request, forge customer_id in token: {}",
                request.url,
            )
        return None


sessions = SessionManager()

__all__ = ["sessions"]
