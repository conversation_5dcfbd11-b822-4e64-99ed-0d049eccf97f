"""
抽奖 API
"""

from blacksheep import Request
from blacksheep.server.responses import json
from loguru import logger

from provision.api.academy import AuthenticatedRequest
from provision.api.admin_auth import admin_required
from provision.api.auth import login_required
from provision.common.router import router
from provision.service.lottery import lottery_service


@router.get("/api/academy/lottery/config")
@login_required
async def get_lottery_config(request: Request):
    """获取抽奖配置"""
    try:
        customer_id = request.customer_id  # type: ignore

        # 获取所有抽奖配置
        configs = lottery_service.get_all_lottery_configs()

        # 过滤掉已有有效奖券的课程
        filtered_configs = []
        for config in configs:
            course = config["course"]

            # 检查用户是否已有该课程的有效奖券
            has_valid_ticket = lottery_service.has_valid_ticket(customer_id, course)

            # 如果没有有效奖券，则添加到配置列表中
            if not has_valid_ticket:
                # 获取用户剩余抽奖次数
                config["remaining_times"] = lottery_service.get_remaining_times(
                    customer_id, course
                )
                filtered_configs.append(config)

        return json({"success": True, "configs": filtered_configs})
    except Exception as e:
        logger.error(f"Error getting lottery config: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/academy/lottery/times/{course}")
@login_required
async def get_lottery_times(request: AuthenticatedRequest, course: str):
    """获取抽奖次数"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        remaining_times = lottery_service.get_remaining_times(customer_id, course)
        return json({"success": True, "remaining_times": remaining_times})
    except Exception as e:
        logger.error(f"Error getting lottery times: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/academy/lottery/draw")
@login_required
async def draw_lottery(request: AuthenticatedRequest):
    """抽奖"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        data = await request.json()
        course = data.get("course")
        if not course:
            return json({"error": "Course is required"}, status=400)

        success, result = lottery_service.draw(customer_id, course)

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error drawing lottery: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/academy/lottery/tickets")
@login_required
async def get_tickets(request: AuthenticatedRequest):
    """获取奖券列表"""
    try:
        customer_id = request.customer_id

        status = request.query.get("status")
        if isinstance(status, list):
            status = status[0]

        # 获取用户的奖券
        tickets = lottery_service.get_tickets(customer_id, status)

        # 获取转让给用户的奖券
        transferred_tickets = lottery_service.get_transferred_tickets(customer_id)

        # 获取用户的转让记录
        transfers = lottery_service.get_transfers(customer_id)

        return json(
            {
                "success": True,
                "tickets": tickets,
                "transferred_tickets": transferred_tickets,
                "transfers": transfers,
            }
        )
    except Exception as e:
        logger.error(f"Error getting tickets: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/academy/lottery/claim")
@login_required
async def claim_ticket(request: AuthenticatedRequest):
    """通过券码领取转让的奖券"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        data = await request.json()
        ticket_code = data.get("ticket_code")
        if not ticket_code:
            return json({"error": "Ticket code is required"}, status=400)

        success, result = lottery_service.claim_transfer_by_code(
            ticket_code, customer_id
        )

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error claiming ticket: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/academy/lottery/tickets/{ticket_id}/create-transfer")
@login_required
async def create_ticket_transfer(request: AuthenticatedRequest, ticket_id: int):
    """创建奖券转让记录"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        # 调用服务创建转让记录
        success, result = lottery_service.create_transfer(ticket_id, customer_id)

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error creating ticket transfer: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/academy/lottery/tickets/{ticket_id}/use")
@login_required
async def use_ticket(request: AuthenticatedRequest, ticket_id: int):
    """使用奖券"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        data = await request.json()
        order_id = data.get("order_id")
        if not order_id:
            return json({"error": "Order ID is required"}, status=400)

        success, result = lottery_service.use_ticket(ticket_id, customer_id, order_id)

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error using ticket: {e}")
        return json({"error": str(e)}, status=500)


# Admin APIs for ticket verification
@router.get("/api/admin/lottery/ticket/{ticket_code}")
@admin_required
async def get_ticket_by_code(_: Request, ticket_code: str):
    """根据券码查询奖券信息（管理员功能）"""
    try:
        success, result = lottery_service.get_ticket_by_code(ticket_code)

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error getting ticket by code: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/admin/lottery/ticket/{ticket_code}/verify")
@admin_required
async def verify_ticket(_: Request, ticket_code: str):
    """核销奖券（管理员功能）"""
    try:
        success, result = lottery_service.verify_ticket(ticket_code)

        if not success:
            return json(result, status=400)

        return json(result)
    except Exception as e:
        logger.error(f"Error verifying ticket: {e}")
        return json({"error": str(e)}, status=500)
