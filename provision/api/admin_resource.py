"""Admin API for managing resources and resource access."""

from typing import Dict, List, Optional

from blacksheep import Request, json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.api.admin_token_auth import api_token_required
from provision.common.helper import update_notebook_metadata
from provision.common.router import router
from provision.conf import cfg
from provision.service.resources import rm
from provision.store.customer import CustomerModel
from provision.store.resource import ResourceModel
from provision.store.whitelist_for_all import WhitelistForAllModel


@router.get("/api/admin/resources")
@admin_required
async def get_resources(
    _: Request,
    page: int = 1,
    page_size: int = 20,
    course: Optional[str] = None,
    resource: Optional[str] = None,
    search_query: Optional[str] = None,
):
    """获取资源列表，支持分页、course和resource筛选和搜索"""
    try:
        # 获取资源列表
        resources = ResourceModel.find_all(
            course=course,
            resource=resource,
            search_query=search_query,
            page=page,
            page_size=page_size,
        )

        # 获取资源总数
        total = ResourceModel.count(
            course=course, resource=resource, search_query=search_query
        )

        # 获取所有course和resource
        courses = ResourceModel.get_course()
        resource_folders = ResourceModel.get_resource_folders(course)

        # 构建响应
        return json(
            {
                "resources": [resource.model_dump() for resource in resources],
                "total": total,
                "courses": courses,
                "resource_folders": resource_folders,
            }
        )
    except Exception as e:
        logger.error(f"Error getting resources: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/admin/resources/{resource_id}")
@admin_required
async def get_resource(_: Request, resource_id: int):
    """获取单个资源的详细信息"""
    try:
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        return json(resource.model_dump())
    except Exception as e:
        logger.error(f"Error getting resource {resource_id}: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/api/admin/resources")
@admin_required
async def create_resource(request: Request):
    """创建新资源"""
    try:
        data = await request.json()
        resource = ResourceModel(**data)
        resource_id = resource.save()

        return json({"id": resource_id, "message": "Resource created successfully"})
    except Exception as e:
        logger.error(f"Error creating resource: {e}")
        return json({"error": str(e)}, status=500)


@router.put("/api/admin/resources/{resource_id}")
@admin_required
async def update_resource(request: Request, resource_id: int):
    """更新资源信息"""
    try:
        data = await request.json()
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        # 更新资源信息
        for key, value in data.items():
            setattr(resource, key, value)

        resource.save()

        # 同步更新 notebook 的 metadata
        try:
            # 获取 notebook 路径
            course_home = cfg.get_course_home(resource.course)
            if course_home:
                notebook_path = course_home / resource.rel_path
                if notebook_path.exists():
                    # 更新 notebook 的 metadata
                    update_notebook_metadata(
                        notebook_path=notebook_path,
                        title=resource.title,
                        description=resource.description,
                        price=resource.price,
                        publish_date=resource.publish_date,
                        img=resource.img,
                    )
                    logger.info(f"Updated notebook metadata for {notebook_path}")
                else:
                    logger.warning(f"Notebook not found: {notebook_path}")
            else:
                logger.warning(f"Course home not found for {resource.course}")
        except Exception as e:
            # 不影响主流程，只记录日志
            logger.error(f"Error updating notebook metadata: {e}")

        return json({"message": "Resource updated successfully"})
    except Exception as e:
        logger.error(f"Error updating resource {resource_id}: {e}")
        return json({"error": str(e)}, status=500)


@router.delete("/api/admin/resources/{resource_id}")
@admin_required
async def delete_resource(_: Request, resource_id: int):
    """删除资源"""
    try:
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        ResourceModel.delete(resource_id)

        return json({"message": "Resource deleted successfully"})
    except Exception as e:
        logger.error(f"Error deleting resource {resource_id}: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/admin/resources/publish")
@api_token_required
async def publish_resource(request: Request):
    """
    发布博客文章资源

    这个接口通过 API token 认证，用于发布博客文章到资源表。
    当 allow_all=True 时，会将资源添加到公开白名单并重建缓存。

    参数:
    - meta: JSON 格式，包含 resources 表格中的字段
    - allow_all: 布尔值，是否允许所有人访问（默认 False）
    """
    try:
        data = await request.json()

        # 获取 meta 信息和 allow_all 参数
        meta = data.get("meta")
        allow_all = data.get("allow_all", False)

        if not meta:
            return json({"error": "Missing required field: meta"}, status=400)

        # 验证必需字段（seq 字段会自动生成，不需要客户端提供）
        required_fields = ["course", "resource", "title", "rel_path", "division"]
        for field in required_fields:
            if field not in meta:
                return json({"error": f"Missing required field in meta: {field}"}, status=400)

        # 使用 store 层的事务安全方法创建资源
        resource_id = ResourceModel.create_with_auto_seq(meta)

        logger.info(f"Resource published successfully with ID: {resource_id}")

        # 如果允许所有人访问，添加到公开白名单
        if allow_all:
            logger.info(f"Adding resource {resource_id} to public whitelist")

            # 添加到公开白名单
            success = WhitelistForAllModel.add_resource(resource_id)

            if success:
                logger.info("Rebuilding resource access cache for all customers")
                rm.build_cache()

                return json({
                    "id": resource_id,
                    "message": "Resource published successfully and made public",
                    "public": True
                })
            else:
                logger.error(f"Failed to add resource {resource_id} to public whitelist")
                return json({
                    "id": resource_id,
                    "message": "Resource published but failed to make public",
                    "public": False
                }, status=500)

        return json({
            "id": resource_id,
            "message": "Resource published successfully",
            "public": False
        })

    except Exception as e:
        logger.error(f"Error publishing resource: {e}")
        return json({"error": str(e)}, status=500)
