from blacksheep import Request, json
from loguru import logger

from provision.api.auth import login_required
from provision.common.router import router
from provision.store.message import MessageModel


@router.get("/api/academy/messages")
@login_required
async def get_messages(request: Request, limit: int = 50, offset: int = 0):
    """获取用户的消息列表

    Args:
        request: 请求对象，包含用户ID
        limit: 返回消息的最大数量
        offset: 分页偏移量
    """
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        messages = MessageModel.find_messages_by_recipient(
            recipient_id=customer_id, include_deleted=False, limit=limit, offset=offset
        )

        unread_count = MessageModel.count_unread_messages(recipient_id=customer_id)

        return json(
            {"messages": messages, "unread_count": unread_count, "total": len(messages)}
        )
    except Exception as e:
        logger.error(f"Error getting messages: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/academy/messages/unread")
@login_required
async def get_unread_count(request: Request):
    """获取用户的未读消息数量

    Args:
        request: 请求对象，包含用户ID
    """
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        unread_count = MessageModel.count_unread_messages(recipient_id=customer_id)
        return json({"unread_count": unread_count})
    except Exception as e:
        logger.error(f"Error getting unread count: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.put("/api/academy/messages/{message_id}/read")
@login_required
async def mark_as_read(request: Request, message_id: int):
    """将消息标记为已读

    Args:
        request: 请求对象，包含用户ID
        message_id: 消息ID
    """
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        # 检查消息是否存在
        message = MessageModel.get_message(message_id)
        if not message:
            return json({"error": "Message not found"}, status=404)

        # 检查消息是否属于当前用户
        if message.get("recipient_id") != customer_id:
            return json({"error": "Unauthorized"}, status=401)

        # 标记为已读
        success = MessageModel.mark_as_read(message_id)
        if success:
            return json({"status": "success"})

        return json({"error": "Failed to mark message as read"}, status=500)
    except Exception as e:
        logger.error(f"Error marking message as read: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.delete("/api/academy/messages/{message_id}")
@login_required
async def delete_message(request: Request, message_id: int):
    """将消息标记为已删除（用户端）

    Args:
        request: 请求对象，包含用户ID
        message_id: 消息ID
    """
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        # 检查消息是否存在
        message = MessageModel.get_message(message_id)
        if not message:
            return json({"error": "Message not found"}, status=404)

        # 检查消息是否属于当前用户
        if message.get("recipient_id") != customer_id:
            return json({"error": "Unauthorized"}, status=401)

        # 标记为已删除
        success = MessageModel.mark_as_deleted(message_id)
        if success:
            return json({"status": "success"})

        return json({"error": "Failed to delete message"}, status=500)
    except Exception as e:
        logger.error(f"Error deleting message: {e}")
        return json({"error": "Internal server error"}, status=500)


# 管理端 API已移至 admin_message.py
