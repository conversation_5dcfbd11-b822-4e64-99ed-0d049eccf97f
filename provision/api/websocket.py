import asyncio
import j<PERSON>
from typing import Dict, Set

import arrow
from blacksheep import WebSocket, WebSocketDisconnectError
from loguru import logger

from provision.containers.env import get_message

# 存储活跃的WebSocket连接
active_connections: Dict[str, Set[WebSocket]] = {}


async def handle_message(websocket: WebSocket, event_id: str):
    await websocket.accept()

    if event_id not in active_connections:
        active_connections[event_id] = set()
    active_connections[event_id].add(websocket)

    try:
        while True:
            message = get_message(event_id)
            if message:
                await send_container_log(event_id, message)

            # 2. Handle client pings/pongs
            try:
                data = await asyncio.wait_for(websocket.receive(), timeout=0.1)
                if isinstance(data, bytes) and len(data) == 4:
                    await websocket.send_bytes(b"pong")
            except asyncio.TimeoutError:
                continue

    except WebSocketDisconnectError:
        logger.info(f"Connection closed for {event_id}")
    finally:
        if event_id in active_connections:
            active_connections[event_id].discard(websocket)
            if not active_connections[event_id]:
                del active_connections[event_id]


async def send_container_log(container_id: str, message: str):
    """
    向指定容器ID的所有WebSocket连接发送日志消息

    Args:
        container_id: 容器ID
        message: 日志消息
        log_type: 日志类型 (info, error, success)
    """
    if container_id not in active_connections:
        logger.warning(f"No active connections for container {container_id}")
        return

    log_data = {
        "message": message,
        "timestamp": arrow.now("Asia/Shanghai").format("HH:mm:ss"),
    }

    # 向所有连接到该容器的WebSocket发送消息
    disconnected = set()
    for ws in active_connections[container_id]:
        try:
            await ws.send_text(json.dumps(log_data))
        except Exception as e:
            logger.error(f"Error sending message to WebSocket: {e}")
            disconnected.add(ws)

    # 移除断开的连接
    for ws in disconnected:
        active_connections[container_id].discard(ws)

    if not active_connections[container_id]:
        del active_connections[container_id]
