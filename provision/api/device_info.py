"""
设备信息收集API
"""
from blacksheep import Request
from blacksheep.server.responses import json as json_response
from loguru import logger

from provision.common.router import router
from provision.common.sessions import sessions
from provision.store.visits import VisitModel
from provision.utils.ip_utils import get_real_ip


@router.post("/api/device-info")
async def receive_device_info(request: Request):
    """
    接收前端发送的设备信息
    """
    try:
        # 获取请求数据
        data = await request.json()
        if not data:
            return json_response({}, status=200)

        device_info = data.get("deviceInfo")
        url = data.get("url", "")

        if not device_info:
            return json_response({}, status=200)

        # 获取真实IP
        real_ip = get_real_ip(request)
        if real_ip is None:
            return json_response({}, status=200)

        # 获取用户代理
        user_agent_header = request.headers.get(b"User-Agent")
        if user_agent_header:
            user_agent = user_agent_header[0].decode('utf-8') if isinstance(user_agent_header[0], bytes) else str(user_agent_header[0])
        else:
            user_agent = "Unknown"

        # 尝试获取客户ID（如果用户已登录）
        customer_id = sessions.get_customer_id(request)

        # 创建访问记录
        visit = VisitModel.create_visit(
            ip_address=real_ip,
            customer_id=customer_id,
            user_agent=user_agent,
            device_info=device_info,  # 直接传递字典，VisitModel会处理JSON转换
            url_path=url
        )

        if visit:
            logger.info(f"设备信息记录成功: IP={real_ip}, Customer={customer_id}, URL={url}")
            return json_response({"success": True})
        else:
            logger.error("设备信息记录失败")
            return json_response({}, status=200)

    except Exception as e:
        logger.error(f"处理设备信息时出错: {e}")
        return json_response({}, status=200)



