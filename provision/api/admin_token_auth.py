"""API Token 认证模块，用于 API 接口的认证"""

from functools import wraps
from typing import Callable

from blacksheep import Request
from blacksheep.server.responses import json
from loguru import logger

from provision.conf import cfg


def api_token_required(handler: Callable) -> Callable:
    """
    装饰器，要求 API token 认证才能访问路由。

    如果请求中没有有效的 API token，将返回 401 Unauthorized。

    API token 可以通过以下方式提供：
    1. Authorization 头: Bearer <token>
    2. X-API-Token 头
    3. api_token 查询参数
    """

    @wraps(handler)
    async def wrapper(request: Request, *args, **kwargs):
        # 从配置文件中获取预设的 API token
        expected_token = cfg.provision.api_token

        if not expected_token:
            logger.error("API token not configured in provision.yaml")
            return json({"error": "API token not configured"}, status=500)

        # 从请求中获取 API token
        token = None

        # 1. 检查 Authorization 头
        auth_header = request.headers.get(b"Authorization")
        if auth_header and len(auth_header) > 0:
            auth_value = auth_header[0].decode("utf-8")
            if auth_value.startswith("Bearer "):
                token = auth_value[7:]

        # 2. 检查 X-API-Token 头
        if not token:
            api_token_header = request.headers.get(b"X-API-Token")
            if api_token_header and len(api_token_header) > 0:
                token = api_token_header[0].decode("utf-8")

        # 3. 检查查询参数
        if not token:
            token = request.query.get("api_token")

        if not token:
            logger.warning(f"API token not provided for {request.url}")
            return json({"error": "API token required"}, status=401)

        # 验证 API token
        if token != expected_token:
            logger.warning(f"Invalid API token provided for {request.url}")
            return json({"error": "Invalid API token"}, status=401)

        # 调用原始处理函数
        return await handler(request, *args, **kwargs)

    return wrapper
