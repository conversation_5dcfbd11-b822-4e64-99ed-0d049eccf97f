"""博客文章发布 API"""

from pathlib import Path

from blacksheep import Request
from blacksheep.server.responses import json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.api.admin_token_auth import api_token_required
from provision.common.router import router
from provision.conf import cfg
from provision.service.resources import rm


@router.post("/api/admin/blog/publish")
@api_token_required
async def publish_blog_article(request: Request):
    """
    发布博客文章

    请求参数:
    - notebook_path: notebook 文件路径，相对于 blog 课程根目录
    - allow_all: 是否允许所有人访问，默认为 False
    """
    try:
        data = await request.json()
        notebook_path = data.get("notebook_path")
        allow_all = data.get("allow_all", False)

        if not notebook_path:
            return json({"error": "Notebook path is required"}, status=400)

        # 检查文件是否存在
        course = "blog"
        home = cfg.get_course_home(course)
        if home is None:
            return json(
                {"error": f"Home path not found for course {course}"}, status=500
            )

        full_path = home / notebook_path
        if not full_path.exists():
            return json({"error": f"Notebook not found: {notebook_path}"}, status=404)

        # 确定资源类型
        resource_type = "articles"
        parent_dir = Path(notebook_path).parent
        if parent_dir and parent_dir.name:
            resource_type = str(parent_dir)

        # 添加资源
        resource_id = rm.add_resource(
            notebook_path=notebook_path,
            course=course,
            resource_type=resource_type,
            allow_all=allow_all,
        )

        if resource_id is None:
            return json({"error": "Failed to add resource"}, status=500)

        return json(
            {
                "success": True,
                "message": "Blog article published successfully",
                "resource_id": resource_id,
                "public": allow_all,
            }
        )

    except Exception as e:
        logger.error(f"Error publishing blog article: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/admin/blog/make-public")
@admin_required
async def make_blog_public(request: Request):
    """
    将博客文章设为公开

    请求参数:
    - resource_id: 资源ID
    """
    try:
        data = await request.json()
        resource_id = data.get("resource_id")

        if not resource_id:
            return json({"error": "Resource ID is required"}, status=400)

        # 添加到公开资源列表
        success = rm.make_resource_public(resource_id)

        if not success:
            return json({"error": "Failed to make resource public"}, status=500)

        return json({"success": True, "message": "Blog article is now public"})

    except Exception as e:
        logger.error(f"Error making blog article public: {e}")
        return json({"error": str(e)}, status=500)
