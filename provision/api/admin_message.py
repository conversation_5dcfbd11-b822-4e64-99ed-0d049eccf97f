from blacksheep import Request, json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.common.router import router
from provision.store.customer import CustomerModel
from provision.store.message import MessageModel


@router.get("/api/admin/messages")
@admin_required
async def get_messages(request: Request, page: int = 1, pageSize: int = 20):
    """获取所有消息列表（管理端）

    Args:
        page: 页码
        pageSize: 每页数量
    """
    try:
        # 获取消息列表
        # 管理端需要获取所有消息，这里我们简化处理，只获取前100条消息
        # 实际应用中应该添加一个特殊的方法来获取所有消息
        messages = []
        # 获取所有客户
        customers = CustomerModel.find_all_customers()
        for customer in customers:
            # 获取每个客户的消息
            if customer.id is None:
                continue
            customer_messages = MessageModel.find_messages_by_recipient(
                recipient_id=customer.id,
                include_deleted=True,
                limit=100,
                offset=0,
            )
            messages.extend(customer_messages)

        # 获取消息总数
        total = len(messages)  # 这里简化处理，实际应该从数据库获取总数

        # 对消息进行排序（按创建时间降序）
        messages.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        # 分页处理
        start_index = (page - 1) * pageSize
        end_index = min(start_index + pageSize, len(messages))
        paginated_messages = messages[start_index:end_index] if messages else []

        return json({"messages": paginated_messages, "total": total})
    except Exception as e:
        logger.error(f"Error getting admin messages: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/api/admin/messages")
@admin_required
async def create_message(request: Request):
    """创建新消息（管理端）"""
    try:
        data = await request.json()

        # 检查标题和内容是否存在
        title = data.get("title")
        content = data.get("content")

        if not title or not title.strip():
            return json({"error": "Title is required"}, status=400)

        if not content or not content.strip():
            return json({"error": "Content is required"}, status=400)

        # 获取所有客户
        customers = CustomerModel.find_all_customers()
        if not customers:
            return json({"error": "No customers found"}, status=404)

        # 向每个客户发送消息
        message_ids = []
        for customer in customers:
            # 创建消息模型
            if customer.id is None:
                continue
            message = MessageModel(
                title=title,
                content=content,
                sender_id=data.get("sender_id", 1),
                recipient_id=customer.id,
            )

            # 保存消息
            message_id = message.save()
            message_ids.append(message_id)

        return json(
            {"ids": message_ids, "count": len(message_ids), "status": "success"}
        )
    except Exception as e:
        logger.error(f"Error creating admin message: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.put("/api/admin/messages/{message_id}")
@admin_required
async def update_message(request: Request, message_id: int):
    """更新消息内容（管理端）"""
    try:
        # 检查消息是否存在
        message = MessageModel.get_message(message_id)
        if not message:
            return json({"error": "Message not found"}, status=404)

        # 获取更新数据
        data = await request.json()
        title = data.get("title")
        content = data.get("content")

        # 更新消息
        success = MessageModel.update_message(message_id, title, content)
        if success:
            return json({"status": "success"})
        else:
            return json({"error": "Failed to update message"}, status=500)
    except Exception as e:
        logger.error(f"Error updating admin message: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.delete("/api/admin/messages/{message_id}")
@admin_required
async def delete_message(request: Request, message_id: int):
    """从数据库中完全删除消息（管理端）"""
    try:
        # 检查消息是否存在
        message = MessageModel.get_message(message_id)
        if not message:
            return json({"error": "Message not found"}, status=404)

        # 删除消息
        success = MessageModel.delete_message(message_id)
        if success:
            return json({"status": "success"})
        else:
            return json({"error": "Failed to delete message"}, status=500)
    except Exception as e:
        logger.error(f"Error deleting admin message: {e}")
        return json({"error": "Internal server error"}, status=500)
