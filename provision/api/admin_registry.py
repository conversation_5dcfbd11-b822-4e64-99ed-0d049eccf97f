"""Admin API for managing course registrations."""

from typing import Optional

from blacksheep import Request, json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.common.router import router
from provision.conf import cfg
from provision.store.registry import RegistryModel


@router.get("/api/admin/registrations")
@admin_required
async def get_all_registrations(
    request: Request,
    page: int = 1,
    page_size: int = 20,
    sort_by: Optional[str] = None,
    sort_order: str = "asc",
    course: Optional[str] = None,
    plan: Optional[str] = None,
    include_inactive: bool = True,
    is_active: Optional[bool] = None,
):
    """获取所有课程注册记录，支持分页和排序

    Args:
        page: 页码（从1开始）
        page_size: 每页记录数
        sort_by: 排序字段
        sort_order: 排序方向（"asc"或"desc"）
        course: 课程筛选
        plan: 套餐筛选
        include_inactive: 是否包含非活跃的课程（默认包含）
    """
    try:
        logger.info(
            f"Getting all registrations, page={page}, page_size={page_size}, "
            f"sort_by={sort_by}, sort_order={sort_order}, course={course}, plan={plan}, "
            f"include_inactive={include_inactive}, is_active={is_active}"
        )

        # 获取课程注册记录
        registrations, total_count = RegistryModel.find_all_registrations(
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            course_filter=course,
            plan_filter=plan,
            include_inactive=include_inactive,
            is_active=is_active,
        )

        logger.info(f"Found {len(registrations)} registrations (total: {total_count})")

        # 返回分页数据
        return json(
            {
                "registrations": registrations,
                "total": total_count,
                "page": page,
                "page_size": page_size,
            }
        )

    except Exception as e:
        logger.error(f"Error retrieving registrations: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.put("/api/admin/registrations/{registration_id}")
@admin_required
async def update_registration(request: Request, registration_id: int):
    """更新课程注册记录"""
    try:
        # 获取请求数据
        data = await request.json()

        # 获取当前注册记录
        registration = RegistryModel.get_registration(registration_id)
        if not registration:
            return json({"error": "Registration not found"}, status=404)

        # 不允许修改 course_id, id, division
        if "course" in data:
            del data["course"]
        if "id" in data:
            del data["id"]
        if "division" in data:
            del data["division"]

        # 更新字段
        for key, value in data.items():
            if hasattr(registration, key):
                setattr(registration, key, value)

        # 保存更新
        registration.update(registration_id)

        return json(
            {"status": "success", "message": "Registration updated successfully"}
        )

    except Exception as e:
        logger.error(f"Error updating registration: {e}")
        return json({"error": "Internal server error"}, status=500)
