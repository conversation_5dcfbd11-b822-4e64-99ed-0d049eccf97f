"""Admin authentication module for the admin interface."""

import hashlib
import time
from functools import wraps
from typing import Callable, List, Optional

import jwt
from blacksheep import Request, Response, redirect
from blacksheep.server.responses import json
from loguru import logger

from provision.common.router import router
from provision.common.sessions import sessions

from ..conf import cfg

# Constants
ADMIN_SESSION_PREFIX = "admin_"
ADMIN_COOKIE_NAME = "admin-token"
ADMIN_SESSION_EXPIRY = 60 * 30  # 30 minutse


def verify_admin_credentials(username: str, password: str) -> bool:
    """Verify admin credentials against configuration."""
    admin_users = cfg.provision.admin.users
    for user in admin_users:
        if user.username == username and user.password == password:
            return True

    return False


def admin_required(handler: Callable) -> Callable:
    """Decorator to require admin authentication for routes."""

    @wraps(handler)
    async def wrapper(request: Request, *args, **kwargs):
        # Check for admin token in cookies
        token = request.cookies.get(ADMIN_COOKIE_NAME)

        if not token:
            # If it's an API request, return 401 Unauthorized
            if request.url.path.startswith(b"/api/admin"):
                return json({"error": "Unauthorized"}, status=401)
            # Otherwise redirect to admin login page
            return redirect("/admin/login")

        try:
            # Verify token
            data = jwt.decode(token, cfg.provision.server.secret, algorithms=["HS256"])
            admin_id = data.get("admin_id")

            if not admin_id:
                logger.error("No admin_id in token data")
                return json({"error": "Invalid token"}, status=401)

            # Check if session is active
            if not sessions.is_active(f"{ADMIN_SESSION_PREFIX}{admin_id}"):
                logger.warning(f"Admin session expired for {admin_id}")
                return json({"error": "Session expired"}, status=401)

            # Set admin username in request context for use in handler
            request.admin_username = data.get("username")  # type: ignore

            # Call the original handler
            return await handler(request, *args, **kwargs)

        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid admin token: {e}")

            # If it's an API request, return 401 Unauthorized
            if request.url.path.startswith(b"/api/admin"):
                return json({"error": "Invalid token"}, status=401)

            # Otherwise redirect to admin login page
            return redirect("/admin/login")

    return wrapper


@router.post("/api/admin/login")
async def admin_login(request: Request):
    """Handle admin login requests."""
    try:
        data = await request.json()
        username = data.get("username")
        password = data.get("password")

        if not username or not password:
            return json({"error": "Username and password are required"}, status=400)

        # Verify credentials
        if not verify_admin_credentials(username, password):
            logger.warning(f"Failed login attempt for admin user: {username}")
            return json({"error": "Invalid credentials"}, status=401)

        # Generate a unique admin ID (using timestamp for simplicity)
        admin_id = f"{username}_{int(time.time())}"

        # Add to sessions
        sessions.add(f"{ADMIN_SESSION_PREFIX}{admin_id}", expire_days=1)

        # Create JWT token
        token_data = {
            "username": username,
            "admin_id": admin_id,
            "exp": int(time.time()) + ADMIN_SESSION_EXPIRY,
        }
        token = jwt.encode(token_data, cfg.provision.server.secret, algorithm="HS256")

        # Create response
        response = json(
            {"success": True, "username": username, "redirectUrl": "/admin/"}
        )

        # Set cookie
        cookie_value = f"{ADMIN_COOKIE_NAME}={token}; Path=/; Max-Age={ADMIN_SESSION_EXPIRY}; HttpOnly"
        response.headers.add(b"Set-Cookie", cookie_value.encode("utf-8"))

        logger.info(f"Admin login successful: {username}")
        return response

    except Exception as e:
        logger.error(f"Error during admin login: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/api/admin/logout")
async def admin_logout(request: Request):
    """Handle admin logout requests."""
    # Get token from cookies
    token = request.cookies.get(ADMIN_COOKIE_NAME)

    if token:
        try:
            # Decode token
            data = jwt.decode(token, cfg.provision.server.secret, algorithms=["HS256"])
            admin_id = data.get("admin_id")

            # Remove from sessions
            if admin_id:
                sessions.remove(f"{ADMIN_SESSION_PREFIX}{admin_id}")
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token during admin logout: {e}")

    # Create response
    response = json({"success": True, "redirectUrl": "/admin/login"})

    # Clear cookie
    cookie_value = f"{ADMIN_COOKIE_NAME}=; Path=/; Max-Age=0; HttpOnly"
    response.headers.add(b"Set-Cookie", cookie_value.encode("utf-8"))

    logger.info("Admin logout successful")
    return response


@router.get("/api/admin/check-auth")
@admin_required
async def check_admin_auth(request: Request):
    """Check if admin is authenticated."""
    return json({"authenticated": True, "username": request.admin_username})  # type: ignore


# Apply admin_required decorator to all admin API endpoints
# This will be done in the router registration
