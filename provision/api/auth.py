"""
认证和授权模块，提供用户登录、注销和访问控制功能。

包含：
- 用户登录和注销 API
- 资源访问控制
- login_required 装饰器，用于保护需要登录才能访问的路由
"""

from functools import wraps
from pathlib import Path
from typing import Callable

import jwt
from blacksheep import Request, redirect
from blacksheep.server.responses import json
from loguru import logger

from provision.common.router import router
from provision.common.sessions import sessions
from provision.conf import cfg
from provision.service.resources import rm
from provision.store.customer import CustomerModel
from provision.store.visits import VisitModel
from provision.utils.ip_utils import get_real_ip

STATIC_DIR = Path(__file__).parent.parent / "static"


@router.route("/sso/customer/auth", ["GET", "POST"])
async def auth_course_request(request: Request):
    """
    Authentication endpoint for nginx auth_request module.
    This endpoint validates user access to course resources based on their session and permissions.
    """
    headers = request.headers
    burl = headers.get(b"X-Original-URI")

    if not burl or len(burl) == 0:
        logger.error("No original URL found")
        return ("", 200)

    url = burl[0].decode("utf-8") if isinstance(burl[0], bytes) else str(burl[0])
    logger.info(f"Auth request: {url}")

    # 获取真实客户端IP和用户代理
    real_ip = get_real_ip(request)
    user_agent_header = headers.get(b"User-Agent")
    if user_agent_header:
        user_agent = user_agent_header[0].decode('utf-8') if isinstance(user_agent_header[0], bytes) else str(user_agent_header[0])
    else:
        user_agent = "Unknown"

    # 尝试从token中获取客户ID（用于访问记录）
    customer_id = sessions.get_customer_id_from_token(request)

    # 检查是否有有效的session（用于授权）
    active_customer_id = sessions.get_customer_id(request)

    logger.info(f"Customer ID from token: {customer_id}, Active session: {active_customer_id is not None}")

    if real_ip is not None:
        VisitModel.create_visit(
            ip_address=real_ip,
            customer_id=customer_id,
            user_agent=user_agent,
            url_path=url
        )

    # 检查认证状态（使用active_customer_id进行授权检查）
    if not active_customer_id:
        return json({"status": "unauthorized"}, status=401)

    # 如果请求的 URL 是以 /academy 开头的，只要用户已登录，就允许访问
    if url.startswith("/academy"):
        logger.info(f"Access allowed for {url}: user is authenticated")
        return json({"status": "authorized"}, status=200)

    # 如果请求的 URL 是以 /course 开头的，需要通过 resource_manager.allow_access 检查
    if url.startswith("/course"):
        # 检查是否允许访问
        allowed = rm.allow_access(url, active_customer_id)
        logger.info(f"Access allowed for {url}: {allowed}")

        if allowed:
            return json({"status": "authorized"}, status=200)

        logger.warning(f"Access denied for {url} by customer {active_customer_id}")
        return json({"status": "unauthorized"}, status=401)

    # 其他路径，默认允许访问
    logger.info(f"Access allowed for {url}: default allow")
    return json({"status": "authorized"}, status=200)


@router.post("/sso/customer/login")
async def login(request: Request):
    """
    处理用户登录请求，验证凭证并设置会话。
    成功登录后返回重定向URL和用户ID。
    """
    data = await request.json() or {}
    password = data.get("password")
    account = data.get("account")
    if data is None or account is None or password is None:
        return json({"status": "both account and password are required"}, 400)

    customer = CustomerModel.find_customer_by_account(account)
    if customer is None or customer.password is None:
        logger.warning(f"Login attempt failed: account '{account}' not found")
        return json({"status": "account not found"}, 401)

    # Ensure salt is a string before encoding
    salt = customer.salt or ""
    hashed_password, _ = CustomerModel.hash_password(password, salt)

    if hashed_password != customer.password:
        logger.warning(
            f"Login attempt failed: incorrect password for account '{account}'"
        )
        return json({"status": "password error"}, 401)

    # 将用户添加到 sessions 中
    sessions.add(customer.id)
    logger.info(f"Added customer {customer.id} to sessions")

    # 创建JWT令牌
    token_data = {"account": account, "account_id": customer.id}
    token = jwt.encode(token_data, cfg.provision.server.secret, algorithm="HS256")

    # 创建JSON响应
    response = json({"redirectUrl": "/academy", "customer": customer.id})

    # 直接设置 Set-Cookie 头
    cookie_value = f"qsession-token={token}; Path=/; Max-Age={3600 * 24 * 30}; HttpOnly"
    response.headers.add(b"Set-Cookie", cookie_value.encode("utf-8"))

    # 返回重定向URL和用户ID
    logger.info(f"Login successful for customer {customer.id}")

    return response


def login_required(handler: Callable) -> Callable:
    """
    装饰器，要求用户登录才能访问路由。

    如果用户未登录，API 请求将返回 401 Unauthorized，
    非 API 请求将重定向到登录页面。
    """

    @wraps(handler)
    async def wrapper(request: Request, *args, **kwargs):
        # 获取用户 ID
        customer_id = sessions.get_customer_id(request)

        if not customer_id:
            # 如果是 API 请求，返回 401 Unauthorized
            if request.url.path.startswith(b"/api/"):
                return json({"error": "Unauthorized"}, status=401)
            # 否则重定向到登录页面
            return redirect("/academy/login")

        # 将 customer_id 添加到请求上下文中，方便处理函数使用
        request.customer_id = customer_id  # type: ignore

        # 调用原始处理函数
        return await handler(request, *args, **kwargs)

    return wrapper


@router.post("/sso/customer/logout")
async def logout(request: Request):
    """
    Logout endpoint that clears the session cookie.
    """
    # 清除会话信息
    token = request.cookies.get("qsession-token")
    if token:
        try:
            data = jwt.decode(token, cfg.provision.server.secret, algorithms=["HS256"])
            customer_id = data.get("account_id")
            if customer_id:
                sessions.remove(customer_id)
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token during logout: {e}")
        except KeyError as e:
            logger.error(f"Missing key in token data during logout: {e}")

    # 创建响应
    response = json({"redirectUrl": "/academy/login"})

    # 直接设置 Set-Cookie 头清除 cookie
    cookie_value = "qsession-token=; Path=/; Max-Age=0; HttpOnly"
    response.headers.add(b"Set-Cookie", cookie_value.encode("utf-8"))

    # 添加调试日志
    logger.info(f"Clearing cookie header: {cookie_value}")
    logger.info(f"Response headers: {response.headers}")

    logger.info("Logout successful, cookie cleared")
    return response
