import datetime
from datetime import timedelta

from blacksheep import Request, json
from loguru import logger
from pydantic import ValidationError

from provision.api.admin_auth import admin_required
from provision.common.router import router
from provision.conf import cfg
from provision.conf.plans import sp
from provision.containers.env import async_create_course_container, delete_container
from provision.service.resources import rm
from provision.store.customer import CustomerModel
from provision.store.registry import RegistryModel


@router.post("/api/admin/courses/register")
@admin_required
async def register_course(request: Request):
    """用户订阅课程"""
    try:
        data = await request.json()
        course, plan = data["course"], data["plan"]
        data["division"] = cfg.get_course_division(course)

        registration = RegistryModel(**data)
        course = registration.course
        plan = registration.plan
        customer_id = registration.customer_id

        customer = CustomerModel.find_customer(customer_id)
        if customer is None:
            # TODO: redirect to customer registration page
            return {"status": "error", "message": "Customer not found"}

        # check if already reserved
        reserved = RegistryModel.find_subscription_by_customer_course(
            customer_id, course, True
        )
        if reserved is not None:
            return json({"error": "Course already reserved"}, status=400)

        has_container = sp.has_own_container(course, plan)
        account = cfg.get_shared_account() if not has_container else customer.account

        # 创建容器ID用于WebSocket通信
        container_id = f"{course}_{account}"

        # 计算宽限期
        retention_period = sp.get_retention_period(course)
        registration.retention_end = registration.prime_end + timedelta(
            days=retention_period
        )

        # 添加课程注册到数据库
        result = RegistryModel.add(registration)
        result["container_id"] = container_id

        # 在后台线程中启动容器创建过程
        async_create_course_container(course, account, plan)

        rm.update_customer_cache(customer_id)

        return result
    except ValidationError as e:
        return json({"error": str(e)}, status=400)


@router.get("/api/admin/courses/available")
async def available_courses():
    """获取可用课程列表"""
    results = []
    for key, value in cfg.resources.model_dump().items():
        results.append({"name": key, "displayName": value["title"]})

    return results


@router.get("/api/admin/courses/plans/{course_id}")
@admin_required
async def get_course_plans(_: Request, course_id: str):
    """获取课程的所有级别"""
    try:
        plan_model = sp.plans.get(course_id)
        if plan_model is None:
            logger.warning("获取课程套餐时，传入了错误的课程id{}", course_id)
            return json({"error": "Course not found"}, status=404)

        plans = plan_model.plans

        return json(
            {
                plan: {
                    "expire": sp.get_grace_period(course_id, plan),
                    "refund": sp.get_refund_period(course_id),
                    "prime": sp.get_prime_period(course_id, plan),
                }
                for plan in plans
            }
        )
    except KeyError:
        return json({"error": "Course not found"}, status=404)


@router.put("/api/admin/courses/{registration_id}")
@admin_required
async def update_course_registration(request: Request, registration_id: int):
    """更新课程注册信息"""
    try:
        data = await request.json()
        registration = RegistryModel(**data)

        # todo: 检查是否已过 prime_time，如果过期，则需要调整url？
        # 调整grace time
        retention_period = sp.get_retention_period(registration.course)
        registration.retention_end = registration.prime_end + timedelta(
            days=retention_period
        )

        registration.update(registration_id)
        return json({"status": "ok"}, status=200)
    except ValidationError as e:
        return json({"error": str(e)}, status=400)


@router.delete("/api/admin/courses/{registration_id}")
@admin_required
async def delete_course_registration(_: Request, registration_id: int):
    """注销课程注册信息"""
    try:
        registration = RegistryModel.get_registration(registration_id)
        if registration is None:
            return json({"error": "Registration not found"}, status=404)

        # todo: 检查是否已超过grace end time，否则不能删除容器
        now = datetime.datetime.now()
        has_own_container = sp.has_own_container(registration.course, registration.plan)
        if (
            has_own_container
            and registration.retention_end
            and now > registration.retention_end
        ):
            customer = CustomerModel.find_customer(registration.customer_id)
            if customer is None:
                logger.error("Customer not found {}", registration.customer_id)
            else:
                # 删除容器
                delete_container(registration.course, customer.account)

        registration.delete()
        return json({"status": "ok"}, status=200)
    except ValidationError as e:
        return json({"error": str(e)}, status=400)
