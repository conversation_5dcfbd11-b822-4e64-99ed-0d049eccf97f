"""Academy API module for handling course data for the academy page."""

import datetime

from blacksheep import Request, json
from loguru import logger

from provision.api.auth import login_required
from provision.common.authenticated_request import AuthenticatedRequest
from provision.common.helper import random_image
from provision.common.router import router
from provision.conf import cfg
from provision.conf.plans import sp
from provision.service.resources import rm
from provision.store.customer import CustomerModel
from provision.store.registry import RegistryModel
from provision.store.resource import ResourceModel


def build_recommended_courses(courses: list[str]):
    """ "提取courses中的信息，生成推荐课程列表"""
    rec_count = 0
    recommended_courses = []
    for course_id in courses:
        rec_count += 1
        if rec_count > 5:
            break

        chapters = ResourceModel.count(course_id, "courseware")
        resource_config = cfg.get_resources_config(course_id)

        recommended_courses.append(
            {
                "id": rec_count,
                "name": resource_config.title,
                "price": resource_config.price,
                "image": resource_config.cover,
                "level": resource_config.level,
                "desc": resource_config.desc,
                "courseId": course_id,
                "chapters": chapters,
                "duration": resource_config.video_length,
                "buyLink": resource_config.buy_link,
                "detail": getattr(resource_config, 'detail', None),
            }
        )

    return recommended_courses


@router.get("/api/academy/home/<USER>")
@login_required
async def get_academy_data(request: AuthenticatedRequest):
    """获取academy页面所需的所有数据，包括用户课程、推荐课程和已完成课程"""
    customer_id = request.customer_id

    customer = CustomerModel.find_customer(customer_id)
    if customer is None:
        return json({"error": "Customer not found"}, status=404)

    try:
        # 1. 获取用户的当前课程（只包含未过期且活跃的课程）
        division = "course"
        registrations = RegistryModel.find_registration_by_customer(
            customer_id, division
        )

        # 1.1 获取用户的所有课程（包括已过期的），用于排除推荐课程
        all_registrations = RegistryModel.find_registration_by_customer(
            customer_id, division, include_expired=True, include_inactive=True
        )

        # 3. 处理用户课程数据
        now = datetime.datetime.now(tz=cfg.get_timezone())

        # 跟踪用户所有的课程ID（包括当前学习、已完成和已过期的）
        user_course_ids = set()

        if all_registrations is None:
            completed_courses = []
            recommended_courses = build_recommended_courses(
                cfg.get_course_by_division("course")
            )

            return json(
                {
                    "ongoing": [],
                    "recommendedCourses": recommended_courses,
                    "completedCourses": [],
                }
            )

        # 从所有注册记录中收集课程ID
        for reg in all_registrations:
            user_course_ids.add(reg.course)

        # 对客户注册的每一门课
        ongoing = []
        completed_courses = []

        for i, reg in enumerate(registrations or [], 1):
            course_id = reg.course
            resource_config = cfg.get_resources_config(course_id)

            # 判断课程是否已完成 - 当前时间超过primeTime即为已完成
            if now > reg.prime_end:
                completed_courses.append(
                    {
                        "id": i,
                        "name": resource_config.title,
                        "courseId": course_id,
                        "expireTime": reg.expire_time.isoformat(),
                        "url": f"/course/{course_id}/{cfg.get_shared_account()}/lab",
                    }
                )
                continue

            has_container = sp.has_own_container(course_id, reg.plan)
            if customer.account == cfg.get_preview_account() or (
                has_container and now < reg.prime_end
            ):
                account = customer.account
            else:
                account = cfg.get_shared_account()

            url_prefix = f"/course/{course_id}/{account}/lab/tree"

            item = {
                "title": resource_config.title,
                "plan": reg.plan,
                "image": resource_config.cover,
                "courseId": course_id,
                "account": account,
                "startTime": reg.start_time.isoformat(),
                "endTime": reg.prime_end.isoformat(),
                "expireTime": reg.expire_time.isoformat(),
                "refundEndTime": reg.refund_end.isoformat(),
                "courseware": [],
                "assignments": [],
            }
            ongoing.append(item)

            # 对每一门课中的courseware和assignments/questions资源
            for sub in ("courseware", "assignments/questions"):
                # 检查是否为preview账号，使用不同的标记逻辑
                if customer.account == cfg.get_preview_account():
                    labelled = rm.label_course_resources_for_preview(
                        course_id,
                        sub,
                        url_prefix,
                    )
                else:
                    labelled = rm.label_course_resources(
                        course_id,
                        sub,
                        reg.plan,
                        reg.start_time,
                        reg.refund_end,
                        now,
                        url_prefix,
                    )

                key = "courseware" if sub == "courseware" else "assignments"
                item[key] = labelled

        # 4. 生成推荐课程列表 - 排除用户已学习或已完成的课程
        course_unsubscribed = [
            course_id
            for course_id in cfg.get_course_by_division("course")
            if course_id not in user_course_ids
        ]
        recommended_courses = build_recommended_courses(course_unsubscribed)

        # 5. 返回所有数据
        return json(
            {
                "ongoing": ongoing,
                "recommendedCourses": recommended_courses,
                "completedCourses": completed_courses,
            }
        )

    except Exception as e:
        logger.error(f"Error getting academy data: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/academy/course-access/{course_id}")
@login_required
async def get_course_access_info(request: AuthenticatedRequest, course_id: str):
    """获取课程访问信息，包括课程ID和账号"""
    try:
        # 从请求上下文中获取 customer_id
        customer_id = request.customer_id

        # 获取用户信息
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            logger.warning(f"Customer {customer_id} not found")
            return json({"error": "Customer not found"}, status=404)

        # 获取课程注册信息
        registration = RegistryModel.find_subscription_by_customer_course(
            customer_id, course_id
        )
        if not registration:
            logger.warning(
                f"Registration for customer {customer_id}, course {course_id} not found"
            )
            return json({"error": "Registration not found"}, status=404)

        # 检查课程是否已过期
        now = datetime.datetime.now()
        if now > registration.expire_time:
            logger.warning(
                f"Registration expired for customer {customer_id}, course {course_id}"
            )
            return json({"error": "Registration expired"}, status=403)

        # 确定账号
        has_container = sp.has_own_container(course_id, registration.plan)
        account = cfg.get_shared_account() if not has_container else customer.account

        # 返回访问信息
        return json(
            {
                "accessInfo": {
                    "courseId": course_id,
                    "account": account,
                    "hasContainer": has_container,
                    "plan": registration.plan,
                }
            }
        )
    except Exception as e:
        logger.error(f"Error getting course access info: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/academy/blog/{customer_id}")
@login_required
async def get_blog_data(request: AuthenticatedRequest):
    """获取热门研究页面所需的数据，包括blog类型的资源列表和访问权限"""
    # 从请求上下文中获取 customer_id
    customer_id = request.customer_id

    customer = CustomerModel.find_customer(customer_id)
    if customer is None:
        return json({"error": "Customer not found"}, status=404)

    try:
        # 获取所有blog类型的资源
        blog_resources = ResourceModel.find_by_course("blog")
        if not blog_resources:
            return json({"articles": []})

        # 获取客户可访问的资源ID列表
        accessible_resources = rm.customer_resource_id_cache.get(customer_id, set())

        # 获取客户的blog课程注册信息
        blog_registration = RegistryModel.find_subscription_by_customer_course(
            customer_id, "blog"
        )

        # 构建文章列表，包含访问权限信息
        articles = []
        now = datetime.datetime.now(cfg.get_timezone())

        for resource in blog_resources:
            if resource.id is None:
                continue

            # 按新需求检查访问权限
            is_accessible = False
            unlock_time = None
            access_reason = None

            # 1. price_tier_id为null的资源，当成公开资源看，允许任何人访问
            if resource.price_tier_id is None:
                is_accessible = True
                access_reason = "public"
            # 2. 如果用户已获得某个资源的授权，则无论他有没有订阅博客，都允许访问
            elif resource.id in accessible_resources:
                is_accessible = True
                access_reason = "authorized"
            # 3. 如果用户没有订阅博客，并且没有获得资源的授权，则不允许访问
            elif not blog_registration:
                is_accessible = False
                access_reason = "not_subscribed"
            # 4. 如果用户已订阅博客，则按plans中的授权逻辑处理
            else:
                # 直接复用resource.py中的逻辑，检查资源是否在用户的访问缓存中
                # 这确保了与访问控制逻辑的一致性
                if resource.id in accessible_resources:
                    is_accessible = True
                    access_reason = "subscription_unlocked"
                else:
                    is_accessible = False
                    access_reason = "subscription_locked"

                    # 如果需要解锁时间，计算它（仅用于前端显示）
                    if resource.price_tier_id is not None:
                        # 获取blog课程的所有付费资源，按发布时间排序（blog是倒序）
                        all_paid_resources = [r for r in blog_resources if r.price_tier_id is not None]
                        all_paid_resources.sort(key=lambda x: x.publish_date or datetime.datetime.min, reverse=True)

                        # 找到当前资源在付费资源中的位置
                        try:
                            resource_index = next(i for i, r in enumerate(all_paid_resources, 1) if r.id == resource.id)

                            # 获取blog课程的计划配置
                            plan_model = sp.plans.get("blog")
                            if plan_model:
                                initial = plan_model.initial
                                release_every = plan_model.release_every

                                # 计算解锁时间（仅用于前端显示）
                                unlock_time = rm.calc_resource_unlock_time(
                                    resource_index,
                                    blog_registration.start_time,
                                    blog_registration.refund_end,
                                    initial,
                                    release_every
                                )
                        except StopIteration:
                            # 资源不在付费资源列表中
                            pass

            # 构建URL
            url = None
            if is_accessible:
                # 确定账号
                account = cfg.get_shared_account()
                if blog_registration and sp.has_own_container(
                    "blog", blog_registration.plan
                ):
                    account = customer.account

                url = f"/course/blog/{account}/lab/tree/{resource.rel_path}?articleId={resource.id}"

            img_url = resource.img or random_image()

            # 添加到文章列表
            article_data = {
                "id": resource.id,
                "title": resource.title,
                "description": resource.description,
                "publish_date": (
                    resource.publish_date.isoformat()
                    if resource.publish_date
                    else None
                ),
                "rel_path": resource.rel_path,
                "price": 0 if is_accessible else resource.get_price(),
                "url": url,
                "img": img_url,
                "buy_link": resource.get_buy_link() if not is_accessible else None,
                "is_accessible": is_accessible,
                "access_reason": access_reason,
                "has_subscription": blog_registration is not None,
            }

            # 如果有解锁时间，添加到响应中
            if unlock_time:
                article_data["unlock_time"] = unlock_time.isoformat()

            articles.append(article_data)

        # 按发布日期排序，最新的在前面
        articles.sort(key=lambda x: x.get("publish_date") or "", reverse=True)

        return json({"articles": articles})
    except Exception as e:
        logger.error(f"Error getting planet data: {e}")
        return json({"error": "Internal server error"}, status=500)
