"""Admin API for managing course registrations with resource access."""

from blacksheep import Request, json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.common.router import router
from provision.conf.plans import sp
from provision.service.resources import rm
from provision.store.db import pg
from provision.store.registry import RegistryModel
from provision.store.resource import ResourceModel


@router.get("/api/admin/registrations/{registration_id}/resources")
@admin_required
async def get_registration_resources(*, registration_id: int):
    """获取课程注册的资源访问权限"""
    try:
        # 获取注册记录
        registration = RegistryModel.get_registration(registration_id)
        if not registration:
            return json({"error": "Registration not found"}, status=404)

        # 获取该课程的所有资源
        all_resources = ResourceModel.find_by_course(registration.course)

        # 获取客户在该课程下的资源授权记录
        if registration.customer_id is None:
            return json({"error": "Registration has no customer_id"}, status=400)

        # 从 ServicePlan 中获取资源访问权限信息
        customer_id = registration.customer_id
        course = registration.course

        # 查询该课程下的所有资源
        resources_sql = """
            SELECT id, division, resource, rel_path
            FROM resources
            WHERE division = %s
        """
        all_course_resources = pg.fetch_records(resources_sql, [course]) or []

        # 获取客户有权访问的资源ID
        customer_resource_ids = set()
        for row in all_course_resources:
            resource_id = row[0]  # 第一列是 id
            # 检查客户是否有权限访问该资源
            if resource_id in rm.customer_resource_id_cache.get(customer_id, set()):
                customer_resource_ids.add(resource_id)

        # 构建响应
        return json(
            {
                "registration": registration.model_dump(),
                "all_resources": [resource.model_dump() for resource in all_resources],
                "authorized_resource_ids": list(customer_resource_ids),
            }
        )
    except Exception as e:
        logger.error(f"Error getting registration resources: {e}")
        return json({"error": str(e)}, status=500)


@router.put("/api/admin/registrations/{registration_id}/resources")
@admin_required
async def update_registration_resources(request: Request, registration_id: int):
    """更新课程注册的资源访问权限"""
    try:
        # 获取请求数据
        data = await request.json()
        resource_ids = data.get("resource_ids", [])

        # 获取注册记录
        registration = RegistryModel.get_registration(registration_id)
        if not registration:
            return json({"error": "Registration not found"}, status=404)

        if registration.customer_id is None:
            return json({"error": "Registration has no customer_id"}, status=400)

        # 更新资源访问权限
        try:
            # 直接更新 ServicePlan 中的缓存
            customer_id = registration.customer_id
            course = registration.course

            # 清空该客户在该课程下的资源访问缓存
            # 首先获取该课程下的所有资源
            resources_sql = """
                SELECT id, rel_path
                FROM resources
                WHERE division = %s
            """
            course_resources = pg.fetch_records(resources_sql, [course]) or []

            # 获取该课程下的所有资源ID
            course_resource_ids = set()
            for resource_id, _ in course_resources:
                course_resource_ids.add(resource_id)

            # 从客户的资源缓存中移除该课程下的所有资源
            if customer_id in rm.customer_resource_id_cache:
                # 获取当前客户的资源缓存
                current_resources = rm.customer_resource_id_cache[customer_id].copy()

                # 移除该课程下的所有资源
                current_resources -= course_resource_ids

                # 添加新的资源访问权限
                if resource_ids:
                    current_resources.update(resource_ids)

                # 更新缓存
                rm.customer_resource_id_cache[customer_id] = current_resources

            # 更新资源访问缓存
            rm.build_cache()
            success = True
        except Exception as e:
            logger.error(f"Failed to update resource access: {e}")
            success = False

        if success:
            return json({"message": "Resource access updated successfully"})
        else:
            return json({"error": "Failed to update resource access"}, status=500)
    except Exception as e:
        logger.error(f"Error updating registration resources: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/admin/courses/{course}/resources")
@admin_required
async def get_course_resources(*, course: str):
    """获取课程的所有资源"""
    try:
        # 验证课程是否有效
        if course not in sp.plans:
            return json({"error": f"Invalid course: {course}"}, status=400)

        # 获取该课程的所有资源
        all_resources = ResourceModel.find_by_course(course)

        # 获取该课程的计划信息
        available_plans = sp.get_plans_for_course(course)

        # 构建响应
        return json(
            {
                "course": course,
                "plans": available_plans,
                "resources": [resource.model_dump() for resource in all_resources],
            }
        )
    except Exception as e:
        logger.error(f"Error getting course resources: {e}")
        return json({"error": str(e)}, status=500)
