"""
价格档位相关的 API 路由
"""
from decimal import Decimal

from blacksheep import json
from loguru import logger

from provision.common.router import router
from provision.store.price_tier import PriceTierModel


@router.get("/api/price-tiers")
async def get_price_tiers():
    """获取所有价格档位"""
    try:
        tiers = PriceTierModel.find_all()
        result = [
            {
                "id": tier.id or 0,
                "price": float(tier.price),
                "wechat_url": tier.wechat_url
            }
            for tier in tiers
        ]
        return json(result)
    except Exception as e:
        logger.error(f"Error fetching price tiers: {e}")
        return json({"error": "Failed to fetch price tiers"}, status=500)


# 简化版本，只保留获取所有价格档位的 API
# 其他 CRUD 操作可以通过数据库管理工具直接操作
