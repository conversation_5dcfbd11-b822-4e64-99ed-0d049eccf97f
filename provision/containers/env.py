"""
# 创建网络环境，用以测试及生产。

## 功能
    1. 如果不存在名为course的网络，则创建一个名为course的网络。
    2. 创建nginx容器
    3. 创建课程容器，并自动挂载到网络，接受nginx代理

## 使用
   1. 在新的机器上，运行 provision setup，初始化环境
   2. 当增加新用户时，运行 provision add course_id name level [--instructor] [--redo] [--port]来为用户创建环境。

## TODO：
    1. 检测是否已安装docker (通过 docker命令)

"""

import queue
import sys
import threading
from pathlib import Path

from loguru import logger

from provision.common.helper import shell_execute, succeed, warn
from provision.conf import cfg

# 全局消息队列，用于存储容器创建过程中的日志
# 键为容器ID，值为该容器的消息队列
messages = {}


def get_queue(container_id: str):
    global messages

    if container_id not in messages:
        messages[container_id] = queue.Queue()

    return messages[container_id]


def get_message(container_id: str):
    """从指定容器的消息队列中获取一条消息"""
    global messages

    if container_id not in messages:
        return None
    try:
        return messages[container_id].get_nowait()
    except queue.Empty:
        return None


def put_message(container_id: str, message: str):
    """向指定容器的消息队列中添加一条消息"""
    global messages

    get_queue(container_id).put(message)


def attach_network(container: str, network: str = "course", alias: str = ""):
    """Attach containers to a network"""

    cmd = f"docker network ls --filter name={network} --format '{{{{.Name}}}}'"
    result = shell_execute(cmd)
    if result == "":
        print(f"Creating network {network}")
        shell_execute(f"docker network create {network}")

    if alias != "":
        alias = f"--alias {alias}"
    cmd = f"docker network connect {alias} {network} {container}"
    shell_execute(cmd)


def create_nginx_container():
    """Create an nginx container

    docker run -d -p 10080:80 -p 10443:443 nginx
    """
    nginx_settings = cfg.provision.nginx.model_dump(by_alias=True, exclude_none=True)
    ports = nginx_settings.get("ports") or [{8080: 80}, {8443: 443}]
    name = nginx_settings.get("name") or "nginx"
    network = nginx_settings.get("network") or "course"
    network_alias = nginx_settings.get("network_alias") or ""

    port_mapping = ""
    for item in ports:
        host_port, container_port = list(item.items())[0]
        port_mapping += f"-p {host_port}:{container_port} "

    vol_mapping = ""
    for _from, to in zip(
        [
            "/var/log/nginx",
            "/etc/letsencrypt",
            str(cfg.get_config_dir() / "nginx"),
            str(Path(cfg.provision.site).expanduser()),
        ],
        ["/var/log/nginx", "/etc/letsencrypt", "/etc/nginx", "/var/html/www"],
    ):
        host = Path(_from).expanduser()
        vol_mapping += f"-v {host}:{to} "

    # ubuntu下docker不支持host.docker.internal
    cmd = f"docker run -d --name {name} {port_mapping} {vol_mapping} --add-host=host.docker.internal:host-gateway nginx"
    logger.info(f"Creating container {name} with command: {cmd}")
    shell_execute(cmd)

    if network != "":
        attach_network(name, network, network_alias)

    # check if setup success
    cmd = f"docker exec {name} nginx -t"
    if "successful" not in shell_execute(cmd, output=sys.stdout):
        warn("Failed to create nginx container")
        cmd = "docker logs nginx"
        print(shell_execute(cmd))
        sys.exit(-1)
    else:
        succeed("Gateway created successfully")


def parse_vol_entry(item: dict, account: str, home: str) -> str:
    """解析volume mapping配置
    volumes:
    all: # 所有人应该加载的目录
      - from: courseware
      - from: supplements
      - from: startup
        to: /root/.ipython/profile_default/startup
      - from: nbgrader/exchange
        to: /root/exchange
        mode: rw
    instructor:
      - from: nbgrader/instructor
        to: ~/assignments
        mode: rw
    student:
      - from: nbgrader/student/{name}
        to: ~/assignments
        mode: rw
    """

    _from = item.get("from")
    assert _from, "from is required"
    # 注册用户名替换
    if _from.find("{account}") != -1:
        _from = _from.replace("{account}", account)

    _from = (Path(home) / _from).expanduser()
    _to = item.get("to") or f"/home/<USER>/notebooks/{Path(_from).name}"
    if _to.startswith("~"):
        _to = _to.replace("~", f"/home/<USER>/notebooks")

    mode = item.get("mode") or "ro"
    return f"-v {_from}:{_to}:{mode}"


def replace_marco(var: dict, values: dict):
    """进行宏替换"""
    if not var:
        return

    for k, v in var.items():
        if isinstance(v, str) and v.startswith("$"):
            replacement = values.get(v)
            var[k] = replacement
            print(f"replaced {k}/{v} to {k}/{replacement}")


def async_create_course_container(course_id: str, name: str, plan: str):
    """在新线程中创建课程容器

    这个函数替代了原来的异步函数，现在使用线程来避免阻塞主服务器线程
    """
    # 创建并启动新线程来执行容器创建
    thread = threading.Thread(
        target=create_course_container, args=(course_id, name, plan)
    )
    thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会退出
    thread.start()

    logger.info(f"Started container creation thread for {course_id}_{name}")


def create_course_container(
    course: str,
    account: str,
    plan: str,
    port: int | None = None,
    low_memory: str | None = None,
    high_memory: str | None = None,
    dry_run: bool = False,
):
    """创建课程环境容器

    - course_id: 课程代码，比如24, fa
    - account: 学生account（惟一）, course_course_name组成了容器ID。account只能是字母、数字和下划线
    - plan: 某些plan可能有不同的目录映射
    """
    # 为简单起见，将用户名及课程代码内部合成为一个惟一的user ID来使用。
    uid = f"{course}_{account}"
    container_name = f"course_{course}_{account}"

    # 创建一个容器ID用于WebSocket通信
    container_id = f"{course}_{account}"

    # 检查容器是否已存在
    result = shell_execute(
        f"docker ps -a --filter name={container_name} --format '{{{{.Names}}}}'"
    )

    if result != "":
        logger.info(f"Container {container_name} already exists.")
        # 添加消息到队列
        put_message(container_id, f"info|容器 {container_name} 已存在，无需重新创建")
        return

    # 添加开始创建容器的消息到队列
    put_message(container_id, f"info|开始为 {account} 创建 {course} 课程容器...")

    course_cfg = getattr(cfg.containers, course, None)
    assert course_cfg, f"Course {course} not found in configuration file."

    container = course_cfg.container  # type: ignore
    assert container, f"Container configuration not found for course {course}"

    # 根据account类型选择内存配置
    if low_memory is None or high_memory is None:
        memory_config = course_cfg.memory  # type: ignore
        if account == cfg.get_shared_account():
            # 使用share配置
            memory_type = memory_config.share
            put_message(container_id, f"info|使用共享账户内存配置")
        else:
            # 使用adhoc配置
            memory_type = memory_config.adhoc
            put_message(container_id, f"info|使用个人账户内存配置")

        if low_memory is None:
            low_memory = memory_type.low
        if high_memory is None:
            high_memory = memory_type.high

        put_message(container_id, f"info|内存配置: 预留={low_memory}, 限制={high_memory}")

    home = cfg.get_course_home(course)
    assert home, f"课程{course}的home目录未设置"

    vol_mapping = []
    volumes = container.model_dump(by_alias=True)
    for volume in volumes.get("volumes", {}).get("all", []):
        vol_entry = parse_vol_entry(volume, account, str(home))  # type: ignore
        vol_mapping.append(vol_entry)

    # volume for special plan
    for volume in volumes.get("volumes", {}).get(plan, []):
        vol_entry = parse_vol_entry(volume, account, str(home))  # type: ignore
        vol_mapping.append(vol_entry)

    # 增加startup和jupyter_lab_config映射，几乎是每个jupyter容器必备
    source = [
        home / ".startup",
        home / ".config/jupyter_lab_config.py",
        home / ".data/ro",
        home / ".data/rw",
    ]
    dst = [
        "/root/.ipython/profile_default/startup",
        "/root/.jupyter/jupyter_lab_config.py",
        "/data/ro",
        "/data/rw",
    ]
    modes = ["ro", "ro", "ro", "rw"]

    for i, from_ in enumerate(source):
        from_ = source[i]
        to = dst[i]
        mode = modes[i]
        entry = f"-v {from_}:{to}:{mode}"

        if entry not in vol_mapping:
            vol_mapping.append(entry)

    # 设置环境变量
    envars = {"course": course, "user": account}

    # merge extra envars
    extra_envars = container.envars
    if extra_envars:
        replace_marco(extra_envars, {"$user": account})
        envars.update(extra_envars)

    extra_args = container.args or []
    args_parts = " ".join(extra_args)
    volume_parts = " ".join(vol_mapping)
    envar_parts = " ".join([f"-e {k}={v}" for k, v in envars.items()])

    mem = f"--memory-reservation {low_memory} -m {high_memory}"
    image = container.image

    if port is None:
        cmd = f"docker run -d {mem} {volume_parts} {envar_parts} {args_parts} --net=course --name course_{uid} {image}"
    else:
        cmd = f"docker run -d {mem} {volume_parts} {envar_parts} {args_parts} -p {port}:8888 --net=course --name course_{uid} {image}"

    # 执行docker run命令
    put_message(container_id, f"正在创建容器:\n{cmd}")
    shell_execute(cmd, dry_run=dry_run)
    put_message(container_id, "容器创建成功！")

    # 执行容器创建后的任务
    post_docker_run(course, account, course_cfg.model_dump(), container_id)
    put_message(container_id, "EOF")


def post_docker_run(
    course_id: str, name: str, cfg: dict, container_id: str, dry_run: bool = False
):
    """在容器创建后，执行文件拷贝、安装等任务"""
    logger.info(f"Post-run tasks for course {course_id} and user {name}")
    put_message(container_id, "info|开始执行容器创建后的任务...")

    jobs = cfg.get("container", {}).get("postrun", {}) or {}
    home = Path(cfg.get("home", "")).expanduser()
    assert home, f"课程{course_id}的home目录未设置"

    # 执行容器内命令
    q = get_queue(container_id)
    if jobs.get("cmd"):
        for cmd in jobs.get("cmd") or []:
            full_cmd = f"docker exec --tty course_{course_id}_{name} {cmd}"
            shell_execute(full_cmd, output=q, dry_run=dry_run)

    # 复制文件到容器
    put_message(container_id, "info|正在复制课程文件到容器...")

    uid = f"{course_id}_{name}"
    if jobs.get("copy"):
        for items in jobs.get("copy") or []:
            for _from, _to in items.items():
                if _from.startswith("/"):
                    _from = Path(home) / _from[1:]
                else:
                    _from = Path(home) / "notebooks" / course_id / _from

                if _to.startswith("~"):
                    _to = _to.replace("~", f"/home/<USER>/notebooks")

                cmd = f"docker cp {_from} course_{uid}:{_to}"
                shell_execute(cmd, output=q, dry_run=dry_run)

    put_message(container_id, "info|容器设置完成，课程环境已准备就绪！")


def reload_nginx():
    """重载nginx配置"""
    cmd = "docker exec --tty nginx nginx -s reload"
    shell_execute(cmd)


def docker_append_file(container: str, file_path: str, content: str) -> None:
    """向容器内的文件追加内容。

    Args:
        file_path: 文件路径
        content: 待追加的内容
    """
    cmd = f"docker exec --tty {container} bash -c 'echo \"{content}\" >> {file_path}'"
    print(cmd)
    shell_execute(cmd)


def delete_container(course: str, account: str):
    """删除课程容器。

    如果用户名为shared,则不允许删除 -- 只能手动操作
    """
    if account in ["shared", "quantide", "research"]:
        logger.error(f"不允许通过程序删除共享容器 {account}")
        return

    container = f"course_{course}_{account}"
    cmd = f"docker ps -a --filter name={container} --format '{{{{.Names}}}}'"
    result = shell_execute(cmd)
    if result == "":
        logger.error(f"容器 {container} 不存在")
        return

    cmd = f"docker rm -f {container}"
    shell_execute(cmd)
    logger.info(f"容器 {container} 已删除")


if __name__ == "__main__":
    import fire

    fire.Fire({"nginx": create_nginx_container, "add": create_course_container})
