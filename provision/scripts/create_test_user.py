#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a test user in the database.
Run this script to add a test user that can be used for login testing.
"""

import os
import sys

# Add the parent directory to the path so we can import from provision
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from loguru import logger

from provision.conf import cfg
from provision.store.customer import CustomerModel
from provision.store.db import pg


def create_test_user(
    username, password, nickname="Test User", phone="***********", wx="testuser"
):
    """Create a test user with the given credentials."""
    try:
        # Check if user already exists
        existing_user = CustomerModel.find_customer_by_account(username)
        if existing_user:
            logger.info(f"User '{username}' already exists with ID {existing_user.id}")
            return existing_user.id

        # Create new user
        customer = CustomerModel(
            nickname=nickname,
            account=username,
            password=password,
            phone=phone,
            wx=wx,
            occupation="Test User",
        )

        # Hash the password
        hashed_password, salt = CustomerModel.hash_password(customer.password)
        customer.password = hashed_password
        customer.salt = salt

        # Insert the user into the database
        result = customer.insert_customer()
        customer_id = result["data"]["customer_id"]

        logger.info(f"Created new user '{username}' with ID {customer_id}")
        return customer_id

    except Exception as e:
        logger.error(f"Error creating test user: {e}")
        raise


if __name__ == "__main__":
    # Initialize configuration and database
    cfg.load_config()
    pg.connect(cfg)

    # Default test user credentials
    DEFAULT_USERNAME = "testuser"
    DEFAULT_PASSWORD = "password123"

    # Get username and password from command line arguments or use defaults
    username = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_USERNAME
    password = sys.argv[2] if len(sys.argv) > 2 else DEFAULT_PASSWORD
    nickname = sys.argv[3] if len(sys.argv) > 3 else "Test User"

    # Create the test user
    customer_id = create_test_user(username, password, nickname)

    print(f"Test user created successfully!")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print(f"Customer ID: {customer_id}")
    print("\nYou can now log in with these credentials.")
