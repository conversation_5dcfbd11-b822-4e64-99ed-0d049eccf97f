#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a course registration for the preview user.
"""

import datetime
import os
import sys

# Add the parent directory to the path so we can import from provision
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from loguru import logger

from provision.conf import cfg
from provision.store.customer import CustomerModel
from provision.store.db import pg
from provision.store.registry import RegistryModel


def create_preview_registration(course="fa", plan="bronze", account="preview"):
    """Create a course registration for the specified user."""
    try:
        # Find the user
        customer = CustomerModel.find_customer_by_account(account)
        if not customer:
            logger.error(f"User {account} not found")
            return None

        customer_id = customer.id
        logger.info(f"Found user {account} with ID: {customer_id}")

        # Check if registration already exists
        existing = RegistryModel.find_subscription_by_customer_course(customer_id, course)
        if existing:
            logger.info(f"Registration already exists for preview user and course {course}")
            return existing.id

        # Create registration dates
        now = datetime.datetime.now()
        start_time = now
        refund_end = now + datetime.timedelta(days=15)  # 15 days refund period
        prime_end = now + datetime.timedelta(days=60)   # 60 days prime period
        retention_end = prime_end + datetime.timedelta(days=30)  # 30 days retention period
        expire_time = now + datetime.timedelta(days=365) # 1 year expiration

        # Get course division
        division = cfg.get_course_division(course)

        # Create registration
        registration = RegistryModel(
            course=course,
            customer_id=customer_id,
            plan=plan,
            start_time=start_time,
            refund_end=refund_end,
            prime_end=prime_end,
            retention_end=retention_end,
            expire_time=expire_time,
            division=division,
            is_active=True
        )

        # Add to database
        result = RegistryModel.add(registration)
        registration_id = result["id"]

        logger.info(f"Created registration {registration_id} for user {account}: course={course}, plan={plan}")
        return registration_id

    except Exception as e:
        logger.error(f"Error creating preview registration: {e}")
        raise


if __name__ == "__main__":
    # Initialize configuration and database
    cfg.load_config()
    pg.connect(cfg)

    # Get course, plan, and account from command line arguments or use defaults
    course = sys.argv[1] if len(sys.argv) > 1 else "fa"
    plan = sys.argv[2] if len(sys.argv) > 2 else "bronze"
    account = sys.argv[3] if len(sys.argv) > 3 else "preview"

    # Create the registration
    registration_id = create_preview_registration(course, plan, account)

    if registration_id:
        print(f"Preview registration created successfully!")
        print(f"Course: {course}")
        print(f"Plan: {plan}")
        print(f"Registration ID: {registration_id}")
        print("\nYou can now test the preview account functionality.")
    else:
        print("Failed to create preview registration.")
