
# 关于匡醍

匡醍是一家专注于量化交易的科技公司，由前 IBM/Oracle 高级软件开发经理、海豚浏览器副总裁创立。我们的使命是通过高质量的教育内容和量化软件，帮助更多人掌握量化交易技能，推动金融平民化。

_匡醍成立于2025年2月28日，名字的含义是传播智慧和希望_。

<div style='width:33%;float:left;padding: 0.5rem 1rem 0 0;text-align:center'>
<img src='/assets/aaron.png' style="width: 100%">
</div>

我们开发了量化开源框架 Zillionare，为量化交易爱好者提供了强大的技术支持。我们的创始人是 Python 技术专家，《Python高效编程实战指南》作者，在量化交易和软件开发领域拥有丰富的经验。


匡醍（Quant
IDE）的探索始于2019年。创始人Aaron从海豚浏览器副总裁岗位上辞职，开始量化交易探索，并在当年3月x日，写下大富翁量化框架(Zillionare)的第一行代码。

## 匡醍好课之旅

两年后，Zillionare的开发得到认可，Aaron受朋友之邀，共同创立了格物致知，致立于量化投资。2023年晚春，我们开讲了第一门量化投资课程，即现在的《量化24课》。

2024年仲夏，我们开启了第二门量化投资课程，即《因子分析与机器学习策略》。随后在2025年春，我们开放了《量化人的Numpy&Pandas》，至此，量化投资课程最基本的框架已经搭建完成。

::: info
截止2025年5月止，据我们所知信息，已有4名名校博士、超过10家私募总、基金经理参与了我们的课程。
:::

下图显示了量化交易的全景图。《量化人的Numpy&Pandas》属于底座的课程，中间的部分对应着《量化24课》，在掌握这些知识之后，独立量化交易者和量化策略研究员就需要掌握策略研究方法，这就是《因子分析与机器学习策略》课程要解决的问题。

<div style='width:75%;text-align:center;margin: 0 auto 1rem'>
<img src='https://images.jieyu.ai/images/hot/cheese-course-roadmap.png' style="width: 75%">
</div>

