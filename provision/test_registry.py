#!/usr/bin/env python3
"""Test script for registry.py"""

from provision.conf import cfg
from provision.store.registry import RegistryModel
from provision.store.db import pg

def main():
    """Main function"""
    # Load configuration
    cfg.load_config()
    
    # Connect to database
    pg.connect(cfg)
    
    # Test find_all_registrations
    print("Testing find_all_registrations...")
    registrations, total_count = RegistryModel.find_all_registrations(
        page=1,
        page_size=20,
        sort_by=None,
        sort_order="asc",
        course_filter=None,
        plan_filter=None,
        include_inactive=True,
        is_active=True,
    )
    
    print(f"Found {len(registrations)} registrations (total: {total_count})")
    
    # Close database connection
    pg.close()

if __name__ == "__main__":
    main()
