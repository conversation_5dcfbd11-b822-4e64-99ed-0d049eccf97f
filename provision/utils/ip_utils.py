"""
IP地址相关工具函数
"""
from blacksheep import Request
from loguru import logger


def get_real_ip(request: Request) -> str|None:
    """
    获取客户端真实IP地址

    从nginx代理头中获取真实IP，优先级：X-Real-IP > X-Forwarded-For

    Args:
        request: BlackSheep请求对象

    Returns:
        str: 客户端真实IP地址
    """
    # 1. 检查 X-Real-IP 头（nginx推荐设置）
    x_real_ip = request.headers.get(b'X-Real-IP')
    if x_real_ip:
        header_value = x_real_ip[0].decode('utf-8') if isinstance(x_real_ip[0], bytes) else str(x_real_ip[0])
        if header_value and header_value != 'unknown':
            return header_value

    # 2. 检查 X-Forwarded-For 头（取第一个IP）
    x_forwarded_for = request.headers.get(b'X-Forwarded-For')
    if x_forwarded_for:
        header_value = x_forwarded_for[0].decode('utf-8') if isinstance(x_forwarded_for[0], bytes) else str(x_forwarded_for[0])
        real_ip = header_value.split(',')[0].strip()
        if real_ip and real_ip != 'unknown':
            return real_ip

    logger.warning("Failed to get real IP from request headers")
    return None
