"""Console script for provision."""

import datetime
import os
import shutil
import subprocess
import sys
import time
from pathlib import Path
from threading import Thread

import daemon
import fire
import uvicorn
from daemon import pidfile
from loguru import logger

from provision.common.helper import (
    backup_database,
    info,
    provision_version,
    set_env_variable,
    shell_execute,
    succeed,
    warn,
)
from provision.conf import cfg
from provision.containers.env import (
    create_course_container,
    create_nginx_container,
    get_message,
    messages,
    reload_nginx,
)
from provision.store.db import pg


def deploy_static_files():
    """
    将 static 目录下的文件拷贝到 $site
    该函数将在 provision deploy 命令中被调用
    """
    try:
        # 获取 static 目录
        package_dir = Path(__file__).parent
        static_dir = package_dir / "static"
        site_dir = Path(cfg.provision.site).expanduser()
        info(f"copy static files from {static_dir} to {site_dir}")

        shutil.copytree(static_dir, site_dir, dirs_exist_ok=True)
    except Exception as e:
        warn(f"Error copying static files: {e}")


def apply_factory_config(home: Path):
    """应用出厂配置到home目录

    Args:
        home (Path): 配置目录，并且记录为 PROVISION_CONFIG_DIR 环境变量
    """
    os.makedirs(home, exist_ok=True)
    set_env_variable("PROVISION_CONFIG_DIR", str(home))

    from_ = cfg.get_factory_config_dir()
    to = home

    # 主要配置文件：provision.yaml, plans.yaml, containers.yaml, resources.yaml
    for name in os.listdir(from_):
        if name.endswith(".yaml"):
            print(f"正在拷贝{name}到{to}")
            shutil.copy(f"{from_}/{name}", to / name)

    # copy nginx配置文件
    from_ = from_ / "nginx"
    to = to / "nginx"
    shutil.copytree(from_, to)


def setup(home: str = "~/.config/provision"):
    """Setup the environment for the first time

    主要任务是创建配置目录，拷贝默认文件
    Args:
        home: 保存配置的目录
    """
    home_dir = Path(home).expanduser()
    if home_dir.exists():
        redo = input("配置目录已存在，是否仍然要置配置？[y/n]")
        if redo.lower() != "y":
            info("用户取消了操作")
            sys.exit(0)

    apply_factory_config(home_dir)
    cfg.load_config()
    pg.connect(cfg)
    pg.init_db()

    # 创建运行目录
    try:
        shell_execute(f"mkdir -p {home_dir}/.local/run/provision")
    except PermissionError:
        warn(f"无法创建 {home_dir}/.local/run/provision 目录，请事先手动创建")
        sys.exit(1)

    succeed("已完成出厂配置设置，请进行修改")


def setup_course(course: str):
    """为课程进行启动设置"""
    home = cfg.get_course_home(course)
    if home is None:
        warn(f"资源{course}的home目录未设置")
        sys.exit(1)

    for sub in (".config", ".data", ".startup"):
        os.makedirs(home / sub, exist_ok=True)

    dst = home / ".config/jupyter_lab_config.py"
    if not dst.exists():
        src = cfg.get_factory_config_dir() / "jupyter_lab_config.py"
        shutil.copy(src, dst)

    dst = home / ".startup" / "startup.py"
    if not dst.exists():
        src = cfg.get_factory_config_dir() / f"startup/{course}.py"
        shutil.copy(src, dst)

    succeed(f"已为课程{course}创建配置目录")


def boot():
    """一台机器只应该执行一次的函数！除非大改版"""
    create_nginx_container()
    deploy_static_files()
    reload_nginx()


def run_server(run_as_daemon: bool = True, reload: bool = False):
    """Run the Provision server with the specified host and port.

    Args:
        run_as_daemon: If True, run in daemon mode
        reload: If True, enable auto-reload
    """
    host, port = cfg.provision.server.host, cfg.provision.server.port

    # 创建日志目录
    log_dir = Path("/var/log/provision")
    log_dir.mkdir(exist_ok=True, parents=True)

    # 配置 uvicorn 日志
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "default",
                "filename": "/var/log/provision/uvicorn.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["console", "file"], "level": "INFO"},
            "uvicorn.error": {"handlers": ["console", "file"], "level": "INFO"},
            "uvicorn.access": {"handlers": ["console", "file"], "level": "INFO"},
        },
    }

    if run_as_daemon:
        working_dir = (Path.home() / ".local/run/provision").absolute()
        working_dir.mkdir(parents = True, exist_ok=True)
        pid_file = working_dir / "provision.pid"

        logger.info(f"Starting Provision daemon on {host}:{port}")
        logger.info(f"working dir is {working_dir}")

        context = daemon.DaemonContext(
            pidfile=pidfile.TimeoutPIDLockFile(str(pid_file)),
            working_directory=str(working_dir),
            umask=0o002,
        )

        with context:
            uvicorn.run(
                "provision.app:app",
                host=host,
                port=port,
                reload=reload,
                log_config=log_config
            )
    else:
        print(f"Starting Provision server on {host}:{port}")
        uvicorn.run(
            "provision.app:app",
            host=host,
            port=port,
            reload=reload,
            log_config=log_config
        )


def stop():
    """Stop the provision daemon if running"""
    info("正在停止 Provision")
    pid_file = (Path.home() / ".local/run/provision/provision.pid").absolute()
    if pid_file.exists():
        with open(pid_file, "r", encoding="utf-8") as f:
            pid = int(f.read().strip())
            try:
                os.kill(pid, 15)  # SIGTERM
                time.sleep(3)
                succeed(f"Stopped provision daemon (PID: {pid})")
            except ProcessLookupError:
                warn("Provision daemon not running")
        pid_file.unlink(missing_ok=True)
    else:
        warn("未找到pid文件，请确认是否已启动过")


def status():
    """Check if provision daemon is running"""
    pid_file = (Path.home() / ".local/run/provision/provision.pid").absolute()
    if pid_file.exists():
        with open(pid_file, "r", encoding="utf-8") as f:
            pid = int(f.read().strip())
            try:
                os.kill(pid, 0)  # Check if process exists
                print(f"Provision daemon is running (PID: {pid})")
            except ProcessLookupError:
                print("Provision daemon not running (stale PID file)")
                pid_file.unlink()
    else:
        print("Provision daemon not running")


def dump_queue_messages():
    """
    Continuously prints messages from all queues until process exits.
    """
    while True:
        for container_id, _ in messages.items():
            msg = ""
            while msg != "EOF":
                msg = get_message(container_id)
                if msg == "EOF":
                    break
                if msg is not None:
                    print(msg)
                time.sleep(0.1)


def backup(backup_dir=None):
    """
    备份 Provision 的配置文件和数据库

    Args:
        backup_dir: 备份文件存储目录，默认为 ~/.backups/provision/YYYY-MM-DD_HHMMSS

    Returns:
        备份目录路径
    """
    # 获取当前时间作为备份目录名
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")

    # 设置默认备份目录
    if backup_dir is None:
        home_dir = Path.home()
        backup_base = home_dir / ".backups" / "provision"
        backup_dir = backup_base / timestamp
    else:
        backup_dir = Path(backup_dir)

    # 创建备份目录
    os.makedirs(backup_dir, exist_ok=True)
    print(f"Creating backup in: {backup_dir}")

    try:
        # 备份 PostgreSQL 数据库
        try:
            db_backup_file = backup_dir / "provision.sql"
            backup_database(db_backup_file)
            print(f"Database backup completed successfully: {db_backup_file}")
        except Exception as db_error:
            warn(f"Database backup failed: {db_error}")
            # 继续备份其他文件，不中断整个备份过程

        # 备份配置文件
        config_dir = cfg.get_config_dir()
        if config_dir.exists():
            config_backup_dir = backup_dir / "config"
            os.makedirs(config_backup_dir, exist_ok=True)

            # 备份 provision.yaml
            for file in config_dir.glob("*.yaml"):
                shutil.copy2(file, config_backup_dir / file.name)
                print(f"Backed up: {file}")

            # 备份 nginx 配置
            nginx_dir = config_dir / "nginx"
            if nginx_dir.exists():
                nginx_backup_dir = config_backup_dir / "nginx"
                shutil.copytree(nginx_dir, nginx_backup_dir, dirs_exist_ok=True)
                print(f"Backed up: {nginx_dir}")

        succeed(f"Backup completed successfully: {backup_dir}")
        return backup_dir

    except Exception as e:
        warn(f"备份时出错，升级将退出:\n{e}")
        shutil.rmtree(backup_dir)
        sys.exit(-1)


def upgrade(whl: str, run_as_daemon: bool = True, overwrite_config: bool = False):
    """Upgrade provision to the latest version"""
    python = sys.executable

    if overwrite_config:
        warn("您要求改写配置，此为危险操作，请输入 YES 来确认")
        if input("确认重写配置？") != "YES":
            warn("取消操作")
            sys.exit(1)

    # 0. stop the server
    stop()

    # 1. backup configs
    backup()

    # 2. uninstall provision
    args = [python, "-m", "pip", "uninstall", "-y", "provision"]
    result = subprocess.run(args, check=False)

    if result.returncode != 0:
        warn("Failed to uninstall provision")
        sys.exit(1)

    # 3. install the new version
    args = [python, "-m", "pip", "install", whl]
    result = subprocess.run(args, check=False)

    if result.returncode != 0:
        warn("Failed to install the new version")
        sys.exit(1)

    cmd = "provision upgrade_run_new_version"
    if run_as_daemon:
        cmd += " --run-as-daemon"
    else:
        cmd += " --run-as-daemon false"

    if overwrite_config:
        cmd += " --overwrite-config"
    else:
        cmd += " --overwrite-config false"

    shell_execute(cmd)

def upgrade_run_new_version(overwrite_config: bool, run_as_daemon: bool):
    """在新的版本下完成配置和设置"""
    info(f"running in new version {provision_version}")
    # 4. 应用新的配置
    if overwrite_config:
        shutil.rmtree(cfg.get_config_dir())
        apply_factory_config(cfg.get_config_dir())

    # 5. copy front static files to nginx
    deploy_static_files()

    # 6. start the server
    if run_as_daemon:
        args = ["provision", "start", "--run-as-daemon"]
        subprocess.Popen(args, start_new_session=True)
    else:
        args = ["provision", "start", "--run-as-daemon", "false"]
        subprocess.Popen(args, start_new_session=True)

    succeed("Provision upgraded successfully")


def main():
    """Main entry point for the CLI."""
    if len(sys.argv) >= 1 and sys.argv[1] != "setup":
        try:
            cfg.load_config()
        except FileNotFoundError:
            warn("配置文件不存在，请先运行 provision setup")
            sys.exit(-1)

    fire.Fire(
        {
            "setup": setup,
            "setup-course": setup_course,
            "add": create_course_container,
            "boot": boot,
            "start": run_server,
            "stop": stop,
            "status": status,
            "upgrade": upgrade,
            "upgrade_run_new_version": upgrade_run_new_version
        }
    )
    Thread(target=dump_queue_messages, daemon=True).start()
    time.sleep(1.5)


if __name__ == "__main__":
    main()  # pragma: no cover
