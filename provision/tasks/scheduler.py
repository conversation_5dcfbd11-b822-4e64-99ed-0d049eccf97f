"""
定时任务调度器模块

用于管理和执行各种定时任务，包括资源访问权限更新、数据库备份等。
"""

import datetime
import os
from pathlib import Path

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger

from provision.common.helper import backup_database
from provision.service.resources import rm


def update_resource_access() -> None:
    """更新资源访问权限

    获取所有有效的课程注册记录，然后更新ResourceManager的缓存
    """
    try:
        # 直接调用ResourceManager的build_cache方法更新所有客户的资源访问缓存
        rm.build_cache()

        logger.info("Resource access cache updated successfully")
    except Exception as e:
        logger.error(f"Failed to update resource access cache: {e}")


def scheduled_backup_database() -> None:
    """定时备份数据库

    使用 pg_dump 命令备份 PostgreSQL 数据库，并保留最近的10个备份
    """
    try:
        # 获取当前时间作为备份文件名
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")

        # 设置备份目录
        home_dir = Path.home()
        backup_base = home_dir / ".backups" / "provision"
        os.makedirs(backup_base, exist_ok=True)

        # 备份数据库
        backup_file = backup_base / f"provision_{timestamp}.sql"
        backup_database(backup_file)

        # 保留最近的10个备份，删除旧的备份
        all_backups = sorted(backup_base.glob("provision_*.sql"), key=os.path.getmtime)
        if len(all_backups) > 10:
            for old_backup in all_backups[:-10]:
                os.remove(old_backup)
                logger.info(f"Removed old backup: {old_backup}")

        logger.info(f"Database backup completed successfully: {backup_file}")
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")


# 创建调度器
scheduler = BackgroundScheduler()


def init_scheduler() -> None:
    """初始化调度器，添加定时任务"""
    if not scheduler.running:
        # 添加每15分钟更新一次资源访问缓存的任务
        scheduler.add_job(
            update_resource_access,
            trigger=IntervalTrigger(minutes=15),
            id="update_resource_access",
            replace_existing=True,
        )

        # 添加每周日凌晨2点执行数据库备份的任务
        scheduler.add_job(
            scheduled_backup_database,
            trigger=CronTrigger(day_of_week="sun", hour=2, minute=0),
            id="weekly_database_backup",
            replace_existing=True,
        )

        logger.info(
            "Scheduler initialized with resource access update and database backup tasks"
        )


def start_scheduler() -> None:
    """启动调度器"""
    if not scheduler.running:
        scheduler.start()
        logger.info("Scheduler started")


def stop_scheduler() -> None:
    """停止调度器"""
    if scheduler.running:
        scheduler.shutdown()
        logger.info("Scheduler stopped")


if __name__ == "__main__":
    # 直接运行此模块时，执行资源访问更新
    update_resource_access()
