"""
访问记录数据模型
"""
import json
from datetime import datetime
from typing import Any, Dict, Optional

from loguru import logger
from pydantic import BaseModel

from provision.store.db import pg


class VisitModel(BaseModel):
    """访问记录模型"""
    id: Optional[int] = None
    customer_id: Optional[int] = None
    ip_address: str
    user_agent: Optional[str] = None
    device_info: Optional[str] = None  # JSON字符串
    url_path: Optional[str] = None
    timestamp: Optional[datetime] = None

    class Config:
        from_attributes = True



    @classmethod
    def create_visit(
        cls,
        ip_address: str,
        customer_id: Optional[int] = None,
        user_agent: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None,
        url_path: Optional[str] = None
    ) -> Optional['VisitModel']:
        """创建访问记录"""
        try:
            device_info_str = json.dumps(device_info) if device_info else None

            result = pg.fetch_record("""
                INSERT INTO visits (customer_id, ip_address, user_agent, device_info, url_path)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id, customer_id, ip_address, user_agent, device_info, url_path, timestamp
            """, [customer_id, ip_address, user_agent, device_info_str, url_path], readonly=False)

            if result:
                return cls(
                    id=result['id'],
                    customer_id=result['customer_id'],
                    ip_address=result['ip_address'],
                    user_agent=result['user_agent'],
                    device_info=result['device_info'],
                    url_path=result['url_path'],
                    timestamp=result['timestamp']
                )
            return None
        except Exception as e:
            logger.error(f"创建访问记录失败: {e}")
            return None

    @classmethod
    def find_by_id(cls, visit_id: int) -> Optional['VisitModel']:
        """根据ID查找访问记录"""
        return pg.fetchone(cls, """
            SELECT id, customer_id, ip_address, user_agent, device_info, url_path, timestamp
            FROM visits WHERE id = %s
        """, [visit_id])



    def get_device_info_dict(self) -> Optional[Dict[str, Any]]:
        """获取设备信息字典"""
        if self.device_info:
            try:
                return json.loads(self.device_info)
            except json.JSONDecodeError:
                logger.error(f"解析设备信息失败: {self.device_info}")
        return None
