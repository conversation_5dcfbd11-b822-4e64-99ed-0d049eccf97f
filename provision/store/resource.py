"""
资源管理模块，用于管理资源
"""

import datetime
from typing import List, Optional

from loguru import logger
from pydantic import BaseModel

from provision.store.db import pg
from provision.store.price_tier import PriceTierModel


class ResourceModel(BaseModel):
    """
    资源模型

    Attributes:
        course: 资源的大类，如 l24, fa, pandas等
        resource: 资源小类，如 courseware, assignments/questions 等
        seq: 资源在小类中的序号，用来排序和进度控制
        title: 资源标题
        description: 资源描述
        rel_path: 资源在本地的相对路径
        publish_date: 资源创建/发布时间
        price: 资源价格
        division: 资源所属门类，如course, blog。不同门类的展示机制不同、资源控制进度方式不同。
        img: 资源的封面图片URL
    """

    course: str
    resource: str
    seq: int
    title: str
    description: Optional[str] = None
    rel_path: str
    publish_date: Optional[datetime.datetime] = None
    price: float = 0.0  # 保持兼容性，实际使用 price_tier_id
    price_tier_id: Optional[int] = None  # 价格档位ID
    division: str
    img: Optional[str] = None

    # 服务端生成字段
    id: Optional[int] = None

    @classmethod
    def find_by_id(cls, resource_id: int) -> Optional["ResourceModel"]:
        """根据ID查找资源"""
        sql = "SELECT * FROM resources WHERE id = %s"
        return pg.fetchone(ResourceModel, sql, [resource_id])

    @classmethod
    def find_by_course_path(
        cls, course: str, rel_path: str
    ) -> Optional["ResourceModel"]:
        """根据course和相对路径查找资源"""
        sql = "SELECT * FROM resources WHERE course = %s AND rel_path = %s"
        return pg.fetchone(ResourceModel, sql, [course, rel_path])

    @classmethod
    def find_all(
        cls,
        course: Optional[str] = None,
        resource: Optional[str] = None,
        search_query: Optional[str] = None,
        page: int = 1,
        page_size: int = 0,
    ) -> List["ResourceModel"]:
        """查找所有资源"""
        params = []
        sql = "SELECT * FROM resources WHERE 1=1"

        if course:
            sql += " AND course = %s"
            params.append(course)

        if resource:
            sql += " AND resource = %s"
            params.append(resource)

        if search_query:
            sql += " AND (title LIKE %s OR description LIKE %s OR rel_path LIKE %s)"
            search_term = f"%{search_query}%"
            params.extend([search_term, search_term, search_term])

        sql += " ORDER BY course, resource, seq"

        if page_size > 0:
            sql += f" LIMIT {page_size} OFFSET {(page - 1) * page_size}"

        return pg.fetchall(ResourceModel, sql, params) or []

    @classmethod
    def count(
        cls,
        course: Optional[str] = None,
        resource: Optional[str] = None,
        search_query: Optional[str] = None,
    ) -> int:
        """计算资源总数"""
        params = []
        sql = "SELECT COUNT(*) FROM resources WHERE 1=1"

        if course:
            sql += " AND course = %s"
            params.append(course)

        if resource:
            sql += " AND resource = %s"
            params.append(resource)

        if search_query:
            sql += " AND (title LIKE %s OR description LIKE %s OR rel_path LIKE %s)"
            search_term = f"%{search_query}%"
            params.extend([search_term, search_term, search_term])

        result = pg.fetch_record(sql, params)
        return result["count"] if result else 0

    @classmethod
    def find_by_course(cls, course: str) -> List["ResourceModel"]:
        """根据 course 查找资源"""
        sql = "SELECT * FROM resources WHERE course = %s ORDER BY resource, seq"
        return pg.fetchall(ResourceModel, sql, [course])  # type: ignore

    def get_price(self) -> float:
        """"根据price_tier_id获取价格"""
        if not self.price_tier_id:
            return 0.0
        tier: Optional[PriceTierModel] = PriceTierModel.find_by_id(self.price_tier_id)
        return tier.price if tier else 0.0

    def get_buy_link(self) -> Optional[str]:
        """根据price_tier_id获取购买链接"""
        if not self.price_tier_id:
            return None
        tier: Optional[PriceTierModel] = PriceTierModel.find_by_id(self.price_tier_id)
        return tier.wechat_url if tier else None

    @classmethod
    def get_course(cls) -> List[str]:
        """获取所有资源种类"""
        sql = "SELECT DISTINCT course FROM resources ORDER BY course"
        return pg.fetch_items(sql, "course")

    @classmethod
    def get_resource_folders(cls, course: Optional[str] = None) -> List[str]:
        """获取所有资源类型"""
        params = []
        sql = "SELECT DISTINCT resource FROM resources"

        if course:
            sql += " WHERE course = %s"
            params.append(course)

        sql += " ORDER BY resource"
        return pg.fetch_items(sql, "resource", params)

    @classmethod
    def get_restricted_folders(cls, course: Optional[str] = None) -> List[str]:
        """获取所有受控子目录（兼容旧代码）"""
        return cls.get_resource_folders(course)

    def save(self) -> int:
        """保存资源

        除了course和rel_path之外，其他字段只有在数据库中为空时才进行更新。
        """
        # 使用简单的分步操作：先尝试查找现有记录
        existing = self.find_by_course_path(self.course, self.rel_path)

        if existing:
            # 记录存在，只更新空字段
            sql = """
                UPDATE resources
                SET
                    resource = COALESCE(NULLIF(resource, ''), %s),
                    seq = COALESCE(seq, %s),
                    title = COALESCE(NULLIF(title, ''), %s),
                    description = COALESCE(NULLIF(description, ''), %s),
                    publish_date = COALESCE(publish_date, %s),
                    price = COALESCE(price, %s),
                    price_tier_id = COALESCE(price_tier_id, %s),
                    division = COALESCE(NULLIF(division, ''), %s),
                    img = COALESCE(NULLIF(img, ''), %s)
                WHERE id = %s
                RETURNING id
            """

            args = [
                self.resource,
                self.seq,
                self.title,
                self.description,
                self.publish_date,
                self.price,
                self.price_tier_id,
                self.division,
                self.img,
                existing.id,
            ]

            rowid = pg.fetch_item(sql, "id", args, readonly=False)
            self.id = existing.id
        else:
            # 记录不存在，执行插入
            sql = """
                INSERT INTO resources (course, resource, seq, title, description, rel_path, publish_date, price, price_tier_id, division, img)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """

            args = [
                self.course,
                self.resource,
                self.seq,
                self.title,
                self.description,
                self.rel_path,
                self.publish_date,
                self.price,
                self.price_tier_id,
                self.division,
                self.img,
            ]

            rowid = pg.fetch_item(sql, "id", args, readonly=False)
            if not rowid:
                raise ValueError("Failed to insert resource")

            self.id = rowid

        return self.id  # type: ignore

    @classmethod
    def create_with_auto_seq(cls, meta: dict) -> int:
        """
        创建新资源，自动生成 seq 字段

        使用更简洁的方案：利用数据库的原子性操作来生成 seq

        Args:
            meta: 资源元数据字典，包含除 seq 外的所有必需字段

        Returns:
            int: 新创建资源的 ID

        Raises:
            ValueError: 当插入失败时
        """
        course = meta["course"]
        resource_type = meta["resource"]
        division = meta["division"]
        meta["price_tier_id"] = PriceTierModel.find_id_by_price(meta.get("price", 0))

        # 使用单个原子操作生成 seq 并插入记录
        insert_sql = """
            INSERT INTO resources (course, resource, seq, title, description, rel_path, publish_date, price, price_tier_id, division, img)
            VALUES (
                %s, %s,
                (SELECT COALESCE(MAX(seq), 0) + 1 FROM resources WHERE course = %s AND resource = %s AND division = %s),
                %s, %s, %s, %s, %s, %s, %s, %s
            )
            RETURNING id, seq
        """

        try:
            result = pg.fetch_record(
                insert_sql,
                [
                    meta["course"],                    # course (for INSERT)
                    meta["resource"],                  # resource (for INSERT)
                    meta["course"],                    # course (for subquery)
                    meta["resource"],                  # resource (for subquery)
                    meta["division"],                  # division (for subquery)
                    meta["title"],
                    meta.get("description"),
                    meta["rel_path"],
                    meta.get("publish_date"),
                    meta.get("price", 0.0),
                    meta.get("price_tier_id"),
                    meta["division"],
                    meta.get("img")
                ],
                readonly=False
            )

            if not result:
                raise ValueError("Failed to insert resource")

            resource_id, generated_seq = result["id"], result["seq"]
            logger.info(f"Auto-generated seq {generated_seq} for course={course}, resource={resource_type}, division={division}")

            return resource_id

        except Exception as e:
            logger.error(f"Error creating resource with auto seq: {e}")
            raise

    @classmethod
    def delete(cls, resource_id: int) -> bool:
        """删除资源

        注意：此方法只删除资源本身，不处理相关的白名单记录和缓存更新。
        这些操作应该在业务层（access/resources.py）中处理。
        """
        try:
            # 删除资源本身
            sql = "DELETE FROM resources WHERE id = %s"
            pg.execute(sql, [resource_id])

            return True
        except Exception as e:
            logger.error(f"Error deleting resource: {e}")
            return False

    @classmethod
    def find_course_sub_resources(
        cls, course: str, sub: str, order_ascend: bool = True
    ) -> Optional[List["ResourceModel"]]:
        """按course, sub来查找资源"""
        if order_ascend:
            sql = """SELECT * FROM resources WHERE course = %s AND resource = %s ORDER BY seq"""
        else:
            sql = """SELECT * FROM resources WHERE course = %s AND resource = %s ORDER BY seq DESC"""

        return pg.fetchall(ResourceModel, sql, [course, sub])
