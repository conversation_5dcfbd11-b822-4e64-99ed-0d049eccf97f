"""
公开资源白名单管理模块，用于管理对所有人开放的资源
"""

import datetime
from typing import List, Optional

from loguru import logger

from provision.store.db import pg
from provision.store.resource import ResourceModel


class WhitelistForAllModel:
    """
    公开资源白名单模型

    Attributes:
        resource_id: 资源ID
        course: 课程ID
        timestamp: 创建时间
    """

    def __init__(
        self,
        resource_id: int,
        course: str,
        timestamp: Optional[datetime.datetime] = None,
        id: Optional[int] = None,
    ):
        self.resource_id = resource_id
        self.course = course
        self.timestamp = timestamp or datetime.datetime.now()
        self.id = id

    @classmethod
    def find_by_id(cls, whitelist_id: int) -> Optional["WhitelistForAllModel"]:
        """根据ID查找公开白名单记录"""
        sql = "SELECT * FROM whitelist_for_all WHERE id = %s"
        return pg.fetchone(cls, sql, [whitelist_id])

    @classmethod
    def find_by_resource(cls, resource_id: int) -> Optional["WhitelistForAllModel"]:
        """根据资源ID查找公开白名单记录"""
        sql = "SELECT * FROM whitelist_for_all WHERE resource_id = %s"
        return pg.fetchone(cls, sql, [resource_id])

    @classmethod
    def find_all(cls) -> List["WhitelistForAllModel"]:
        """获取所有公开资源"""
        sql = "SELECT * FROM whitelist_for_all"
        return pg.fetchall(cls, sql) or []

    @classmethod
    def add_resource(cls, resource_id: int) -> bool:
        """
        添加公开资源

        Args:
            resource_id: 资源ID
        """
        try:
            # 获取资源信息
            resource = ResourceModel.find_by_id(resource_id)
            if not resource:
                logger.error(f"Resource {resource_id} not found")
                return False

            # 检查是否已存在
            existing = cls.find_by_resource(resource_id)
            if existing:
                logger.info(f"Resource {resource_id} is already public")
                return True

            # 添加新记录
            whitelist = cls(
                resource_id=resource_id,
                course=resource.course,
            )
            whitelist.save()

            logger.info(f"Added public resource {resource_id}")
            return True
        except Exception as e:
            logger.error(f"Error adding public resource: {e}")
            logger.exception(e)
            return False

    def save(self) -> int:
        """保存公开资源记录"""
        try:
            if self.id:
                # 更新
                sql = """
                    UPDATE whitelist_for_all
                    SET resource_id = %s,
                        course = %s,
                        timestamp = %s
                    WHERE id = %s
                """
                pg.execute(
                    sql,
                    [
                        self.resource_id,
                        self.course,
                        self.timestamp,
                        self.id,
                    ],
                )
                
                logger.info(f"Updated public resource {self.resource_id}")
                return self.id

            # 插入
            sql = """
                INSERT INTO whitelist_for_all (resource_id, course, timestamp)
                VALUES (%s, %s, %s)
                RETURNING id
            """
            rowid = pg.fetch_item(
                sql,
                "id",
                [
                    self.resource_id,
                    self.course,
                    self.timestamp,
                ],
                readonly=False
            )
            if rowid is None:
                raise ValueError("Failed to insert public resource record")

            self.id = rowid
            
            logger.info(f"Created public resource record for {self.resource_id}")
            return self.id
        except Exception as e:
            logger.error(f"Error saving public resource record: {e}")
            logger.exception(e)
            raise

    @classmethod
    def delete_resource(cls, resource_id: int) -> bool:
        """删除公开资源"""
        try:
            sql = "DELETE FROM whitelist_for_all WHERE resource_id = %s"
            pg.execute(sql, [resource_id])
            
            logger.info(f"Deleted public resource {resource_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting public resource: {e}")
            logger.exception(e)
            return False

    def model_dump(self) -> dict:
        """将模型转换为字典"""
        return {
            "id": self.id,
            "resource_id": self.resource_id,
            "course": self.course,
            "timestamp": self.timestamp,
        }
