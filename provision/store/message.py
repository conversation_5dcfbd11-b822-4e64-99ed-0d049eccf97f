import datetime
from typing import Any, Dict, List, Optional

from psycopg2.extras import RealDictCursor
from pydantic import BaseModel, Field

from provision.store.db import pg


class MessageModel(BaseModel):
    """消息模型类，用于处理消息相关的数据库操作"""

    title: str
    content: str
    sender_id: int
    recipient_id: int

    # 可选字段，创建时自动生成
    id: Optional[int] = None
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.now)
    is_read: bool = False
    is_deleted: bool = False

    @classmethod
    def find_messages_by_recipient(
        cls,
        recipient_id: int,
        include_deleted: bool = False,
        limit: int = 50,
        offset: int = 0,
    ) -> List[Dict[str, Any]]:
        """获取指定接收者的消息列表

        Args:
            recipient_id: 接收者ID
            include_deleted: 是否包含已删除的消息
            limit: 返回消息的最大数量
            offset: 分页偏移量

        Returns:
            消息列表，按创建时间倒序排列
        """
        sql = "SELECT * FROM messages WHERE recipient_id = %s"
        args = [recipient_id]

        if not include_deleted:
            sql += " AND is_deleted = FALSE"

        sql += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
        args.extend([limit, offset])

        results = pg.fetch_records(sql, args)

        return results or []

    @classmethod
    def count_unread_messages(cls, recipient_id: int) -> int:
        """获取指定接收者的未读消息数量

        Args:
            recipient_id: 接收者ID

        Returns:
            未读消息数量
        """
        sql = "SELECT COUNT(*) FROM messages WHERE recipient_id = %s AND is_read = FALSE AND is_deleted = FALSE"
        
        return pg.fetch_item(sql, "count", [recipient_id]) or 0

    @classmethod
    def get_message(cls, message_id: int) -> Optional[Dict[str, Any]]:
        """获取指定ID的消息

        Args:
            message_id: 消息ID

        Returns:
            消息详情，如果不存在则返回None
        """
        sql = "SELECT * FROM messages WHERE id = %s"

        result = pg.fetch_record(sql, [message_id])
        return result

    def save(self) -> int:
        """保存消息到数据库

        Returns:
            新创建的消息ID
        """
        sql = """
            INSERT INTO messages (title, content, sender_id, recipient_id, created_at, is_read, is_deleted)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        row_id = pg.fetch_item(
            sql,
            "id",
            [
                self.title,
                self.content,
                self.sender_id,
                self.recipient_id,
                self.created_at,
                self.is_read,
                self.is_deleted,
            ],
            readonly=False
        )

        if row_id is None:
            raise ValueError(f"消息写入失败， title: {self.title}, recipient: {self.recipient_id}")
        
        self.id = row_id
        return self.id

    @classmethod
    def mark_as_read(cls, message_id: int) -> bool:
        """将消息标记为已读

        Args:
            message_id: 消息ID

        Returns:
            操作是否成功
        """
        sql = "UPDATE messages SET is_read = TRUE WHERE id = %s"
        pg.execute(sql, [message_id])
        return True

    @classmethod
    def mark_as_deleted(cls, message_id: int) -> bool:
        """将消息标记为已删除（用户端）

        Args:
            message_id: 消息ID

        Returns:
            操作是否成功
        """
        sql = "UPDATE messages SET is_deleted = TRUE WHERE id = %s"
        pg.execute(sql, [message_id])
        return True

    @classmethod
    def delete_message(cls, message_id: int) -> bool:
        """从数据库中完全删除消息（管理端）

        Args:
            message_id: 消息ID

        Returns:
            操作是否成功
        """
        sql = "DELETE FROM messages WHERE id = %s"
        pg.execute(sql, [message_id])
        return True

    @classmethod
    def update_message(cls, message_id: int, title: str, content: str) -> bool:
        """更新消息内容（管理端）

        Args:
            message_id: 消息ID
            title: 新标题
            content: 新内容

        Returns:
            操作是否成功
        """
        sql = "UPDATE messages SET title = %s, content = %s WHERE id = %s"
        pg.execute(sql, [title, content, message_id])
        return True
