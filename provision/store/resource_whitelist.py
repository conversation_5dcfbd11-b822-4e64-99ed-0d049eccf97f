"""
资源白名单管理模块，用于管理资源访问权限
"""

import datetime
from typing import List, Optional

from loguru import logger
from pydantic import BaseModel

from provision.store.customer import CustomerModel
from provision.store.db import pg
from provision.store.resource import ResourceModel


class ResourceWhitelistModel(BaseModel):
    """
    资源白名单模型

    Attributes:
        customer_id: 客户ID
        resource_id: 资源ID
        course: 课程ID
        timestamp: 创建时间
    """

    customer_id: int
    resource_id: int
    course: str
    timestamp: Optional[datetime.datetime] = None

    # 服务端生成字段
    id: Optional[int] = None

    @classmethod
    def find_by_id(cls, whitelist_id: int) -> Optional["ResourceWhitelistModel"]:
        """根据ID查找白名单记录"""
        sql = "SELECT * FROM resource_whitelist WHERE id = %s"
        result = pg.fetchone(cls, sql, [whitelist_id])
        return result

    @classmethod
    def find_by_customer(
        cls, customer_id: int
    ) -> List["ResourceWhitelistModel"] | None:
        """根据客户ID查找白名单记录"""
        sql = "SELECT * FROM resource_whitelist WHERE customer_id = %s"
        return pg.fetchall(cls, sql, [customer_id])

    @classmethod
    def find_by_customer_course(
        cls, customer_id: int, course: str
    ) -> List["ResourceWhitelistModel"]:
        """根据客户ID和课程查找白名单记录"""
        sql = "SELECT * FROM resource_whitelist WHERE customer_id = %s AND course = %s"
        return pg.fetchall(cls, sql, [customer_id, course]) or []

    @classmethod
    def find_by_resource(cls, resource_id: int) -> List["ResourceWhitelistModel"]:
        """根据资源ID查找白名单记录"""
        sql = "SELECT * FROM resource_whitelist WHERE resource_id = %s"
        return  pg.fetchall(cls, sql, [resource_id]) or []

    @classmethod
    def find_by_customer_resource(
        cls, customer_id: int, resource_id: int
    ) -> Optional["ResourceWhitelistModel"]:
        """根据客户ID和资源ID查找白名单记录"""
        sql = (
            "SELECT * FROM resource_whitelist WHERE customer_id = %s AND resource_id = %s"
        )
        return pg.fetchone(cls, sql, [customer_id, resource_id])

    @classmethod
    def delete_by_customer_course(cls, customer_id: int, course: str) -> bool:
        """删除客户在特定课程下的所有白名单记录"""
        sql = "DELETE FROM resource_whitelist WHERE customer_id = %s AND course = %s"
        pg.execute(sql, [customer_id, course])
        
        logger.info(
            f"Deleted whitelist records for customer {customer_id} in course {course}"
        )
        return True

    @classmethod
    def update_customer_course_resources(
        cls, customer_id: int, course: str, resource_ids: List[int]
    ) -> bool:
        """更新客户在特定课程下的资源白名单"""
        try:
            # 先删除该客户在该课程下的所有白名单记录
            cls.delete_by_customer_course(customer_id, course)

            # 添加新的白名单记录
            for resource_id in resource_ids:
                whitelist = ResourceWhitelistModel(
                    customer_id=customer_id,
                    resource_id=resource_id,
                    course=course,
                    timestamp=datetime.datetime.now(),
                )
                whitelist.save()

            # 缓存更新应该在业务层处理
            logger.info(
                f"Updated whitelist for customer {customer_id} in course {course}"
            )
            return True
        except Exception as e:
            logger.error(f"Error updating whitelist: {e}")
            logger.exception(e)
            return False

    @classmethod
    def add_whitelist_item(cls, customer_id: int, resource_id: int) -> bool:
        """
        添加白名单记录

        Args:
            customer_id: 客户ID
            resource_id: 资源ID
        """
        try:
            # 获取资源信息
            resource = ResourceModel.find_by_id(resource_id)
            if not resource:
                logger.error(f"Resource {resource_id} not found")
                return False

            # 检查客户是否存在
            customer = CustomerModel.find_customer(customer_id)
            if not customer:
                logger.error(f"Customer {customer_id} not found")
                return False

            # 检查是否已存在
            existing = cls.find_by_customer_resource(customer_id, resource_id)
            if existing:
                logger.info(
                    f"Whitelist record already exists for customer {customer_id} and resource {resource_id}"
                )
                return True

            # 添加新记录
            whitelist = ResourceWhitelistModel(
                customer_id=customer_id,
                resource_id=resource_id,
                course=resource.course,
                timestamp=datetime.datetime.now(),
            )

            # 打印记录信息，用于调试
            logger.debug(f"Creating new whitelist record: {whitelist.model_dump()}")

            whitelist.save()
            logger.info(
                f"Added whitelist record for customer {customer_id} and resource {resource_id}"
            )
            return True
        except Exception as e:
            logger.error(f"Error adding whitelist record: {e}")
            logger.exception(e)
            return False

    def save(self) -> int:
        """保存白名单记录"""
        try:
            if self.id:
                # 更新
                sql = """
                    UPDATE resource_whitelist
                    SET customer_id = %s,
                        resource_id = %s,
                        course = %s,
                        timestamp = %s
                    WHERE id = %s
                """

                pg.execute(
                    sql,
                    [
                        self.customer_id,
                        self.resource_id,
                        self.course,
                        self.timestamp or datetime.datetime.now(),
                        self.id,
                    ],
                )
                
                logger.info(f"Whitelist record {self.id} has been updated")
                return self.id

            # 插入
            sql = """
                INSERT INTO resource_whitelist (customer_id, resource_id, course, timestamp)
                VALUES (%s, %s,%s,%s)
                RETURNING id
            """
            rowid = pg.fetch_item(
                sql,
                "id",
                [
                    self.customer_id,
                    self.resource_id,
                    self.course,
                    self.timestamp or datetime.datetime.now(),
                ],readonly=False
            )
            if rowid is None:
                raise ValueError("Failed to insert whitelist record")

            self.id = rowid
            
            logger.info(f"Whitelist record {self.id} has been created")
            return self.id
        except Exception as e:
            logger.error(f"Error saving whitelist record: {e}")
            logger.exception(e)
            raise

    @classmethod
    def delete_whitelist_item(cls, whitelist_id: int) -> bool:
        """删除白名单项"""
        try:
            sql = "DELETE FROM resource_whitelist WHERE id = %s"
            pg.execute(sql, [whitelist_id])
            
            logger.info(f"Whitelist item {whitelist_id} has been deleted")
            return True
        except Exception as e:
            logger.error(f"Error deleting whitelist item: {e}")
            logger.exception(e)

            return False
