"""
抽奖系统模型
"""

import datetime
from typing import Any, Dict, List, Optional, Self

from loguru import logger
from pydantic import BaseModel

from provision.store.customer import CustomerModel
from provision.store.db import pg


class LotteryTicket(BaseModel):
    """
    抽奖奖券模型

    Attributes:
        id: 奖券ID
        ticket_code: 奖券券码（5位数字、大写字母组合）
        customer_id: 用户ID
        course: 课程ID
        discount: 奖券面值
        base_fare: 最低消费额
        max_discount: 最大叠加金额
        created_at: 创建时间
        expires_at: 过期时间
        status: 状态（unused-未使用, transferring-转让中, transferred-已转让, used-已使用）
        transferred_to: 转让给的用户ID
        transferred_at: 转让时间
        used_at: 使用时间
        order_id: 关联的订单ID
    """

    id: Optional[int] = None
    ticket_code: str = "12345"  # 默认值，避免数据库错误
    customer_id: int
    course: str
    discount: int
    base_fare: int
    max_discount: int
    created_at: datetime.datetime = datetime.datetime.now()
    expires_at: datetime.datetime
    status: str = "unused"
    transferred_to: Optional[int] = None
    transferred_at: Optional[datetime.datetime] = None
    used_at: Optional[datetime.datetime] = None
    order_id: Optional[str] = None

    @classmethod
    def find_by_id(cls, ticket_id: int) -> Optional[Self]:
        """根据ID查找奖券"""
        sql = "SELECT * FROM lottery_tickets WHERE id = %s"

        return pg.fetchone(cls, sql, [ticket_id])

    @classmethod
    def find_by_code(cls, ticket_code: str) -> Optional[Self]:
        """根据券码查找奖券"""
        sql = "SELECT * FROM lottery_tickets WHERE ticket_code = %s"
        return pg.fetchone(cls, sql, [ticket_code])

    @classmethod
    def find_by_customer(
        cls, customer_id: int, status: Optional[str] = None
    ) -> List[Self]:
        """查找用户的奖券"""

        if status:
            sql = "SELECT * FROM lottery_tickets WHERE customer_id = %s AND status = %s ORDER BY created_at DESC"
            args = [customer_id, status]
        else:
            sql = "SELECT * FROM lottery_tickets WHERE customer_id = %s ORDER BY created_at DESC"
            args = [customer_id]

        return pg.fetchall(cls, sql, args) or []


    @classmethod
    def find_by_transferred_to(cls, customer_id: int) -> List[Self]:
        """查找转让给用户的奖券"""
        sql = "SELECT * FROM lottery_tickets WHERE transferred_to = %s AND status = 'transferred' ORDER BY transferred_at DESC"
        return pg.fetchall(cls, sql, [customer_id]) or []

    def save(self) -> int:
        """保存奖券"""

        if self.id:
            # 更新
            sql = """
                UPDATE lottery_tickets
                SET ticket_code = %s,
                    customer_id = %s,
                    course = %s,
                    discount = %s,
                    base_fare = %s,
                    max_discount = %s,
                    created_at = %s,
                    expires_at = %s,
                    status = %s,
                    transferred_to = %s,
                    transferred_at = %s,
                    used_at = %s,
                    order_id = %s
                WHERE id = %s
            """
            pg.execute(
                sql,
                [
                    self.ticket_code,
                    self.customer_id,
                    self.course,
                    self.discount,
                    self.base_fare,
                    self.max_discount,
                    self.created_at,
                    self.expires_at,
                    self.status,
                    self.transferred_to,
                    self.transferred_at,
                    self.used_at,
                    self.order_id,
                    self.id,
                ],
            )
            return self.id

        # 插入
        sql = """
            INSERT INTO lottery_tickets (
                ticket_code, customer_id, course, discount, base_fare, max_discount,
                created_at, expires_at, status, transferred_to, transferred_at,
                used_at, order_id
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        rowid = pg.fetch_item(
            sql,
            "id",
            [
                self.ticket_code,
                self.customer_id,
                self.course,
                self.discount,
                self.base_fare,
                self.max_discount,
                self.created_at,
                self.expires_at,
                self.status,
                self.transferred_to,
                self.transferred_at,
                self.used_at,
                self.order_id,
            ],
            readonly=False
        )
        self.id = rowid
        assert self.id
        return self.id


    def start_transfer(self) -> bool:
        """开始转让奖券（将状态设置为转让中）"""
        if self.status != "unused":
            logger.error(f"Cannot transfer ticket {self.id}: status is {self.status}")
            return False

        sql = """
            UPDATE lottery_tickets
            SET status = %s
            WHERE id = %s
        """
        try:
            pg.execute(
                sql,
                [
                    "transferring",
                    self.id,
                ],
            )

            # 更新对象的属性以保持一致
            self.status = "transferring"
        except Exception as e:
            logger.error(f"Failed to update lottery ticket status: {e}")
            return False

        return True


    def claim_transfer(self, recipient_id: int) -> bool:
        """认领转让的奖券"""
        if self.status != "transferring":
            logger.error(f"Cannot claim ticket {self.id}: status is {self.status}")
            return False

        if self.customer_id == recipient_id:
            logger.error(f"Cannot transfer ticket {self.id} to self")
            return False

        # 检查接收者是否存在
        recipient = CustomerModel.find_customer(recipient_id)
        if not recipient:
            logger.error(f"Recipient {recipient_id} not found")
            return False

        # 准备更新的时间戳
        transferred_at = datetime.datetime.now()

        try:
            sql = """
                UPDATE lottery_tickets
                SET status = %s,
                    transferred_to = %s,
                    transferred_at = %s
                WHERE id = %s
            """
            pg.execute(
                sql,
                [
                    "transferred",
                    recipient_id,
                    transferred_at,
                    self.id,
                ],
            )

            # 更新对象的属性以保持一致
            self.status = "transferred"
            self.transferred_to = recipient_id
            self.transferred_at = transferred_at

            return True
        except Exception as e:
            logger.error(f"Error claiming ticket {self.id}: {e}")
            return False

    def transfer(self, recipient_id: int) -> bool:
        """转让奖券（直接转让，不经过转让中状态）- 保留此方法以兼容旧代码"""
        if self.status != "unused":
            logger.error(f"Cannot transfer ticket {self.id}: status is {self.status}")
            return False

        if self.customer_id == recipient_id:
            logger.error(f"Cannot transfer ticket {self.id} to self")
            return False

        # 检查接收者是否存在
        recipient = CustomerModel.find_customer(recipient_id)
        if not recipient:
            logger.error(f"Recipient {recipient_id} not found")
            return False

        # 准备更新的时间戳
        transferred_at = datetime.datetime.now()

        try:
            # 只更新必要的字段，避免外键约束问题
            sql = """
                UPDATE lottery_tickets
                SET status = %s,
                    transferred_to = %s,
                    transferred_at = %s
                WHERE id = %s
            """
            pg.execute(
                sql,
                [
                    "transferred",
                    recipient_id,
                    transferred_at,
                    self.id,
                ],
            )

            # 更新对象的属性以保持一致
            self.status = "transferred"
            self.transferred_to = recipient_id
            self.transferred_at = transferred_at

            return True
        except Exception as e:
            logger.error(f"Error transferring ticket {self.id}: {e}")
            return False

    def use(self, order_id: str) -> bool:
        """使用奖券"""
        if self.status != "unused":
            logger.error(f"Cannot use ticket {self.id}: status is {self.status}")
            return False

        if self.expires_at < datetime.datetime.now():
            logger.error(f"Ticket {self.id} has expired")
            return False

        # 准备更新的时间戳
        used_at = datetime.datetime.now()

        try:
            sql = """
                UPDATE lottery_tickets
                SET status = %s,
                    used_at = %s,
                    order_id = %s
                WHERE id = %s
            """
            pg.execute(
                sql,
                [
                    "used",
                    used_at,
                    order_id,
                    self.id,
                ],
            )

            # 更新对象的属性以保持一致
            self.status = "used"
            self.used_at = used_at
            self.order_id = order_id

            return True
        except Exception as e:
            logger.error(f"Error using ticket {self.id}: {e}")
            return False

    def use_for_verification(self, order_id: str) -> bool:
        """核销奖券（管理员功能，支持unused和transferred状态的券）"""
        if self.status not in ["unused", "transferred"]:
            logger.error(f"Cannot verify ticket {self.id}: status is {self.status}")
            return False

        if self.expires_at < datetime.datetime.now():
            logger.error(f"Ticket {self.id} has expired")
            return False

        # 准备更新的时间戳
        used_at = datetime.datetime.now()

        try:
            sql = """
                UPDATE lottery_tickets
                SET status = %s,
                    used_at = %s,
                    order_id = %s
                WHERE id = %s
            """
            pg.execute(
                sql,
                [
                    "used",
                    used_at,
                    order_id,
                    self.id,
                ],
            )

            # 更新对象的属性以保持一致
            self.status = "used"
            self.used_at = used_at
            self.order_id = order_id

            return True
        except Exception as e:
            logger.error(f"Error verifying ticket {self.id}: {e}")
            return False

    def is_expired(self) -> bool:
        """检查奖券是否过期"""
        return self.expires_at < datetime.datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = self.model_dump()
        result["is_expired"] = self.is_expired()
        return result


class LotteryLog(BaseModel):
    """
    抽奖日志模型

    Attributes:
        id: 日志ID
        customer_id: 用户ID
        course: 课程ID
        created_at: 抽奖时间
        result: 抽奖结果
    """

    id: Optional[int] = None
    customer_id: int
    course: str
    created_at: datetime.datetime = datetime.datetime.now()
    result: str

    @classmethod
    def find_by_customer(cls, customer_id: int, limit: int = 10) -> List[Self]:
        """查找用户的抽奖日志"""
        sql = "SELECT * FROM lottery_logs WHERE customer_id = %s ORDER BY created_at DESC LIMIT %s"
        return pg.fetchall(cls, sql, [customer_id, limit]) or []

    @classmethod
    def count_by_customer_month(cls, customer_id: int, course: str) -> int:
        """统计用户本月的抽奖次数"""
        # 获取当前月份的第一天和最后一天
        now = datetime.datetime.now()
        first_day = datetime.datetime(now.year, now.month, 1)
        if now.month == 12:
            last_day = datetime.datetime(now.year + 1, 1, 1) - datetime.timedelta(
                days=1
            )
        else:
            last_day = datetime.datetime(
                now.year, now.month + 1, 1
            ) - datetime.timedelta(days=1)

        sql = """
            SELECT COUNT(*) FROM lottery_logs
            WHERE customer_id = %s AND course = %s AND created_at BETWEEN %s AND %s
        """
        count = pg.fetch_item(sql, "count", [customer_id, course, first_day, last_day])
        return count or 0

    def save(self) -> int:
        """保存抽奖日志"""
        try:
            if self.id:
                # 更新
                sql = """
                    UPDATE lottery_logs
                    SET customer_id = %s,
                        course = %s,
                        created_at = %s,
                        result = %s
                    WHERE id = %s
                """
                pg.execute(
                    sql,
                    [
                        self.customer_id,
                        self.course,
                        self.created_at,
                        self.result,
                        self.id,
                    ],
                )
                return self.id

            # 插入
            sql = """
                INSERT INTO lottery_logs (customer_id, course, created_at, result)
                VALUES (%s, %s, %s, %s)
                RETURNING id
            """
            rowid = pg.fetch_item(
                sql,
                "id",
                [
                    self.customer_id,
                    self.course,
                    self.created_at,
                    self.result,
                ],
                readonly = False
            )
            if rowid is None:
                raise ValueError("Failed to insert lottery log")

            self.id = rowid
            assert self.id is not None
            return self.id
        except Exception as e:
            logger.error(f"Error saving lottery log: {e}")
            raise

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "course": self.course,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "result": self.result,
        }
