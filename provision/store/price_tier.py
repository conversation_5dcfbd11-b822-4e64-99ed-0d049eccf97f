"""
价格档位数据模型
"""
from datetime import datetime
from typing import List, Optional, Self

from loguru import logger
from pydantic import BaseModel

from provision.store.db import pg


class PriceTierModel(BaseModel):
    """价格档位模型"""
    id: Optional[int] = None
    price: float
    wechat_url: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

    @classmethod
    def find_all(cls) -> List[Self]:
        """获取所有价格档位"""
        sql = "SELECT * FROM price_tiers ORDER BY price ASC"
        return pg.fetchall(cls, sql) or []

    @classmethod
    def find_by_id(cls, tier_id: int) -> Optional[Self]:
        """根据ID查找档位"""
        sql = "SELECT * FROM price_tiers WHERE id = %s"
        return pg.fetchone(cls, sql, [tier_id])

    @classmethod
    def find_by_price(cls, price: float) -> Optional[Self]:
        """根据价格查找档位"""
        sql = "SELECT * FROM price_tiers WHERE price = %s"
        return pg.fetchone(cls, sql, [price])
    
    @classmethod
    def find_id_by_price(cls, price: float) -> Optional[int]:
        """根据价格查找档位ID"""
        sql = "SELECT id FROM price_tiers WHERE price = %s"
        return pg.fetch_item(sql, "id", [price])

    @classmethod
    def add(cls, tier: "PriceTierModel") -> dict:
        """添加价格档位"""
        sql = """
            INSERT INTO price_tiers (price, wechat_url)
            VALUES (%s, %s) RETURNING id
        """
        tier_id = pg.fetch_item(
            sql,
            "id",
            [tier.price, tier.wechat_url],
            readonly=False
        )

        if tier_id is None:
            raise ValueError("Failed to insert price tier")

        tier.id = tier_id
        logger.info(f"Added price tier: {tier.price}")
        return {"id": tier.id}

    @classmethod
    def update(cls, tier_id: int, price: float, wechat_url: Optional[str] = None) -> bool:
        """更新价格档位"""
        sql = """
            UPDATE price_tiers
            SET price = %s, wechat_url = %s
            WHERE id = %s
        """
        result = pg.execute(sql, [price, wechat_url, tier_id])
        return result is not None

    @classmethod
    def delete(cls, tier_id: int) -> bool:
        """删除价格档位"""
        sql = "DELETE FROM price_tiers WHERE id = %s"
        result = pg.execute(sql, [tier_id])
        return result is not None

    @classmethod
    def init_default_tiers(cls):
        """初始化默认价格档位"""
        default_prices = [9.9, 19.9, 39.9, 79.9, 99.9, 199.9, 360.0]

        for price in default_prices:
            existing = cls.find_by_price(float(str(price)))
            if not existing:
                tier = PriceTierModel(price=float(str(price)))
                try:
                    cls.add(tier)
                    logger.info(f"Initialized default price tier: {price}")
                except Exception as e:
                    logger.error(f"Failed to initialize price tier {price}: {e}")
