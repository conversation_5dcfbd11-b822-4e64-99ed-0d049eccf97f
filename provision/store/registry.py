import datetime
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger
from pydantic import BaseModel, field_validator

from provision.conf import cfg
from provision.store.db import pg


class RegistryModel(BaseModel):
    """
    课程注册信息模型

    engage -> register -> trial -> prime -> grace -> end

    prime end与termination可能一致
    Attributes:
        course: 课程名称，可选值在plans.yaml中设定
        customer_id: 客户ID
        plan: 订阅套餐，可选值在plans中设定
        start_time: 注册时间，默认为当前时间
        refund_end: 可退款时间，默认为7天
        prime_end: 激活期，完全使用
        retention_end: 对自有容器级别，容器到期后，可保留容器的时间
        expire_time: 课程完全到期时间
    """

    course: str
    customer_id: int
    plan: str
    start_time: datetime.datetime
    refund_end: datetime.datetime
    prime_end: datetime.datetime
    expire_time: datetime.datetime
    is_active: bool = True
    division: str

    # 服务端生成字段
    id: int | None = None
    retention_end: datetime.datetime | None = None

    @field_validator("division")
    @classmethod
    def validate_division(cls, value):
        """验证division"""
        if value not in ["course", "blog"]:
            raise ValueError("Division must be course or blog")
        return value

    @field_validator("course")
    @classmethod
    def validate_course(cls, value):
        """验证课程名称"""
        if not value or (value not in cfg.resources.model_dump()):
            raise ValueError("Course name cannot be empty")

        return value

    @field_validator(
        "start_time", "refund_end", "prime_end", "expire_time", mode="before"
    )
    @classmethod
    def ensure_timezone(cls, value):
        """强制时区为统一时区。

        """
        if isinstance(value, datetime.datetime) and value.tzinfo is None:
            return value.replace(tzinfo=cfg.get_timezone())
        return value

    @classmethod
    @field_validator("plan")
    def validate_plan(cls, value):
        """验证套餐名称"""
        if not value:
            raise ValueError("Plan cannot be empty")

    @classmethod
    def find_registration_by_customer(
        cls,
        customer_id: int,
        division: str,
        include_expired: bool = False,
        include_inactive: bool = False,
    ) -> list["RegistryModel"] | None:
        """返回课程注册信息

        Args:
            customer_id: 客户ID
            division: 资源的门类（course/blog）
            include_expired: 是否包含已过期的课程（默认不包含）
            include_inactive: 是否包含非活跃的课程（默认不包含）

        Returns:
            符合条件的课程注册信息列表
        """
        sql = "SELECT * FROM registry WHERE customer_id = %s and division = %s"
        args = [customer_id, division]

        # 不包含已过期的课程
        if not include_expired:
            sql += " AND expire_time > CURRENT_TIMESTAMP"

        # 不包含非活跃的课程
        if not include_inactive:
            sql += " AND is_active = TRUE"

        return pg.fetchall(RegistryModel, sql, args)

    @classmethod
    def find_subscription_by_customer_course(
        cls, customer_id: int, course: str, active: bool | None = True
    ) -> Optional["RegistryModel"]:
        """一名用户+一门课只能注册一次"""
        sql = "SELECT * FROM registry WHERE customer_id = %s AND course = %s"
        args = [customer_id, course]
        if active is not None:
            sql += " AND is_active = %s"
            args.append(active)

        result = pg.fetchone(cls, sql, args)

        return result

    @classmethod
    def find_reservation_by_account_course(cls, account: str, course: str)->"RegistryModel|None":
        """返回课程注册信息(所有的division)"""
        sql = """
            SELECT plan, start_time FROM registry
            WHERE customer_id = (SELECT id FROM customer WHERE account = %s)
            AND course = %s
        """
        return pg.fetchone(cls, sql, [account, course])

    @classmethod
    def get_registration_basic(cls, registration_id: int)->"RegistryModel|None":
        """返回课程注册信息（基本版本）"""
        sql = "SELECT * FROM registry WHERE id = %s"
        result = pg.fetchone(cls, sql, [registration_id])

        return result

    @classmethod
    def add(cls, registration: "RegistryModel")->dict:
        """插入课程注册信息"""
        sql = """
            INSERT INTO registry (course, customer_id, plan, start_time, refund_end, prime_end, retention_end, expire_time, division)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) returning id
        """
        rowid = pg.fetch_item(
            sql,
            "id",
            [
                registration.course,
                registration.customer_id,
                registration.plan,
                registration.start_time,
                registration.refund_end,
                registration.prime_end,
                registration.retention_end,
                registration.expire_time,
                registration.division,
            ],
            readonly=False
        )

        if rowid is None:
            raise ValueError("Failed to insert registration")

        registration.id = rowid
        logger.info(f"{registration.customer_id} has registered {registration.id}")
        return {"id": registration.id}

    def update_times(self, registration_id: int):
        """更新课程注册信息的时间字段"""
        sql = """
            UPDATE registry
            SET start_time = %s, refund_end = %s, prime_end = %s, expire_time = %s
            WHERE id = %s
        """
        pg.execute(
            sql,
            [
                self.start_time,
                self.refund_end,
                self.prime_end,
                self.expire_time,
                registration_id,
            ]
        )

    def delete(self):
        """删除课程注册信息"""
        sql = "UPDATE registry SET is_active = FALSE WHERE id = %s"
        pg.execute(sql, [self.id])

    def update(self, registration_id: int):
        """更新课程注册信息"""
        sql = """
            UPDATE registry SET
                plan = %s,
                start_time = %s,
                refund_end = %s,
                prime_end = %s,
                retention_end = %s,
                expire_time = %s,
                is_active = %s,
                division = %s
            WHERE id = %s
        """
        pg.execute(
            sql,
            [
                self.plan,
                self.start_time,
                self.refund_end,
                self.prime_end,
                self.retention_end,
                self.expire_time,
                self.is_active,
                self.division,
                registration_id,
            ],
        )
        return True

    @classmethod
    def get_registration(cls, registration_id: int) -> Optional["RegistryModel"]:
        """根据ID获取课程注册信息"""
        sql = "SELECT * FROM registry WHERE id = %s"
        return pg.fetchone(cls, sql, [registration_id])

    @classmethod
    def find_all_registrations(
        cls,
        page: int = 1,
        page_size: int = 20,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
        course_filter: Optional[str] = None,
        plan_filter: Optional[str] = None,
        include_inactive: bool = True,
        is_active: Optional[bool] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有课程注册记录

        Args:
            page: 页码（从1开始）
            page_size: 每页记录数
            sort_by: 排序字段
            sort_order: 排序方向（"asc"或"desc"）
            course_filter: 课程筛选
            plan_filter: 套餐筛选
            include_inactive: 是否包含非活跃的课程（默认包含）
            is_active: 指定活跃状态（True或False），如果为None则不筛选

        Returns:
            课程注册记录列表和总记录数
        """
        # 构建基本查询
        sql = "SELECT r.*, c.account, c.nickname FROM registry r JOIN customers c ON r.customer_id = c.id WHERE 1=1"
        count_sql = "SELECT COUNT(*) FROM registry r WHERE 1=1"
        args: List[Any] = []
        count_args: List[Any] = []

        # 添加课程筛选
        if course_filter:
            sql += " AND r.course = %s"
            count_sql += " AND r.course = %s"
            args.append(course_filter)
            count_args.append(course_filter)

        # 添加套餐筛选
        if plan_filter:
            sql += " AND r.plan = %s"
            count_sql += " AND r.plan = %s"
            args.append(plan_filter)
            count_args.append(plan_filter)

        # 是否包含非活跃的课程
        if not include_inactive:
            sql += " AND r.is_active = TRUE"
            count_sql += " AND r.is_active = TRUE"

        # 指定活跃状态
        if is_active is not None:
            sql += " AND r.is_active = %s"
            count_sql += " AND r.is_active = %s"
            args.append(is_active)
            count_args.append(is_active)

        # 添加排序
        if sort_by and sort_by not in ["password", "id", "course_id"]:
            valid_fields = [
                "course",
                "plan",
                "start_time",
                "refund_end",
                "prime_end",
                "retention_end",
                "expire_time",
                "is_active",
                "customer_id",
                "account",
                "nickname",
            ]
            if sort_by in valid_fields:
                sort_direction = "ASC" if sort_order.lower() == "asc" else "DESC"
                # 如果排序字段是客户表的字段，需要添加表前缀
                if sort_by in ["account", "nickname"]:
                    sql += f" ORDER BY c.{sort_by} {sort_direction}"
                else:
                    sql += f" ORDER BY r.{sort_by} {sort_direction}"
        else:
            # 默认按ID排序
            sql += " ORDER BY r.id ASC"

        # 获取总记录数
        total_count = pg.fetch_item(count_sql, "count", count_args) or 0

        # 添加分页
        offset = (page - 1) * page_size
        sql += " LIMIT %s OFFSET %s"
        args.extend([page_size, offset])

        registrations = pg.fetch_records(sql, args, return_dict=True) or []

        return registrations, total_count

    @classmethod
    def find_active_registries(cls) -> List["RegistryModel"]:
        """获取所有有效的课程注册记录"""
        sql = """
            SELECT * FROM registry
            WHERE is_active = TRUE
            AND expire_time > CURRENT_TIMESTAMP
        """
        result = pg.fetchall(cls, sql)
        if result is None:
            return []
        return list(result)
