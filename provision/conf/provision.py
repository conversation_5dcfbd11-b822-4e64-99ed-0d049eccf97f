# generated by datamodel-codegen:
#   filename:  provision.yaml

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from provision.common.custom_base_model import CustomBaseModel


class Handler(CustomBaseModel):
    sink: str
    level: str
    format: str
    rotation: str


class Logging(CustomBaseModel):
    handlers: List[Handler]


class Server(CustomBaseModel):
    port: int
    host: str
    secret: str
    domain: str
    url_prefix: str
    logging: Logging


class Accounts(CustomBaseModel):
    preview: str
    share: str


class Pool(CustomBaseModel):
    min_size: int
    max_size: int


class Postgres(CustomBaseModel):
    pool: Pool
    user: str
    password: str
    host: str
    database: str


class Port(CustomBaseModel):
    field_8080: Optional[int] = Field(None, alias='8080')
    field_8443: Optional[int] = Field(None, alias='8443')
    field_8403: Optional[int] = Field(None, alias='8403')


class Nginx(CustomBaseModel):
    ports: List[Port]
    name: str
    network: str


class Jupyterlab(CustomBaseModel):
    prefix: List[str]


class User(CustomBaseModel):
    username: str
    password: str


class Admin(CustomBaseModel):
    users: List[User]


class Sms(CustomBaseModel):
    access_key_id: str
    access_key_secret: str
    sign_name: str
    template_code: str
    endpoint: str


class Model(CustomBaseModel):
    server: Server
    accounts: Accounts
    postgres: Postgres
    nginx: Nginx
    site: str
    jupyterlab: Jupyterlab
    admin: Admin
    sms: Sms
    api_token: str
