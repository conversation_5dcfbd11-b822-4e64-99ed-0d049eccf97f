user  nginx;
worker_processes auto;

pid   /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                        '"$request" $status $body_bytes_sent '
                        '"$http_referer" "$http_user_agent" '
                        'auth_status:$upstream_status '       # 认证子请求状态码
                        'ups_addr:$upstream_addr '            # 上游服务器地址
                        'req_time:$request_time '             # 请求处理总时间
                        'ups_resp_time:$upstream_response_time'; # 上游响应时间

    include /etc/nginx/mime.types;
    default_type  application/octet-stream;

    upstream auth_backend {
        # this works on ubuntu
        server **********:403;
        # this works on windows/macos
        server host.docker.internal:403;
    }

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    types_hash_max_size 1024;

    proxy_connect_timeout 300;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
    send_timeout 300;
    client_max_body_size 0;
    client_body_buffer_size 50M;
    gzip  on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 2;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # server {
    #     listen [::]:443 ssl ipv6only=off;
    #     server_name default;
    #     ssl_certificate /etc/letsencrypt/live/ke.quantide.cn/fullchain.pem;
    #     ssl_certificate_key /etc/letsencrypt/live/ke.quantide.cn/privkey.pem;
    #     if ($scheme != "https") {
    #         return 444;  # 444 表示关闭连接
    #     }

    #     access_log /var/log/nginx/ke.log;
    #     include proxy_params;
    #     proxy_redirect off;
    #     include users/*.conf;
    #     include auth.conf;

    #     location / {
    #         return 302 https://www.jieyu.ai/#用户注册;
    #     }

    #     location @router {
    #         rewrite ^.*$ /index.html last;
    #     }
    # }

    # 主服务器
    server {
        rewrite_log on;
        listen 80;
        server_name default;

        # 禁用自动添加尾部斜杠
        absolute_redirect off;
        # 禁用目录自动添加尾部斜杠
        server_name_in_redirect off;
        port_in_redirect off;

        error_log /var/log/nginx/error.log error;
        access_log /var/log/nginx/access.log;
        include proxy_params;
        proxy_redirect off;

        # 其他路径应用认证规则
        include auth.conf;

        location ^~ /api/ {
            proxy_pass http://auth_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /academy/markdown/ {
            auth_request off;
            alias /var/html/www/markdown/;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
            expires 30d;
        }

        # 处理 /academy/ 路径下的请求，转发到后端
        location ^~ /academy/ {
            proxy_pass http://auth_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        location ^~ /assets/ {
            auth_request off;
            alias /var/html/www/assets/;
            add_header Cache-Control "public, max-age=31536000";
            expires 30d;
        }

        # 处理bulletin.json文件
        location = /bulletin.json {
            auth_request off;
            root /var/html/www;
            add_header Content-Type "application/json";
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }

        # 处理根路径请求，显示宣传页面
        location = / {
            auth_request off;
            root /var/html/www;
            try_files /index.html =404;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }
    }

    # 管理后台服务器配置，监听8403端口
    server {
        rewrite_log on;
        listen 8403;
        server_name default;

        # 禁用自动添加尾部斜杠
        absolute_redirect off;
        # 禁用目录自动添加尾部斜杠
        server_name_in_redirect off;
        port_in_redirect off;

        error_log /var/log/nginx/admin_error.log error;
        access_log /var/log/nginx/admin_access.log detailed;
        include proxy_params;
        proxy_redirect off;

        # 处理API请求，转发到后端服务
        location ^~ /api/ {
            proxy_pass http://auth_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        }

        # 处理admin API请求，转发到后端服务
        location ^~ /admin/api/ {
            proxy_pass http://auth_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 处理admin/assets静态资源请求
        location ^~ /admin/assets/ {
            alias /var/html/www/admin/assets/;
            add_header Cache-Control "public, max-age=31536000";
            expires 30d;
        }

        # 处理静态资源请求
        location / {
            root /var/html/www/admin;
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }
    }
}
