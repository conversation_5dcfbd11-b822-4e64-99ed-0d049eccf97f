resolver 127.0.0.11 valid=10s;

location ~ ^/course/([^/]+)/([^/]+)/.*\.ipynb$ {
    set $courseid $1;
    set $name $2;
    set $upstream_host "course_${courseid}_${name}";
    set $query_args "";

    proxy_pass http://$upstream_host:8888$request_uri;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Authorization "token Qu@ntide2024";
    proxy_set_header Cookie $http_cookie;
    proxy_pass_request_headers on;

    auth_request /auth;
    error_page 401 403 415 = @unauthorized;
}

# 处理其他非 ipynb 请求（无需认证）
location ~ ^/course/([^/]+)/([^/]+)/(.*)$ {
    set $courseid $1;
    set $name $2;
    set $upstream_host "course_${courseid}_${name}";

    auth_request off;
    proxy_pass http://$upstream_host:8888$request_uri;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Authorization "token Qu@ntide2024";
    proxy_set_header Cookie $http_cookie;
    proxy_pass_request_headers on;
}

location @unauthorized{
    # 如果是 API 请求（路径包含 /api/），直接返回 JSON
    if ($request_uri ~* "/api/") {
        add_header Content-Type "application/json; charset=utf-8";
        return 401 '{"error": "access_denied", "message": "您没有权限访问此资源，请等待解锁或者升级套餐。详情请咨询客服。"}';
    }

    # 先设置原始 URL cookie，然后重定向
    add_header Set-Cookie "ORIGINURL=$scheme://$http_host$request_uri;Path=/;Domain=quantide.cn;Max-Age=300";
    return 302 /academy;
}

location /sso/customer/login {
    auth_request off;
    proxy_pass http://auth_backend/sso/customer/login;

    # 确保 cookie 正确传递
    proxy_cookie_path / /;
    proxy_cookie_domain auth_backend $host;
    proxy_pass_header Set-Cookie;
}

location /sso/ {
    proxy_pass http://auth_backend/sso/;
    auth_request off;

    # 确保 cookie 正确传递
    proxy_cookie_path / /;
    proxy_cookie_domain auth_backend $host;
    proxy_pass_header Set-Cookie;
}

location /auth {
    internal;
    auth_request off;
    proxy_pass http://auth_backend/sso/customer/auth;
    proxy_set_body off;
    proxy_set_header Content-Length "";
    proxy_set_header X-Original-URI $request_uri;
}
