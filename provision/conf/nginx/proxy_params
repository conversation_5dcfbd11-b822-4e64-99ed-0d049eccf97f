#proxy_set_header Host $host;
proxy_set_header Host $http_host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto http_x_forwarded_proto;

proxy_read_timeout 30s;
proxy_connect_timeout 30s;

proxy_headers_hash_max_size 1024;
proxy_headers_hash_bucket_size 128;
proxy_http_version 1.1;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection "Upgrade";
proxy_buffering on;

# 确保 cookie 正确传递
proxy_cookie_path / /;
proxy_pass_header Set-Cookie;
