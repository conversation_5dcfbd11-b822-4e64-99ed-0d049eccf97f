# generated by datamodel-codegen:
#   filename:  resources.yaml

from __future__ import annotations

from typing import List

from provision.common.custom_base_model import CustomBaseModel


class Lottery(CustomBaseModel):
    times: int
    min: int
    max: int
    base_fare: int
    max_discount: int
    expire: int


class L24(CustomBaseModel):
    title: str
    cover: str
    desc: str
    division: str
    resources: List[str]
    home: str
    level: str
    video_length: str
    buy_link: str
    price: str
    detail: str
    lottery: Lottery


class Fa(L24):
    pass


class Pandas(CustomBaseModel):
    title: str
    cover: str
    desc: str
    division: str
    resources: List[str]
    home: str
    level: str
    buy_link: str
    price: str
    detail: str
    lottery: Lottery


class Blog(CustomBaseModel):
    title: str
    cover: str
    desc: str
    division: str
    resources: List[str]
    home: str
    buy_link: str
    price: str
    lottery: Lottery


class Model(CustomBaseModel):
    l24: L24
    fa: Fa
    pandas: Pandas
    blog: Blog
