import datetime
import functools
import logging
import os
import pickle
import random
import socket
import time
import warnings
from collections import defaultdict
from pathlib import Path
from typing import Any, Callable, List, Optional, Sequence, Tuple, Union

import arrow
import matplotlib as mpl
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import polars as pl
import seaborn as sns
import talib as ta
from alphalens.performance import factor_alpha_beta
from alphalens.plotting import plot_quantile_statistics_table
from alphalens.tears import create_full_tear_sheet, create_returns_tear_sheet
from alphalens.utils import get_clean_factor_and_forward_returns
from bottleneck import move_mean
from numpy._typing import NDArray
from plotly.subplots import make_subplots

mpl.rcdefaults()
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', '文泉驿微米黑']
plt.rcParams['axes.unicode_minus'] = False

sns.set_theme(font="WenQuanYi Micro Hei")


logger = logging.getLogger("quantide")
data_home = Path("/data")

warnings.filterwarnings("ignore")

class TushareProProxy:
    """Proxy class that forwards all method calls to the server"""

    def __init__(self, host='localhost', port=5290, timeout=30, max_retries=3, retry_delay=1):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._socket = None
        self._connected = False

    def _connect(self):
        """Establish connection to server"""
        if self._connected:
            return

        try:
            self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._socket.settimeout(self.timeout)
            self._socket.connect((self.host, self.port))
            self._connected = True
            logger.info(f"Connected to server {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to connect to server: {e}")
            raise ConnectionError(f"Cannot connect to tushare proxy server at {self.host}:{self.port}")

    def _disconnect(self):
        """Close connection to server"""
        if self._socket:
            try:
                self._socket.close()
            except:
                pass
            self._socket = None
        self._connected = False

    def _send_request(self, method_name, *args, **kwargs):
        """Send request to server and return response with retry mechanism"""
        request = {
            'method': method_name,
            'args': args,
            'kwargs': kwargs
        }

        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                self._connect()

                # Send request
                request_data = pickle.dumps(request)
                self._send_data(request_data)

                # Receive response
                response_data = self._receive_data()
                response = pickle.loads(response_data)

                if response['success']:
                    return response['result']
                else:
                    error_msg = response.get('error', 'Unknown error')
                    error_type = response.get('type', 'unknown')
                    raise RuntimeError(f"Server error ({error_type}): {error_msg}")

            except (ConnectionError, ConnectionRefusedError, OSError, socket.error) as e:
                last_exception = e
                self._disconnect()

                if attempt < self.max_retries:
                    logger.warning(f"Connection failed (attempt {attempt + 1}/{self.max_retries + 1}): {e}")
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"All {self.max_retries + 1} connection attempts failed")
                    raise
            except Exception as e:
                self._disconnect()
                raise

        # This should never be reached, but just in case
        if last_exception:
            raise last_exception

    def _send_data(self, data):
        """Send data with length prefix"""
        try:
            # Send length first (4 bytes)
            length = len(data)
            self._socket.sendall(length.to_bytes(4, byteorder='big'))

            # Then send the actual data
            self._socket.sendall(data)
        except Exception as e:
            logger.error(f"Error sending data: {e}")
            raise

    def _receive_data(self):
        """Receive data with length prefix"""
        try:
            # First, receive the length of the data (4 bytes)
            length_data = self._socket.recv(4)
            if len(length_data) < 4:
                raise ConnectionError("Connection closed by server")

            data_length = int.from_bytes(length_data, byteorder='big')

            # Then receive the actual data
            data = b''
            while len(data) < data_length:
                chunk = self._socket.recv(data_length - len(data))
                if not chunk:
                    raise ConnectionError("Connection closed by server")
                data += chunk

            return data
        except Exception as e:
            logger.error(f"Error receiving data: {e}")
            raise

    def __getattr__(self, name):
        """Forward all attribute access to the server"""
        def method_proxy(*args, **kwargs):
            return self._send_request(name, *args, **kwargs)

        return method_proxy

    def __del__(self):
        """Clean up connection when object is destroyed"""
        self._disconnect()

def code_tushare2jq(asset: str) -> str:
    """将tushare的代码转换为jq的代码

    Args:
        asset (str): tushare代码，比如000001.SH

    Returns:
        str: jq风格的代码，比如000001.XSHE
    """
    x, y = asset.split(".")
    map_ = {"SH": "XSHG", "SZ": "XSHE", "BJ": "BJ"}
    return f"{x}.{map_.get(y.upper())}"

def code_jq2tushare(asset: str) -> str:
    """将jq的代码转换为tushare的代码

    Args:
        asset (str): jq的代码，比如000001.XSHE

    Returns:
        str: tushare的代码，比如000001.SH
    """
    x, y = asset.split(".")
    map_ = {"XSHG": "SH", "XSHE": "SZ", "BJ": "BJ"}
    return f"{x}.{map_.get(y.upper())}"


def date2int(date: datetime.date):
    """将日期转换为形如20231229的整数"""
    return date.year * 10000 + date.month * 100 + date.day


def int2date(date: int):
    """将形如20231229的整数转换为日期"""
    return datetime.date(date // 10000, date % 10000 // 100, date % 100)


def load_bars(
    start: datetime.date|datetime.datetime,
    end: datetime.date|datetime.datetime,
    universe: Tuple[str]|int = 500,
    seed: int|None = 78
) -> pd.DataFrame | None:
    """获取指定时间区间内的行情数据

    universe可以为字符串，也可以为整数。当为整数时，表示随机抽取universe支股票；当为字符串数组时，注意后缀为.XSHE/.XSHG

    Args:
        start: 起始日期，必须大于2005-01-04
        end: 结束日期，必须小于等于2023-12-29
        universe: 可以是list of string, 也可以是整数。当为整数时，随机抽取。
    Returns:
        以frame, asset为双重索引，OHLC等为列的DataFrame
    """
    if seed is not None:
        np.random.seed(seed)
        
    assert (
        isinstance(start, datetime.date)
        and isinstance(end, datetime.date)
        and not isinstance(start, datetime.datetime)
        and not isinstance(end, datetime.datetime)
    ), "start/end 必须为datetime.date类型"
    if start >= datetime.date(2005, 1, 1) and end <= datetime.date(2023, 12, 31):
        return _load_day_bars_v2(start, end, universe)
    else:
        print("暂不支持该时间区间")


def _ensure_payh(all_symbols, universe):
    universe_ = np.random.choice(all_symbols, 
                                 size = min(universe, len(all_symbols)), 
                                 replace=False)

    if "000001.XSHE" in universe_:
        pos = np.where(universe_ == "000001.XSHE")[0][0]
        swp = universe_[0]
        universe_[0], universe_[pos] = "000001.XSHE", swp
    else:
        universe_[0] = "000001.XSHE"

    return universe_

def _load_day_bars(
    start: datetime.date, end: datetime.date, universe: Union[NDArray, int] = 500
):
    """timeit result（universe = 2000）: 3.71秒，要比dataframe筛选更快。dataframe中按isin进行symbol筛选太慢，导致最终耗时多5倍(18秒)"""
    barss = None

    assert (
        isinstance(start, datetime.date)
        and isinstance(end, datetime.date)
        and not isinstance(start, datetime.datetime)
        and not isinstance(end, datetime.datetime)
    ), "start/end 必须为datetime.date类型"

    if end > datetime.date(2023, 12, 31) or end < datetime.date(2005, 1, 1):
        raise ValueError("Allow time range: 2005-01-04 ~ 2023-12-29")

    with open(data_home / "ro / bars_1d_2005_2023.pkl", "rb") as f:
        barss = pickle.load(f)

    keys = list(barss.keys())
    if isinstance(universe, int):
        if universe == -1:
            selected_keys = keys
        else:
            selected_keys = _ensure_payh(keys, universe)
    else:
        selected_keys = universe

    dfs = []
    for symbol in selected_keys:
        qry = "frame >= @start & frame <= @end"
        df = pd.DataFrame(barss[symbol]).assign(asset=symbol).query(qry)

        if len(df) == 0:
            logger.debug("no bars for %s from %s to %s", symbol, start, end)
            continue
        # 前复权
        last = df.iloc[-1]["factor"]
        adjust_factor = df["factor"] / last
        adjust = ["open", "high", "low", "close", "volume"]
        df.loc[:, adjust] = df.loc[:, adjust].multiply(adjust_factor, axis="index")

        dfs.append(df)

    df = pd.concat(dfs, ignore_index=True)
    df.set_index(["frame", "asset"], inplace=True)
    df.index.names = ["date", "asset"]
    df.drop("factor", axis=1, inplace=True)
    df["price"] = df["open"].shift(-1)
    return df


def load_sectors():
    """获取分类数据。返回一个dataframe, index为asset, columns为sector,其值为对应的分类"""
    cache = data_home / "rw/sectors.pkl"
    try:
        with open(cache, "rb") as f:
            return pickle.load(f)
    except Exception:  # pylint: disable=broad-exception-caught
        pass

    # 获取申万一级行业分类
    sw_classify = pro_api().index_classify(level="L1", src="SW2021")
    sw_classify.set_index("index_code", inplace=True)
    dfs = []
    for code in sw_classify.index:
        df = pro_api().index_member(index_code=code)
        df["index_name"] = sw_classify.loc[code, "industry_name"]
        dfs.append(df)

    sectors = pd.concat(dfs)
    sectors.rename(columns={"con_code": "asset", "index_name": "sector"}, inplace=True)
    sectors_sorted = sectors.sort_values(by="in_date", ascending=True)

    # 去重，保留每个重复组中的最后一个记录（即 in_date 最大的记录）
    sectors = sectors_sorted.drop_duplicates(subset=["asset"], keep="last")

    sectors["asset"] = sectors.asset.apply(code_tushare2jq)
    sectors.set_index("asset", inplace=True)

    with open(cache, "wb") as f:
        pickle.dump(sectors["sector"], f)

    return sectors["sector"]


def fetch_daily_basic(date: datetime.date) -> pd.DataFrame:
    """
    获取指定日期的所有股票的基本面信息

    Args:
        date (datetime.date): 日期
    Returns:
        asset为索引的dataframe
    """
    year = date.year
    month = date.month
    df = None
    folder = data_home / f"rw/tushare_daily_basic/{year}/{month}"
    file = os.path.join(folder, f"{date}.pkl")
    try:
        with open(file, "rb") as f:
            df = pickle.load(f)
            return df
    except Exception:  # pylint: disable=broad-exception-caught
        pass

    df = pro_api().daily_basic(ts_code="", trade_date=str(date2int(date)))
    df = df.rename(columns={"ts_code": "asset"})

    df.asset = df.asset.apply(code_tushare2jq)
    df.set_index("asset", inplace=True)

    os.makedirs(folder, exist_ok=True)
    try:
        with open(file, "wb") as f:
            pickle.dump(df, f)
    except Exception:  # pylint: disable=broad-exception-caught
        pass
    return df


def fetch_valuation(date: datetime.date):
    """
    获取指定日期的股票市值
    """
    df = fetch_daily_basic(date)
    return df["circ_mv"]


def get_daily_basic(
    fields: List[str], start: datetime.date, end: datetime.date, universe: List[str]
) -> pd.DataFrame:
    """获取指定字段值。

    允许的字段有：close, turnover(自由流通股)、volume_ratio, pe(市盈率), pe_ttm,pb(市净率),ps, ps_ttm, dv_ratio, dv_ttm, mv(circ_mv，即流通市值)。未提供注释的，请看tushare文档。
    """
    dfs = []
    days = pd.bdate_range(start, end)
    for day in days:
        df = fetch_daily_basic(day)
        if len(df) == 0:
            continue
        df["date"] = day
        dfs.append(df)

    df = pd.concat(dfs)
    df = df[df.index.isin(universe)]

    df.rename(columns={"turnover_rate_f": "turnover", "circ_mv": "mv"}, inplace=True)

    df.set_index(["date", df.index], inplace=True)
    return df[fields]


def alphatest(
    universe: int | Tuple[str],
    start: datetime.date,
    end: datetime.date,
    calc_factor: Callable[[NDArray, Tuple[Any, ...]], Any],
    args=(),
    bins=None,
    top=-1,
    bottom=-1,
    plot_mode="returns",
    **kwargs,
):
    """因子检验封装函数

    Args:
        universe: 样本数量，或者指定的资产池
        start: 开始日期
        end: 结束日期
        calc_factor: 计算因子的函数
        args: 计算因子的函数的参数
        bins: number of cut by bins, or cut bin edges
        top: top 分层
        bottom: bottom 分层
        plot_mode: 绘图模型。
            'full' -> create_full_tear_sheet,
            'returns' -> create_returns_tear_sheet,
            'quantiles' -> plot_quantile_statistics_table,
            'none' -> 不绘图，只返回回测结果。
    kwargs:
        传递给 get_clean_factor_and_forward_returns 的参数
    """
    # 加载数据
    barss = load_bars(start, end, universe)
    if barss is None:
        raise ValueError("No data loaded")

    # 计算因子
    factors = (
        barss.groupby(level="asset")
        .apply(lambda x: calc_factor(x, *args))
        .droplevel(level=0)
    )

    # 提取价格数据
    prices = barss["price"].unstack(level=1)

    long_short = kwargs.pop("long_short", True)

    # 预处理
    if bins is not None:
        factor_data = get_clean_factor_and_forward_returns(
            factors, prices, quantiles=None, bins=bins, **kwargs
        )
    else:
        if "quantiles" in kwargs:
            quantiles = kwargs.pop("quantiles")
        else:
            quantiles = 10
        factor_data = get_clean_factor_and_forward_returns(
            factors, prices, quantiles=quantiles, **kwargs
        )

    if top != -1:
        factor_data = factor_data[factor_data.factor_quantile <= top]
    if bottom != -1:
        factor_data = factor_data[factor_data.factor_quantile >= bottom]

    alpha = factor_alpha_beta(factor_data, demeaned=long_short)

    if plot_mode == "quantiles":
        plot_quantile_statistics_table(factor_data)
    elif plot_mode == "returns":
        create_returns_tear_sheet(factor_data, long_short=long_short)
    elif plot_mode == "full":
        create_full_tear_sheet(factor_data, long_short=long_short)
    else:
        pass
    return alpha, factor_data


def moving_average(ts: Sequence, win: int, padding=True) -> np.ndarray:
    """生成ts序列的移动平均值

    Examples:

        >>> ts = np.arange(7)
        >>> moving_average(ts, 5)
        array([nan, nan, nan, nan,  2.,  3.,  4.])

    Args:
        ts (Sequence): the input array
        win (int): the window size
        padding: if True, then the return will be equal length as input, padding with np.NaN at the beginning

    Returns:
        The moving mean of the input array along the specified axis. The output has the same shape as the input.
    """
    ma = move_mean(ts, win)
    if padding:
        return ma
    else:
        return ma[win - 1 :]


class Candlestick:
    """K线图对象"""

    RED = "#FF4136"
    GREEN = "#3DAA70"
    TRANSPARENT = "rgba(0,0,0,0)"
    LIGHT_GRAY = "rgba(0, 0, 0, 0.1)"
    MA_COLORS = {
        5: "#1432F5",
        10: "#EB52F7",
        20: "#C0C0C0",
        30: "#882111",
        60: "#5E8E28",
        120: "#4294F7",
        250: "#F09937",
    }

    def __init__(
        self,
        bars: np.ndarray,
        ma_groups: List[int] | None = None,
        title: str = "",
        show_volume=True,
        show_rsi=True,
        width=None,
        height=None,
        **kwargs,
    ):
        """构造函数

        Args:
            bars: 行情数据，需要有date, open, high, low, close, volume字段
            ma_groups: 均线组参数。比如[5, 10, 20]表明向k线图中添加5, 10, 20日均线。如果不提供，将从数组[5, 10, 20, 30, 60, 120, 250]中取直到与`len(bars) - 5`匹配的参数为止。比如bars长度为30，则将取[5, 10, 20]来绘制均线。
            title: k线图的标题
            show_volume: 是否显示成交量图
            show_rsi: 是否显示RSI图。缺省显示参数为6的RSI图。
            width: the width in 'px' units of the figure
            height: the height in 'px' units of the figure
        Keyword Args:
            rsi_win int: default is 6
        """
        self.title = title
        self.bars = bars
        self.width = width
        self.height = height

        # traces for main area
        self.main_traces = {}

        # traces for indicator area
        self.ind_traces = {}

        self.ticks = self._format_tick(bars["date"])
        self._bar_close = np.around(bars["close"], 2).astype(np.float64)

        # for every candlestick, it must contain a candlestick plot
        cs = go.Candlestick(
            x=self.ticks,
            open=bars["open"],
            high=bars["high"],
            low=bars["low"],
            close=self._bar_close,
            line=dict({"width": 1}),
            name="K线",
            **kwargs,
        )

        # Set line and fill colors
        cs.increasing.fillcolor = "rgba(255,255,255,0.9)"
        cs.increasing.line.color = self.RED
        cs.decreasing.fillcolor = self.GREEN
        cs.decreasing.line.color = self.GREEN

        self.main_traces["ohlc"] = cs

        if show_volume:
            self.add_indicator("volume")

        if show_rsi:
            self.add_indicator("rsi", win=kwargs.get("rsi_win", 6))

        # 增加均线
        if ma_groups is None:
            ma_groups = []

        for win in ma_groups:
            name = f"ma{win}"
            if win > len(bars):
                continue
            ma = moving_average(self._bar_close, win)
            line = go.Scatter(
                y=ma,
                x=self.ticks,
                name=name,
                line=dict(width=1, color=self.MA_COLORS.get(win)),
            )
            self.main_traces[name] = line

    @property
    def figure(self):
        """返回一个figure对象"""
        rows = len(self.ind_traces) + 1
        specs = [[{"secondary_y": False}]] * rows
        specs[0][0]["secondary_y"] = True

        if rows == 1:
            row_heights = [1]
        else:
            row_heights = [0.7, *([0.3 / (rows - 1)] * (rows - 1))]
        cols = 1

        fig = make_subplots(
            rows=rows,
            cols=cols,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=(self.title, *self.ind_traces.keys()),
            row_heights=row_heights,
            specs=specs,
        )

        for _, trace in self.main_traces.items():
            fig.add_trace(trace, row=1, col=1)

        for i, (_, trace) in enumerate(self.ind_traces.items()):
            fig.add_trace(trace, row=i + 2, col=1)

        ymin = np.min(self.bars["low"])
        ymax = np.max(self.bars["high"])

        ylim = [ymin * 0.95, ymax * 1.05]

        # 显示十字光标
        fig.update_xaxes(
            showgrid=False,
            showspikes=True,
            spikemode="across",
            spikesnap="cursor",
            spikecolor="grey",
            spikedash="solid",
            spikethickness=1,
        )

        fig.update_yaxes(
            showspikes=True,
            spikemode="across",
            spikesnap="cursor",
            spikedash="solid",
            spikecolor="grey",
            spikethickness=1,
            showgrid=True,
            gridcolor=self.LIGHT_GRAY,
        )

        fig.update_xaxes(
            nticks=len(self.bars) // 10,
            ticklen=10,
            ticks="outside",
            minor=dict(nticks=5, ticklen=5, ticks="outside"),
            row=rows,
            col=1,
        )

        # 设置K线显示区域
        if self.width:
            win_size = int(self.width // 10)
        else:
            win_size = 120

        fig.update_xaxes(
            type="category", range=[len(self.bars) - win_size, len(self.bars) - 1]
        )

        fig.update_layout(
            yaxis=dict(range=ylim),
            hovermode="x unified",
            plot_bgcolor=self.TRANSPARENT,
            xaxis_rangeslider_visible=False,
            xaxis_tickangle=45
        )

        if self.width:
            fig.update_layout(width=self.width)

        if self.height:
            fig.update_layout(height=self.height)

        return fig

    def _format_tick(self, tm: NDArray) -> NDArray:
        tm_ = pd.to_datetime(tm)
        if tm_[0].hour == 0:  # assume it's date
            return np.array([f"{x.year:02}-{x.month:02}-{x.day:02}" for x in tm_])

        return np.array(
            [f"{x.month:02}-{x.day:02} {x.hour:02}:{x.minute:02}" for x in tm_]
        )

    def _remove_ma(self):
        traces = {}
        for name in self.main_traces:
            if not name.startswith("ma"):
                traces[name] = self.main_traces[name]

        self.main_traces = traces

    def add_line(self, trace_name: str, x: List[int], y: List[float]):
        """在k线图上增加以`x`,`y`表示的一条直线

        Args:
            trace_name : 图例名称
            x : x轴坐标，所有的x值都必须属于[0, len(self.bars)]
            y : y值
        """
        line = go.Scatter(x=self.ticks[x], y=y, mode="lines", name=trace_name)

        self.main_traces[trace_name] = line

    def add_indicator(self, indicator: str, **kwargs):
        """向k线图中增加技术指标

        Args:
            indicator: 当前支持值有'volume', 'rsi', 'bbands'
            kwargs: 计算某个indicator时，需要的参数。比如计算bbands时，需要传入均线的window
        """
        if indicator == "volume":
            colors = np.repeat(self.RED, len(self.bars))
            colors[self.bars["close"] <= self.bars["open"]] = self.GREEN

            trace = go.Bar(
                x=self.ticks,
                y=self.bars["volume"],
                showlegend=False,
                marker={"color": colors},
            )
        elif indicator == "rsi":
            win = kwargs.get("win")
            rsi = ta.RSI(self._bar_close, win)  # type: ignore
            trace = go.Scatter(x=self.ticks, y=rsi, showlegend=False)
        elif indicator == "bbands":
            self._remove_ma()
            win = kwargs.get("win")
            for name, ind in zip(
                ["bbands-high", "bbands-mean", "bbands-low"],
                ta.BBANDS(self._bar_close, win),  # type: ignore
            ):
                trace = go.Scatter(x=self.ticks, y=ind, showlegend=True, name=name)
                self.main_traces[name] = trace

            return
        else:
            raise ValueError(f"{indicator} not supported")

        self.ind_traces[indicator] = trace

    def add_marks(
        self,
        x: List[int],
        y: List[float],
        name: str,
        marker: str = "cross",
        color: Optional[str] = None,
    ):
        """向k线图中增加标记点"""
        trace = go.Scatter(
            x=self.ticks[x],
            y=y,
            mode="markers",
            marker_symbol=marker,
            marker_color=color,
            name=name,
        )
        self.main_traces[name] = trace

    def plot(self):
        """绘制图表"""
        fig = self.figure
        fig.show()


def plot_candlestick(
    bars: np.ndarray | pd.DataFrame,
    ma_groups: List[int] | None = None,
    title: str = "",
    **kwargs,
):
    """绘制k线图

    Args:
        bars: 输入的K线数据，numpy structured array或者dataframe类型，包含以下字段：
            open, high, low, close, volume
        ma_groups: 均线组，比如[5, 10, 20]表示绘制5, 10, 20日均线，可为None
        title: 生成的k线图标题，可用股票对应的symbol 作为title以示区分
    """
    if isinstance(bars, pd.DataFrame):
        if "date" not in bars.columns:
            bars["date"] = bars.index
        bars = bars.to_records(index=False)
    cs = Candlestick(bars, ma_groups, title=title)
    cs.plot()


def get_stock_list(
    date: datetime.date, code_only: bool = True
) -> Tuple[str] | pd.DataFrame:
    """获取某一交易日的股票列表

    Args:
        date (datetime.date): 列表所属日期

    Returns:
        如果code_only，则返回股票代码列表，否则返回dataframe，包含code, name, industry, area, list_date字段
    """
    df = pro_api().stock_basic(
        exchange="",
        list_status="L",
        fields="ts_code,symbol,name,area,industry,list_date",
    )

    df.rename(columns={"ts_code": "code", "list_date": "ipo"}, inplace=True)

    df["code"] = [code_tushare2jq(code) for code in df["code"]]

    df = df.query(f'ipo <= "{date}"')
    if code_only:
        return tuple(df["code"].tolist())
    return df

def _adjust_polars(df, selected_symbols, start, end):
    lf = df.lazy()

    adjusted = (
        lf
        .filter(
            (pl.col("date").is_between(start, end)) &
            (pl.col("asset").is_in(selected_symbols))
        )
        .sort(["asset", "date"])
        .with_columns(
            last_factor=pl.col("factor").last().over("asset")
        )
        .with_columns(
            ratio=pl.col("factor") / pl.col("last_factor"),
            volume_ratio=pl.col("last_factor") / pl.col("factor")
        )
        .with_columns(
            open=pl.col("open") * pl.col("ratio"),
            high=pl.col("high") * pl.col("ratio"),
            low=pl.col("low") * pl.col("ratio"),
            close=pl.col("close") * pl.col("ratio"),
            volume=pl.col("volume") * pl.col("volume_ratio"),
        )
        .with_columns(
            price=pl.col("open").shift(-1).over("asset")
        )
        .sort(["date", "asset"])
        .select([
            "date", "asset", 
            "open", "high", "low", "close", 
            "volume", "amount", "price"
        ])
    )

    return adjusted.collect()


def _load_day_bars_v2(start: datetime.date, end: datetime.date, universe: Tuple[str]|int=500):
    barss = None

    assert (
        isinstance(start, datetime.date)
        and isinstance(end, datetime.date)
        and not isinstance(start, datetime.datetime)
        and not isinstance(end, datetime.datetime)
    ), "start/end 必须为datetime.date类型"

    if end > datetime.date(2023, 12, 31) or end < datetime.date(2005, 1, 1):
        raise ValueError("Allow time range: 2005-01-04 ~ 2023-12-29")

    path = data_home / "ro/bars_1d_2005_2023_category.parquet"
    barss = pl.read_parquet(path, use_pyarrow=True)

    all_symbols = barss["asset"].unique().sort()
    if isinstance(universe, int):
        if universe == -1:
            universe_ = all_symbols
        else:
            universe_ = _ensure_payh(all_symbols, universe)
    else:
        universe_ = universe

    adjusted = _adjust_polars(barss, universe_, start, end)
    df = adjusted.to_pandas()
    df.set_index(["date", "asset"], inplace=True)
    
    return df

def pro_api(host='tushare', port=5290, timeout=30):
    """
    Create a tushare pro proxy object

    Args:
        host: Server hostname or IP address
        port: Server port number
        timeout: Connection timeout in seconds

    Returns:
        TushareProProxy object that behaves like tushare.pro_api()

    Example:
        pro = pro_proxy('localhost', 5290)
        df = pro.daily(ts_code='000001.SZ')
    """
    return TushareProProxy(host, port, timeout)

def load_bars_tushare(start_date: datetime.date, end_date: datetime.date, universe: List[str]) -> pd.DataFrame:
    """
    使用tushare pro接口获取股票数据，返回与load_bars相同格式的DataFrame

    Args:
        start_date: 开始日期
        end_date: 结束日期
        universe: 股票代码列表，格式如['000001.SZ', '000002.SZ']

    Returns:
        DataFrame: 以date和asset为MultiIndex，包含open,high,low,close,volume,amount,factor,price列
    """
    start_str = start_date.strftime('%Y%m%d')
    end_str = end_date.strftime('%Y%m%d')
    if isinstance(universe, str):
        universe = [universe]

    universe = [code_jq2tushare(code) for code in universe]
    all_data = []

    pro = pro_api()
    for ts_code in universe:
        try:
            df = pro.daily(ts_code=ts_code, start_date=start_str, end_date=end_str)

            if df.empty:
                continue

            try:
                adj_factor = pro.adj_factor(ts_code=ts_code, start_date=start_str, end_date=end_str)
            except Exception:
                continue

            df = pd.merge(df, adj_factor, on = ["ts_code", "trade_date"])

            # 重命名列并转换数据类型
            df = df.rename(columns={
                'trade_date': 'date',
                "vol": "volume"
            })

            # tushare返回的是字符串格式的日期，如'20231229'
            df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
            df['asset'] = code_tushare2jq(ts_code)

            # 按日期排序（tushare返回的是倒序）
            df = df.sort_values('date').reset_index(drop=True)

            # 进行前复权
            latest_adj_factor = df['adj_factor'].iloc[-1]
    
            # 计算前复权价格
            df['close'] = df['close'] * df['adj_factor'] / latest_adj_factor
            df['open'] = df['open'] * df['adj_factor'] / latest_adj_factor
            df['high'] = df['high'] * df['adj_factor'] / latest_adj_factor
            df['low'] = df['low'] * df['adj_factor'] / latest_adj_factor
            df['volume'] = df['volume'] * latest_adj_factor / df['adj_factor']

            # 选择需要的列
            df = df[['date', 'asset', 'open', 'high', 'low', 'close', 'volume', 'amount']]

            all_data.append(df)

        except Exception as e:
            print(f"Error loading data for {ts_code}: {e}")
            continue

    if not all_data:
        return pd.DataFrame()

    # 合并所有数据
    result = pd.concat(all_data, ignore_index=True)

    # 设置MultiIndex
    result = result.set_index(['date', 'asset'])

    # 添加price列（次日开盘价）
    result['price'] = result.groupby('asset')['open'].shift(-1)

    return result

