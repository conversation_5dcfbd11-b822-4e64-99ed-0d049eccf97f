server:
  port: 403
  host: "0.0.0.0"
  secret: "a3c18565d92aa5eb1fb615d9a4fcbd08"
  domain: test.quantide.cn
  url_prefix: "http://ke.quantide.cn:8080/"
  logging:
    handlers:
      - sink: /var/log/provision/app.log
        level: INFO
        format: "<level>{level: <8}</level> <green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> - <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        rotation: "10 MB"
accounts:
  preview: preview
  share: quantide
postgres:
  pool:
    min_size: 1
    max_size: 10
  user: quantide
  password: Quant1de
  host: localhost
  database: provision
nginx:
  ports:
    - "8080": 80
    - "8443": 443
    - "8403": 8403
  name: nginx
  network: course
site: ~/site

# JupyterLab 配置
jupyterlab:
  prefix: ["/lab/tree", "/api/contents"]

# 管理员用户配置
admin:
  users:
    - username: admin
      password: Quant!de
    - username: manager
      password: Quant!de

# 阿里云短信服务配置
sms:
  access_key_id: "LTAI5t9CMo66j4Eq1N6oFo2a"
  access_key_secret: "******************************"
  sign_name: "匡醍武汉信息技术"
  template_code: "SMS_484005076"
  endpoint: dysmsapi.aliyuncs.com

# API Token 配置
api_token: "a3c18565d92aa5eb1fb615d9a4fcb0520"

