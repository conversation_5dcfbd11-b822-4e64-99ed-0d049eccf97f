fa:
  plans: [bronze, silver, gold_a, gold_b, platinum, diamond]
  own_container: [platinum, diamond]
  # 专属容器过期后的保留时间
  retention: 30
  refund: 15
  initial: 3
  release_every: 3
  division: course
  exclude:
    bronze:
      - assignments/questions
      - assignments/answers
      - courseware/19.ipynb
      - courseware/20.ipynb
    silver:
      - courseware/19.ipynb
      - courseware/20.ipynb
    gold_a:
      - courseware/19.ipynb
      - assignments/questions/19.ipynb
      - assignments/answers/19.ipynb
    gold_b:
      - courseware/20.ipynb
      - assignments/questions/20.ipynb
      - assignments/answers/20.ipynb
  prime:
    bronze: 60
    silver: 90
    gold_a: 120
    gold_b: 120
    platinum: 365
    diamond: 365
  grace:
    bronze: 305
    silver: 305
    gold_a: 365
    gold_b: 365
    platinum: 365
    diamond: 365
l24:
  plans: [gold, diamond]
  own_container: [diamond]
  # 专属容器过期后的保留时间
  retention: 30
  refund: 15
  initial: 4
  release_every: 3
  division: course
  prime:
    gold: 120
    diamond: 365
  grace:
    gold: 365
    diamond: 365
pandas:
  own_container: []
  plans: [bronze]
  refund: 15
  initial: 3
  release_every: 3
  division: course
  prime:
    bronze: 60
  grace:
    bronze: 60
blog:
  own_container: []
  plans: [bronze]
  refund: 3
  initial: 3
  release_every: 1
  orderby: timestamp
  division: blog
  prime:
    bronze: 365
  grace:
    bronze: 30
