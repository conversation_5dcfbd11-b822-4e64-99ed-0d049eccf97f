import os
from pathlib import Path
from typing import Any
from zoneinfo import ZoneInfo

import yaml
from loguru import logger
from pydantic import BaseModel

from .containers import Model as ContainersCfg
from .provision import Model as ProvisionCfg
from .resources import Model as ResourceCfg


class AppConfig(BaseModel):
    provision: ProvisionCfg
    resources: ResourceCfg
    containers: ContainersCfg

    @classmethod
    def get_config_dir(cls):
        return Path(
            os.environ.get(
                "PROVISION_CONFIG_DIR", os.path.expanduser("~/.config/provision")
            )
        )

    @classmethod
    def get_factory_config_dir(cls):
        """出厂设置目录，即本文件所在目录"""
        return Path(__file__).parent

    def get_db_url(self):
        """获取postgres连接串。
        
          url: postgresql://username:password@hostname:port/database

          """
        url = f"postgresql://{self.provision.postgres.user}:{self.provision.postgres.password}@{self.provision.postgres.host}:{self.provision.postgres.port}/{self.provision.postgres.database}"
        return url

    def get_course_title(self, course_id: str) -> str:
        return getattr(self.resources, course_id).title

    def get_course_cover(self, course_id: str) -> str:
        return getattr(self.resources, course_id).cover

    def get_course_container_config(self, course_id: str) -> ContainersCfg:
        """获取课程的容器配置"""
        return getattr(self.containers, course_id).container

    def get_course_home(self, course_id: str) -> Path | None:
        """获取课程的根目录"""
        try:
            course = getattr(self.resources, course_id)
            return Path(course.home).expanduser()
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return None

    def get_course_division(self, course_id: str) -> str:
        """获取课程所属的门类，比如course/blog"""
        return getattr(self.resources, course_id).division

    def get_course_by_division(self, division: str) -> list[str]:
        """获取属于某个门类的课程id
        比如，传入course, 返回 ["l24", "fa",. ..]
        """
        results = []
        for key, value in self.resources.model_dump().items():
            if value.get("division") == division:
                results.append(key)

        return results

    def get_course_resources(self, course_id: str) -> list[str]:
        """获取课程配置的受控资源，即courseware, article, assignments"""
        return getattr(self.resources, course_id).resources

    def get_resources_config(self, course: str) -> Any:
        """获取资源配置"""
        return getattr(self.resources, course)

    def course_server_prefix(self):
        """返回服务器前缀"""
        prefix = self.provision.server.url_prefix
        if prefix.endswith("/"):
            return prefix[:-1]

        return prefix

    def get_preview_account(self):
        return getattr(self.provision.accounts, "preview", "preview")

    def get_shared_account(self):
        return getattr(self.provision.accounts, "share", "quantide")

    def get_jupyterlab_prefixes(self):
        return self.provision.jupyterlab.prefix or ["/lab/tree", "/api/contents"]

    def get_timezone(self):
        shanghai_timezone = ZoneInfo("Asia/Shanghai")
        return shanghai_timezone

    def get_resource_prefix(self):
        return self.provision.server.url_prefix or "http://ke.quantide.cn/"

    def load_config(self):
        print("config dir is", self.get_config_dir())
        for file, model in zip(
            [
                "provision.yaml",
                "resources.yaml",
                "containers.yaml",
            ],
            [ProvisionCfg, ResourceCfg, ContainersCfg],
        ):
            with open(self.get_config_dir() / file, encoding="utf-8") as f:
                data = yaml.safe_load(f)
                config = model(**data)
                setattr(self, file.split(".")[0], config)

cfg = AppConfig.model_construct()
__all__ = ["cfg"]
