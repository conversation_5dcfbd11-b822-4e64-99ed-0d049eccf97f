# generated by datamodel-codegen:
#   filename:  containers.yaml

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from provision.common.custom_base_model import CustomBaseModel


class Adhoc(CustomBaseModel):
    low: str
    high: str


class Share(Adhoc):
    pass


class Memory(CustomBaseModel):
    adhoc: Adhoc
    share: Share


class AllItem(CustomBaseModel):
    from_: str = Field(..., alias='from')
    to: Optional[str] = None


class Volumes(CustomBaseModel):
    all: List[AllItem]


class Postrun(CustomBaseModel):
    cmd: List[str]


class Container(CustomBaseModel):
    image: str
    volumes: Volumes
    postrun: Postrun


class L24(CustomBaseModel):
    memory: Memory
    container: Container


class Memory1(Memory):
    pass


class AllItem1(CustomBaseModel):
    from_: str = Field(..., alias='from')


class Volumes1(CustomBaseModel):
    all: List[AllItem1]


class Container1(CustomBaseModel):
    image: str
    volumes: Volumes1
    postrun: Postrun


class Fa(CustomBaseModel):
    memory: Memory1
    container: Container1


class Memory2(Memory):
    pass


class Volumes2(Volumes1):
    pass


class Container2(CustomBaseModel):
    image: str
    volumes: Volumes2
    postrun: Postrun


class Pandas(CustomBaseModel):
    memory: Memory2
    container: Container2


class Memory3(Memory):
    pass


class Volumes3(Volumes1):
    pass


class Container3(CustomBaseModel):
    image: str
    volumes: Volumes3
    postrun: Postrun


class Blog(CustomBaseModel):
    memory: Memory3
    container: Container3


class Model(CustomBaseModel):
    l24: L24
    fa: Fa
    pandas: Pandas
    blog: Blog
