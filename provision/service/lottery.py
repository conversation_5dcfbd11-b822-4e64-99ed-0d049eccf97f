"""
抽奖服务
"""

import datetime
import random
from typing import Any, Dict, List, Optional, Tuple

from loguru import logger

from provision.conf import cfg
from provision.store.customer import CustomerModel
from provision.store.lottery import LotteryLog, LotteryTicket


def transform_ticket(ticket: LotteryTicket) -> Dict[str, Any]:
    """添加课程标题"""
    ticket_dict = ticket.to_dict()
    ticket_dict["title"] = cfg.get_course_title(ticket_dict["course"])
    return ticket_dict
class LotteryService:
    """抽奖服务"""

    @staticmethod
    def generate_ticket_code() -> str:
        """生成唯一的5位券码，不使用容易混淆的字符（0, O, I, L）"""
        # 可用字符集
        chars = "********************************"
        # 生成5位随机券码
        return "".join(random.choice(chars) for _ in range(5))

    @staticmethod
    def generate_half_normal(min_val: int, max_val: int, step: int):
        """生成以min_val为峰值的单边正态分布随机数
        
        >>> import random
        >>> import matplotlib.pyplot as plt
        >>> samples = [generate_half_normal() for _ in range(10000)]

        >>> plt.hist(samples, bins=20, density=True)
        >>> plt.show()
        """
        while True:
            z = random.gauss(0, 1)
            if z >= 0:
                x = min_val + (max_val - min_val + step) * z / 3
                if x <= max_val:
                    return x//step * step

    @staticmethod
    def get_lottery_config(course: str) -> Optional[Dict[str, Any]]:
        """获取抽奖配置"""
        try:
            # 获取课程配置
            course_config = cfg.resources.model_dump().get(course, {})
            if not course_config:
                logger.error(f"Course config not found: {course}")
                return None

            # 获取抽奖配置
            lottery_config = course_config.get("lottery", {})
            if not lottery_config:
                logger.error(f"Lottery config not found for course: {course}")
                return None

            return {
                "course": course,
                "times": int(lottery_config.get("times", 5)),
                "min": int(lottery_config.get("min", 100)),
                "max": int(lottery_config.get("max", 500)),
                "step": int(lottery_config.get("step", 50)),
                "base_fare": int(lottery_config.get("base_fare", 2000)),
                "max_discount": int(lottery_config.get("max_discount", 700)),
                "expire": int(lottery_config.get("expire", 3)),
                "title": course_config.get("title", ""),
            }
        except Exception as e:
            logger.error(f"Error getting lottery config: {e}")
            return None

    @classmethod
    def get_all_lottery_configs(cls) -> List[Dict[str, Any]]:
        """获取所有抽奖配置"""
        try:
            # 从配置中获取所有课程
            configs = []
            for course, course_config in cfg.resources.model_dump().items():
                # 检查是否有抽奖配置
                if "lottery" in course_config:
                    config = cls.get_lottery_config(course)
                    if config:
                        configs.append(config)

            return configs
        except Exception as e:
            logger.error(f"Error getting all lottery configs: {e}")
            return []

    @staticmethod
    def has_valid_ticket(customer_id: int, course: str) -> bool:
        """检查用户是否已有该课程的未使用、未转让、未过期的奖券"""
        try:
            # 查找用户的未使用奖券
            tickets = LotteryTicket.find_by_customer(customer_id, "unused")

            # 检查是否有该课程的未过期奖券
            now = datetime.datetime.now()
            for ticket in tickets:
                if ticket.course == course and ticket.expires_at > now:
                    return True

            return False
        except Exception as e:
            logger.error(f"Error checking valid tickets: {e}")
            return False

    @classmethod
    def get_remaining_times(cls, customer_id: int, course: str) -> int:
        """获取剩余抽奖次数"""
        try:
            # 获取抽奖配置
            config = cls.get_lottery_config(course)
            if not config:
                return 0

            # 获取本月已抽奖次数
            used_times = LotteryLog.count_by_customer_month(customer_id, course)

            # 计算剩余次数
            remaining_times = config["times"] - used_times
            return max(0, remaining_times)
        except Exception as e:
            logger.error(f"Error getting remaining times: {e}")
            return 0

    @classmethod
    def draw(cls, customer_id: int, course: str) -> Tuple[bool, Dict[str, Any]]:
        """抽奖"""
        try:
            # 检查用户是否存在
            customer = CustomerModel.find_customer(customer_id)
            if not customer:
                logger.error(f"Customer {customer_id} not found")
                return False, {"error": "Customer not found"}

            # 获取抽奖配置
            config = cls.get_lottery_config(course)
            if not config:
                return False, {"error": "Lottery config not found"}

            # 检查剩余抽奖次数
            remaining_times = cls.get_remaining_times(customer_id, course)
            if remaining_times <= 0:
                return False, {"error": "No remaining lottery times"}

            # 检查用户是否已有该课程的未使用、未转让、未过期的奖券
            if cls.has_valid_ticket(customer_id, course):
                return False, {
                    "error": "You already have a valid ticket for this course"
                }

            # 使用半正态分布随机生成奖券面值
            discount = cls.generate_half_normal(
                config["min"], config["max"], config.get("step", 50)
            )

            # 生成唯一券码
            ticket_code = cls.generate_ticket_code()

            # 创建奖券，使用配置中的过期天数
            expires_at = datetime.datetime.now() + datetime.timedelta(
                days=config.get("expire", 3)
            )  # 默认3天
            ticket = LotteryTicket(
                ticket_code=ticket_code,
                customer_id=customer_id,
                course=course,
                discount=discount,
                base_fare=config["base_fare"],
                max_discount=config["max_discount"],
                expires_at=expires_at,
            )
            ticket.save()

            # 记录抽奖日志
            log = LotteryLog(
                customer_id=customer_id, course=course, result=f"ticket:{ticket.id}"
            )
            log.save()

            return True, {
                "success": True,
                "ticket": ticket.to_dict(),
                "remaining_times": remaining_times - 1,
            }
        except Exception as e:
            logger.error(f"Error drawing lottery: {e}")
            return False, {"error": str(e)}

    @staticmethod
    def get_tickets(
        customer_id: int, status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取用户的奖券"""

        try:
            tickets = LotteryTicket.find_by_customer(customer_id, status)
            return [transform_ticket(ticket) for ticket in tickets]
        except Exception as e:
            logger.error(f"Error getting tickets: {e}")
            return []

    @staticmethod
    def get_transferred_tickets(customer_id: int) -> List[Dict[str, Any]]:
        """获取转让给用户的奖券"""

        try:
            tickets = LotteryTicket.find_by_transferred_to(customer_id)
            return [transform_ticket(ticket) for ticket in tickets]
        except Exception as e:
            logger.error(f"Error getting transferred tickets: {e}")
            return []

    @staticmethod
    def get_transfers(customer_id: int) -> List[Dict[str, Any]]:
        """获取用户的转让记录（已废弃，返回空列表）"""
        logger.info(
            f"get_transfers called for customer {customer_id}, but this method is deprecated"
        )
        return []

    @staticmethod
    def claim_transfer_by_code(
        ticket_code: str, customer_id: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """通过券码领取转让的奖券"""
        try:
            # 查找奖券
            ticket = LotteryTicket.find_by_code(ticket_code)
            if ticket is None:
                logger.warning(
                    "客户 {} 在claim奖券时，输入了错误的券码{}",
                    customer_id,
                    ticket_code,
                )
                return False, {"error": "您输入的券码有误，请检查。"}

            # 检查奖券状态
            if ticket.status != "transferring":
                return False, {
                    "error": f"Ticket status is {ticket.status}, cannot claim"
                }

            # 检查是否是自己的奖券
            if ticket.customer_id == customer_id:
                return False, {"error": "不能转让给自己"}

            # 领取奖券
            success = ticket.claim_transfer(customer_id)
            if not success:
                return False, {"error": "Failed to claim ticket"}

            return True, {"success": True, "ticket": ticket.to_dict()}
        except Exception as e:
            logger.error(f"Error claiming transfer by code: {e}")
            return False, {"error": str(e)}

    @staticmethod
    def create_transfer(
        ticket_id: int, customer_id: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """创建奖券转让（将状态设置为转让中）"""
        # 获取奖券
        ticket = LotteryTicket.find_by_id(ticket_id)
        if not ticket:
            return False, {"error": "Ticket not found"}

        # 检查奖券是否属于用户
        if ticket.customer_id != customer_id:
            return False, {"error": "Ticket does not belong to the customer"}

        # 检查奖券状态
        if ticket.status == "transferring":
            # 如果奖券已经处于转让中状态，直接返回
            return True, {
                "success": True,
                "ticket": ticket.to_dict(),
                "message": "Ticket is already in transferring state",
            }
        elif ticket.status != "unused":
            return False, {
                "error": f"Ticket status is {ticket.status}, cannot transfer"
            }

        # 检查奖券是否过期
        if ticket.is_expired():
            return False, {"error": "Ticket has expired"}

        # 开始转让
        success = ticket.start_transfer()
        if not success:
            return False, {"error": "Failed to start transfer"}

        return True, {"success": True, "ticket": ticket.to_dict()}

    @staticmethod
    def use_ticket(
        ticket_id: int, customer_id: int, order_id: str
    ) -> Tuple[bool, Dict[str, Any]]:
        """使用奖券"""
        try:
            # 获取奖券
            ticket = LotteryTicket.find_by_id(ticket_id)
            if not ticket:
                return False, {"error": "Ticket not found"}

            # 检查奖券是否属于用户
            if ticket.customer_id != customer_id:
                return False, {"error": "Ticket does not belong to the customer"}

            # 检查奖券状态
            if ticket.status != "unused":
                return False, {"error": f"Ticket status is {ticket.status}, cannot use"}

            # 检查奖券是否过期
            if ticket.is_expired():
                return False, {"error": "Ticket has expired"}

            # 使用奖券
            success = ticket.use(order_id)
            if not success:
                return False, {"error": "Failed to use ticket"}

            return True, {"success": True, "ticket": ticket.to_dict()}
        except Exception as e:
            logger.error(f"Error using ticket: {e}")
            return False, {"error": str(e)}

    @staticmethod
    def get_ticket_by_code(ticket_code: str) -> Tuple[bool, Dict[str, Any]]:
        """根据券码查询奖券信息（管理员功能）"""
        try:
            # 查找奖券
            ticket = LotteryTicket.find_by_code(ticket_code)
            if not ticket:
                return False, {"error": "Ticket not found"}

            # 获取归属人信息和转让信息
            if ticket.status == "transferred":
                # 对于转让的券：
                # - customer_id 是原始归属人（转让来源）
                # - transferred_to 是当前归属人
                original_owner = CustomerModel.find_customer(ticket.customer_id)
                transferred_from = original_owner.account if original_owner else "Unknown"

                current_owner = (
                    CustomerModel.find_customer(ticket.transferred_to)
                    if ticket.transferred_to else None
                )
                owner_account = current_owner.account if current_owner else "Unknown"
            else:
                # 非转让的券，归属人就是 customer_id
                owner = CustomerModel.find_customer(ticket.customer_id)
                owner_account = owner.account if owner else "Unknown"
                transferred_from = None

            # 计算可兑额
            redeemable_amount = ticket.discount
            if ticket.status == "transferred":
                # 如果是转让的券，可兑额为原值的一半
                redeemable_amount = ticket.discount / 2

            # 获取课程标题
            course_title = ticket.course  # 可以后续扩展为从配置中获取课程名称

            ticket_info = {
                "ticket_code": ticket.ticket_code,
                "course": ticket.course,
                "course_title": course_title,
                "owner_account": owner_account,
                "transferred_from": transferred_from,
                "discount": ticket.discount,
                "redeemable_amount": redeemable_amount,
                "base_fare": ticket.base_fare,
                "max_discount": ticket.max_discount,
                "status": ticket.status,
                "expires_at": ticket.expires_at.isoformat() if ticket.expires_at else None,
                "used_at": ticket.used_at.isoformat() if ticket.used_at else None,
            }

            return True, {"success": True, "ticket": ticket_info}

        except Exception as e:
            logger.error(f"Error getting ticket by code: {e}")
            return False, {"error": "Internal server error"}

    @staticmethod
    def verify_ticket(ticket_code: str) -> Tuple[bool, Dict[str, Any]]:
        """核销奖券（管理员功能）"""
        try:
            # 查找奖券
            ticket = LotteryTicket.find_by_code(ticket_code)
            if not ticket:
                return False, {"error": "Ticket not found"}

            # 检查奖券状态 - 允许unused和transferred状态的券核销
            if ticket.status not in ["unused", "transferred"]:
                return False, {"error": f"Ticket status is {ticket.status}, cannot verify"}

            # 检查奖券是否过期
            if ticket.is_expired():
                return False, {"error": "Ticket has expired"}

            # 核销奖券（标记为已使用）
            success = ticket.use_for_verification("ADMIN_VERIFICATION")
            if not success:
                return False, {"error": "Failed to verify ticket"}

            return True, {"success": True, "message": "Ticket verified successfully"}

        except Exception as e:
            logger.error(f"Error verifying ticket: {e}")
            return False, {"error": "Internal server error"}


# 创建全局实例
lottery_service = LotteryService()
