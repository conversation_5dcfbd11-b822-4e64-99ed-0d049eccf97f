#!/usr/bin/env python3
"""
测试博客文章发布功能的脚本

这个脚本演示如何使用重构后的 API 接口来发布博客文章资源。
新架构将事务安全的数据库操作移到了 store 层。
"""

from datetime import datetime

import requests

# 配置
API_BASE_URL = "http://localhost:403"
API_TOKEN = "a3c18565d92aa5eb1fb615d9a4fcb0520"  # 从 provision.yaml 中获取

def test_publish_resource():
    """测试发布资源接口"""
    
    # 准备测试数据（seq 字段会自动生成）
    test_meta = {
        "course": "blog",
        "resource": "articles",
        "title": "测试博客文章",
        "description": "这是一个通过API发布的测试博客文章",
        "rel_path": "test_article.ipynb",
        "division": "blog",
        "publish_date": datetime.now().isoformat(),
        "price": 0.0,
        "price_tier_id": None,
        "img": None
    }
    
    # 测试数据
    test_cases = [
        {
            "name": "发布私有资源",
            "data": {
                "meta": test_meta,
                "allow_all": False
            }
        },
        {
            "name": "发布公开资源",
            "data": {
                "meta": {**test_meta, "title": "公开测试博客文章", "rel_path": "public_test_article.ipynb"},
                "allow_all": True
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        
        # 发送请求
        response = requests.post(
            f"{API_BASE_URL}/api/admin/resources/publish",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {API_TOKEN}"
            },
            json=test_case["data"]
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_invalid_token():
    """测试无效 token"""
    print("\n=== 测试无效 token ===")
    
    response = requests.post(
        f"{API_BASE_URL}/api/admin/resources/publish",
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer invalid_token"
        },
        json={
            "meta": {
                "course": "blog",
                "resource": "articles",
                "title": "测试",
                "rel_path": "test.ipynb",
                "division": "blog"
            }
        }
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 401:
        print("✅ 无效 token 测试通过")
    else:
        print("❌ 无效 token 测试失败")

def test_missing_fields():
    """测试缺少必需字段"""
    print("\n=== 测试缺少必需字段 ===")
    
    response = requests.post(
        f"{API_BASE_URL}/api/admin/resources/publish",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_TOKEN}"
        },
        json={
            "meta": {
                "course": "blog",
                # 缺少其他必需字段
            }
        }
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    if response.status_code == 400:
        print("✅ 缺少字段测试通过")
    else:
        print("❌ 缺少字段测试失败")

if __name__ == "__main__":
    print("开始测试博客文章发布功能...")
    
    try:
        test_publish_resource()
        test_invalid_token()
        test_missing_fields()
        
        print("\n🎉 所有测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
