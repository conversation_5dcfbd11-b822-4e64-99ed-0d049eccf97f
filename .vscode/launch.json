{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "env > course",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/env.py",
            "console": "integratedTerminal",
            "args": [
                "course",
                "fa",
                "aaron",
                "--redo"
            ]
        },
        {
            "name": "setup > setup-course",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "setup-course",
                "fa"
            ]
        },
        {
            "name": "setup > boot",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "boot"
            ]
        },
        {
            "name": "add",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "add",
                "fa",
                "shared"
            ]
        },
        {
            "name": "server start",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "start",
                "--run-as-daemon",
                "False",
                "--reload"
            ]
        },
        {
            "name": "upgrade",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "upgrade",
                "dist/provision-0.3.0-py3-none-any.whl",
                "--run-as-daemon",
                "False",
                "--overwrite-config"
            ]
        },
        {
            "name": "boot",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/provision/cli.py",
            "console": "integratedTerminal",
            "args": [
                "boot"
            ]
        }
    ]
}
