site_name: provision
# site_url: http://www.jieyu.ai
repo_url: https://github.com/zillionare/provision
repo_name: provision
#strict: true
nav:
  - home: index.md
  - installation: installation.md
  - usage: usage.md
  - modules: api.md
  - contributing: contributing.md
  - authors: authors.md
  - history: history.md
theme:
  name: material
  language: en
  #logo: assets/logo.png
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/weather-night
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/weather-sunny
        name: Switch to light mode
  features:
    - navigation.indexes
    - navigation.tabs
    - navigation.instant
    - navigation.tabs.sticky
markdown_extensions:
  - pymdownx.emoji:
      emoji_index: !!python/name:materialx.emoji.twemoji
      emoji_generator: !!python/name:materialx.emoji.to_svg
  - pymdownx.critic
  - pymdownx.caret
  - pymdownx.mark
  - pymdownx.tilde
  - pymdownx.tabbed
  - attr_list
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.highlight:
      linenums: true
  - pymdownx.superfences
  - pymdownx.details
  - admonition
  - toc:
      baselevel: 2
      permalink: true
      slugify: !!python/name:pymdownx.slugs.uslugify
  - meta
plugins:
  - include-markdown
  - search:
      lang: en
  - mkdocstrings:
      watch:
        - provision
extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/twitter
      # replace with your own tweet link below
      link: http://www.jieyu.ai
      name: Tweet
    - icon: fontawesome/brands/facebook
      # replace with your own facebook link below
      link: http://www.jieyu.ai
      name: Facebook
    - icon: fontawesome/brands/github
      link: https://github.com/zillionare/provision
      name: Github
    - icon: material/email
      link: "mailto:<EMAIL>"
  # to enable disqus, uncomment the following and put your disqus id below
  # disqus: disqus_id
# uncomment the following and put your google tracking id below to enable GA
#google_analytics:
  #- UA-xxx
  #- auto
