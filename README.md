# Provision System


## 镜像构建

构建镜像时，应该考虑到构建环境与部署环境的CPU架构是否相同。比如，如果是在mac上进行构建，而镜像要部署到amd64架构的机器上，那么应该使用`--platform linux/amd64`选项。

```bash
docker buildx build --platform linux/amd64 -t your_image_name:tag .
```

## preview容器
1. 创建 fa/preview 容器，单独的目录，只放少量课程
2. 创建 preview 账号， nginx会自动重定向到 course_fa_preview。通过设置refund日期，让所有课程都可以看

## 研究平台

定位： 小小策略，单篇付费 > 会员费 > 引导到课程

## 创建容器
<!--preview_l24-->
docker run  -d --memory-reservation 1.5g -m 4g --memory-swap=6g\
            -v ~/courses/l24_preview/courseware:/home/<USER>/notebooks/courseware:ro\
            -v ~/courses/l24_preview/assignments:/home/<USER>/notebooks/assignments:ro\
            -v ~/courses/l24_preview/supplements:/home/<USER>/notebooks/supplements:ro\
            -v ~/courses/l24_preview/video:/home/<USER>/notebooks/video:ro\
            -v ~/courses/l24_preview/课程指南.ipynb:/home/<USER>/notebooks/课程指南.ipynb:ro\
            -v ~/courses/l24_preview/notebook入门.ipynb:/home/<USER>/notebooks/notebook入门.ipynb:ro\
            -v ~/courses/l24_preview/notebook高级技巧.ipynb:/home/<USER>/notebooks/notebook高级技巧.ipynb:ro\
            -v ~/courses/l24_preview/.startup:/root/.ipython/profile_default/startup:ro\
            -v ~/courses/l24_preview/.config/jupyter_lab_config.py:/root/.jupyter/jupyter_lab_config.py:ro\
            -v ~/courses/l24_preview/.data/ro:/data/ro:ro\
            -v ~/courses/l24_preview/.data/rw:/data/rw:rw\
            -e course=l24\
            -e user=preview\
            --net=course --name course_l24_preview l24

<!-- blog -->
docker run  -d --memory-reservation 1.5g -m 8g\
            -v ~/courses/blog/articles:/home/<USER>/notebooks/articles:ro\
            -v ~/courses/blog/使用说明.ipynb:/home/<USER>/notebooks/使用说明.ipynb:ro\
            -v ~/courses/blog/notebook入门.ipynb:/home/<USER>/notebooks/notebook入门.ipynb:ro\
            -v ~/courses/blog/notebook高级技巧.ipynb:/home/<USER>/notebooks/notebook高级技巧.ipynb:ro\
            -v ~/courses/blog/data.ipynb:/home/<USER>/notebooks/data.ipynb:ro\
            -v ~/courses/blog/.startup:/root/.ipython/profile_default/startup:ro\
            -v ~/courses/blog/.config/jupyter_lab_config.py:/root/.jupyter/jupyter_lab_config.py:ro\
            -v ~/courses/blog/.config/kernel.json:/opt/miniconda/share/jupyter/kernels/python3/kernel.json:ro\
            -v ~/courses/blog/home:/home/<USER>/notebooks/home:rw\
            -v ~/courses/blog/papers:/home/<USER>/notebooks/papers:ro\
            -v ~/courses/blog/podcast:/home/<USER>/notebooks/podcast:ro\
            -v ~/courses/blog/.data/ro:/data/ro:ro\
            -v ~/courses/blog/.data/rw:/data/rw:rw\
            -e course=blog -e user=quantide --net=course --name course_blog_quantide fav3


<!--preview fa-->
docker run  -d --memory-reservation 1.5g -m 4g\
            -v ~/courses/fa_preview/courseware:/home/<USER>/notebooks/courseware:ro\
            -v ~/courses/fa_preview/assignments:/home/<USER>/notebooks/assignments:ro\
            -v ~/courses/fa_preview/supplements:/home/<USER>/notebooks/supplements:ro\
            -v ~/courses/fa_preview/video:/home/<USER>/notebooks/video:ro\
            -v ~/courses/fa_preview/课程指南.ipynb:/home/<USER>/notebooks/课程指南.ipynb:ro\
            -v ~/courses/fa_preview/notebook入门.ipynb:/home/<USER>/notebooks/notebook入门.ipynb:ro\
            -v ~/courses/fa_preview/notebook高级技巧.ipynb:/home/<USER>/notebooks/notebook高级技巧.ipynb:ro\
            -v ~/courses/fa_preview/.startup:/root/.ipython/profile_default/startup:ro\
            -v ~/courses/fa_preview/.config/jupyter_lab_config.py:/root/.jupyter/jupyter_lab_config.py:ro\
            -v ~/courses/fa_preview/.data/ro:/data/ro:ro\
            -v ~/courses/fa_preview/.data/rw:/data/rw:rw\
            -e course=fa\
            -e user=preview\
            --net=course\
            --name course_fa_preview fav3

## 测试用例

1. url(/)，看首页加载是否有login，图片是否正确显示
2. 后台增加用户aa, 前台登录
3. 为aa依次注册课程 l24, pandas 和fa，分别使用 bronze, bronze, diamond账号，每注册一课，前台刷新一次，看看课程列表是否正确。
4. 增加用户bb，前台登录
5. 为bb注册 l24, fa, 分别使用 diamon， bronze账号，每注册一课，前台刷新一次，看看课程列表是否正确。
6. 修改bb的fa课程的时间，看资源访问控制如何
7. 修改bb的24课时间，让其超过prime时间，看是否进入已完课列表，但仍然可以共享容器访问
8. 修改bb的24课时间，让其超过expire时间，看是否进入已完课列表，并且不可访问
9.  后台新建消息，前台看是否能接收到
10. bb更改密码，看能否登录
11. 将课程改为已完结，看能否访问套餐内允许访问的全部资源。

### 0.3部署

1. 在项目目录下，运行.reload.sh，打出最新的包（包含静态文件）
2. 本地创建postgres, 命令是 docker run -d --name postgres -e POSTGRES_USER=quantide -e POSTGRES_PASSWORD=Quant1de -e POSTGRES_DB=provision -p 5432:5432 --restart unless-stopped postgres
3. 创建数据库，使用以下语句.dev/0.3.sql
4. 用dbeaver连接数据库，执行数据库迁移。
   1. 通过以下语句，导致customers, registry表格为csv:
   ```sql
copy customers to '/tmp/customers.csv' (format csv, header);
    copy registry to '/tmp/registry.csv' (format csv, header);
```
2. 修改registry.csv，将 grace_end 列改为 retention_end， 增加division = course列。
```
import pandas as pd
    df = pd.read_csv("/tmp/registry.csv")
    df.rename(columns={"grace_end": "retention_end"}, inplace=True)
    df["division"] = "course"
    df.to_csv("/tmp/registry_new.csv", index=False, header=True)
```
3. 重建序列，在dbearver中执行：
```sql
SELECT SETVAL('customers_id_seq', (SELECT MAX(id) FROM customers) + 1);
    SELECT SETVAL('registry_id_seq', (SELECT MAX(id) from registry) + 1);
    SELECT SETVAL('messages_id_seq', (SELECT MAX(id) from messages) + 1);
```
5. 停止provision, 将~/.config/provision/provision.sessions copy到 ~/.local/run/provision下
6. 备份(mv) ~/.config/provision到~/.backups/provision下
7. 安装新版本，运行provision setup。
8. 删除nginx，运行provision boot，这样创建出新版的nginx
9. 启动provision，登录前后台，开始测试用例。

### 0.4部署
1. 重启frps和frpc，使得真实的客户端IP可以传递到nginx(跳过)
2. 执行0.4.sql
3. 执行provision upgrade,忽略数据库备份错误

## developer

如果要通过重要公告新增markdown文件，要把文件放到static/markdown下然后在BulletinView中，增加菜单。

### 数据库恢复

docker exec -i postgres psql -U quantide -d provision < /path/to/backup.sql


### 0.5部署

provision upgrade *.whl --overwrite-config

## bugs

### quantstats
安装quantstats之后，存在一些错误，比如ZMQInteractiveShell' object has no attribute 'magic'，这是由于 QuantStats 库在 Jupyter Notebook 环境中尝试调用 IPython 的 magic 方法，但该方法在最新版本的 IPython 中已被重构导致的。此时需要通过github安装：

pip install git+https://github.com/ranaroussi/quantstats.git

然后可能遇到UnsupportedFunctionCall: numpy operations are not valid with resample. Use .resample(...).sum() instead错误，这个错误是由于 Pandas 库的版本兼容性问题导致的。在 Pandas 2.0+ 版本中，resample().sum(axis=0) 这种写法已被弃用，需要直接使用 resample().sum()。

方案是修改 ~/miniforge3/envs/zillionare/lib/python3.12/site-packages/quantstats/_plotting/core.py中的第294行：

returns = returns.last() if compound is True else returns.sum(axis=0)

去掉axis=0

到static/markdown下然后在BulletinView中，增加菜单。
-i postgres psql -U quantide -d provision < /path/to/backup.sql

## bugs

### quantstats
安装quantstats之后，存在一些错误，比如ZMQInteractiveShell' object has no attribute 'magic'，这是由于 QuantStats 库在 Jupyter Notebook 环境中尝试调用 IPython 的 magic 方法，但该方法在最新版本的 IPython 中已被重构导致的。此时需要通过github安装：

pip install git+https://github.com/ranaroussi/quantstats.git

然后可能遇到UnsupportedFunctionCall: numpy operations are not valid with resample. Use .resample(...).sum() instead错误，这个错误是由于 Pandas 库的版本兼容性问题导致的。在 Pandas 2.0+ 版本中，resample().sum(axis=0) 这种写法已被弃用，需要直接使用 resample().sum()。

方案是修改 ~/miniforge3/envs/zillionare/lib/python3.12/site-packages/quantstats/_plotting/core.py中的第294行：

returns = returns.last() if compound is True else returns.sum(axis=0)

去掉axis=0

到static/markdown下然后在BulletinView中，增加菜单。

### 数据库恢复

docker exec -i postgres psql -U quantide -d provision < /path/to/backup.sql

## bugs

### quantstats
安装quantstats之后，存在一些错误，比如ZMQInteractiveShell' object has no attribute 'magic'，这是由于 QuantStats 库在 Jupyter Notebook 环境中尝试调用 IPython 的 magic 方法，但该方法在最新版本的 IPython 中已被重构导致的。此时需要通过github安装：

pip install git+https://github.com/ranaroussi/quantstats.git

然后可能遇到UnsupportedFunctionCall: numpy operations are not valid with resample. Use .resample(...).sum() instead错误，这个错误是由于 Pandas 库的版本兼容性问题导致的。在 Pandas 2.0+ 版本中，resample().sum(axis=0) 这种写法已被弃用，需要直接使用 resample().sum()。

方案是修改 ~/miniforge3/envs/zillionare/lib/python3.12/site-packages/quantstats/_plotting/core.py中的第294行：

returns = returns.last() if compound is True else returns.sum(axis=0)

去掉axis=0
