---
description: 
globs: 
---

# Your rule content

- 保持前后端分离
- 在front中，使用vue3，声明式
- 在backend中，限制：
    1. 使用blacksheep作为web框架
    2. 使用postgresql作为数据库
    3. 使用Python 3.13
    4. 运行时，使用conda构建好的provision环境
    5. 使用poetry和pyproject.toml，而不是requirements.txt来管理依赖。
    6. 每开发一个功能，都要开发完成对应的单元测试。单元测试时，始终使用conda已经创建好的虚拟环境provision
    7. 使用tox来管理测试。测试时使用nginx的容器。
    8. 所有配置都由provision.conf来进行管理
    9. 初始化通过provision setup命令来完成，包括创建数据库
