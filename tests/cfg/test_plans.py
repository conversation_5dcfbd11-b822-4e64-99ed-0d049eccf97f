import pytest

from provision.conf.plans import ServicePlan


@pytest.fixture
def mock_sp():
    """Create mock ServicePlan with controlled config"""
    sp = ServicePlan()
    return sp


def test_expand_with_mappings(mock_sp):
    el = mock_sp.d.get("fa").get("bronze")[0]
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/03.ipynb",
        "courseware/04.ipynb",
        "courseware/08.ipynb",
    ]
    assert mock_sp.build_available_urls("fa", el) == expected


def test_get_periods(mock_sp):
    """Test when expire period is explicitly configured"""
    # Example from mocked test data where course 'test_course' has silver plan with 90 days
    result = mock_sp.get_expire_period("fa", "silver")
    assert result == 275

    result = mock_sp.get_expire_period("default", "silver")
    assert result == 120

    assert mock_sp.get_prime_period("fa", "bronze") == 90

    assert mock_sp.get_refundable_period("fa", "bronze") == 15


def test_has_own_container(mock_sp):
    assert mock_sp.has_own_container("fa", "bronze") == False
    assert mock_sp.has_own_container("fa", "platinum")


def test_parse_resource_expression_normal(mock_sp):
    """Test parsing a normal resource expression."""
    expression = "courseware/(01-04,08).ipynb"
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/03.ipynb",
        "courseware/04.ipynb",
        "courseware/08.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_empty(mock_sp):
    """Test parsing an empty expression."""
    expression = ""
    expected = []
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_invalid(mock_sp):
    """Test parsing an invalid expression."""
    expression = "invalid_expression"
    expected = ["invalid_expression"]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_range(mock_sp):
    """Test parsing an expression with a range."""
    expression = "assignments/(01-03).ipynb"
    expected = [
        "assignments/01.ipynb",
        "assignments/02.ipynb",
        "assignments/03.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_single(mock_sp):
    """Test parsing an expression with a single value."""
    expression = "courseware/(05).ipynb"
    expected = ["courseware/05.ipynb"]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_mixed(mock_sp):
    """Test parsing an expression with mixed values."""
    expression = "courseware/(01-02,05,07-08).ipynb"
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/05.ipynb",
        "courseware/07.ipynb",
        "courseware/08.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_no_leading_zero(mock_sp):
    """Test parsing an expression without leading zeros."""
    expression = "courseware/(1-3,5).ipynb"
    expected = [
        "courseware/1.ipynb",
        "courseware/2.ipynb",
        "courseware/3.ipynb",
        "courseware/5.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_mixed_leading_zero(mock_sp):
    """Test parsing an expression with mixed leading zeros."""
    expression = "courseware/(01-03,5,07).ipynb"
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/03.ipynb",
        "courseware/5.ipynb",
        "courseware/07.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_empty_range(mock_sp):
    """Test parsing an expression with an empty range."""
    expression = "courseware/().ipynb"
    expected = []
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_empty_items(mock_sp):
    """Test parsing an expression with empty items."""
    expression = "courseware/(,).ipynb"
    expected = []
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_whitespace(mock_sp):
    """Test parsing an expression with whitespace."""
    expression = "courseware/(01 - 03, 05).ipynb"
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/03.ipynb",
        "courseware/05.ipynb",
    ]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_different_resource_types(mock_sp):
    """Test parsing expressions with different resource types."""
    expressions = [
        "courseware/(01-02).ipynb",
        "assignments/(01-02).ipynb",
        "supplements/(01-02).ipynb",
    ]
    expected_results = [
        ["courseware/01.ipynb", "courseware/02.ipynb"],
        ["assignments/01.ipynb", "assignments/02.ipynb"],
        ["supplements/01.ipynb", "supplements/02.ipynb"],
    ]

    for expr, expected in zip(expressions, expected_results):
        result = mock_sp.parse_resource_expression(expr)
        assert result == expected


def test_parse_resource_expression_large_range(mock_sp):
    """Test parsing an expression with a large range."""
    expression = "courseware/(01-20).ipynb"
    expected = [f"courseware/{i:02d}.ipynb" for i in range(1, 21)]
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


def test_parse_resource_expression_invalid_range(mock_sp):
    """Test parsing an expression with an invalid range (end < start)."""
    expression = "courseware/(05-03).ipynb"
    # 这种情况下，range(5, 3+1)会生成空列表，所以结果应该是空的
    expected = []
    result = mock_sp.parse_resource_expression(expression)
    assert result == expected


# 以下是 build_available_urls 函数的测试用例


def test_build_available_urls_string_expression(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理字符串表达式的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware", "assignments"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {"available": "courseware/(01-03).ipynb"}

    # 预期结果
    expected = ["courseware/01.ipynb", "courseware/02.ipynb", "courseware/03.ipynb"]

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert result == expected


def test_build_available_urls_list_expressions(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理列表表达式的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware", "assignments"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {"available": ["courseware/(01-02).ipynb", "assignments/(01-02).ipynb"]}

    # 预期结果
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "assignments/01.ipynb",
        "assignments/02.ipynb",
    ]

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert result == expected


def test_build_available_urls_empty_available(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理空 available 值的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware", "assignments"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {"available": ""}

    # 预期结果
    expected = []

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert result == expected


def test_build_available_urls_missing_available(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理缺少 available 字段的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware", "assignments"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {}

    # 预期结果
    expected = []

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert result == expected


def test_build_available_urls_invalid_resource_type(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理无效资源类型的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {"available": "invalid_resource/(01-03).ipynb"}

    # 预期结果 - 即使资源类型无效，也应该返回解析后的路径
    expected = [
        "invalid_resource/01.ipynb",
        "invalid_resource/02.ipynb",
        "invalid_resource/03.ipynb",
    ]

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert result == expected


def test_build_available_urls_course_not_configured(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理未配置课程的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return None

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "nonexistent_course"
    phase_cfg = {"available": "courseware/(01-03).ipynb"}

    # 验证函数抛出 ValueError 异常
    import pytest

    with pytest.raises(ValueError, match=f"Course {course_id} 未配置"):
        mock_sp.build_available_urls(course_id, phase_cfg)


def test_build_available_urls_complex_expressions(mock_sp, monkeypatch):
    """测试 build_available_urls 函数处理复杂表达式的情况"""

    # 模拟 get_course_config 函数返回值
    def mock_get_course_config(course_id):
        return {"restricted": ["courseware", "assignments", "supplements"]}

    monkeypatch.setattr("provision.cfg.plans.get_course_config", mock_get_course_config)

    # 测试数据
    course_id = "test_course"
    phase_cfg = {
        "available": [
            "courseware/(01-03,05,07-08).ipynb",
            "assignments/(01-02).ipynb",
            "supplements/(01).ipynb",
        ]
    }

    # 预期结果
    expected = [
        "courseware/01.ipynb",
        "courseware/02.ipynb",
        "courseware/03.ipynb",
        "courseware/05.ipynb",
        "courseware/07.ipynb",
        "courseware/08.ipynb",
        "assignments/01.ipynb",
        "assignments/02.ipynb",
        "supplements/01.ipynb",
    ]

    # 调用函数
    result = mock_sp.build_available_urls(course_id, phase_cfg)

    # 验证结果
    assert sorted(result) == sorted(expected)
