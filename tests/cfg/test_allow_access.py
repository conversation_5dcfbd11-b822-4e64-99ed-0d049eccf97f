"""Tests for the allow_access method in plans.py."""

import datetime
from unittest.mock import MagicMock, patch

import pytest

from provision.common.types import LifeCycle
from provision.conf.plans import ServicePlan


@pytest.fixture
def mock_sp():
    """Create a ServicePlan instance for testing."""
    sp = ServicePlan()
    # 设置 jupyterlab_prefixes
    sp.jupyterlab_prefixes = ["/lab/tree", "/api/contents"]
    return sp


@pytest.fixture
def mock_registry():
    """Mock the RegistryModel for testing."""
    with patch("provision.store.registry.RegistryModel") as mock:
        # 创建一个模拟的注册记录
        registration = MagicMock()
        registration.expire_time = datetime.datetime.now() + datetime.timedelta(days=30)
        registration.prime_end = datetime.datetime.now() + datetime.timedelta(days=15)
        registration.refund_end = datetime.datetime.now() - datetime.timedelta(days=1)
        registration.plan = "gold"

        # 设置 find_reservation_by_customer_course 方法的返回值
        mock.find_reservation_by_customer_course.return_value = registration

        yield mock


def test_allow_access_non_course_path(mock_sp):
    """测试非 course 开头的路径是否直接允许访问。"""
    url = "http://example.com/academy/dashboard"
    result = mock_sp.allow_access(url, 123)
    assert result is True


def test_allow_access_invalid_path_format(mock_sp):
    """测试路径格式不正确的情况。"""
    url = "http://example.com/course"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_preview_account(mock_sp, monkeypatch):
    """测试访问 preview 账号的资源是否直接允许访问。"""
    # 模拟 get_preview_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_preview_account", lambda: "preview")

    url = "http://example.com/course/fa/preview/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is True


def test_allow_access_user_not_registered(mock_sp, mock_registry):
    """测试用户未注册课程的情况。"""
    # 设置 find_reservation_by_customer_course 方法返回 None
    mock_registry.find_reservation_by_customer_course.return_value = None

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_expired_registration(mock_sp, mock_registry):
    """测试账号已过期的情况。"""
    # 设置 expire_time 为过去的时间
    registration = mock_registry.find_reservation_by_customer_course.return_value
    registration.expire_time = datetime.datetime.now() - datetime.timedelta(days=1)

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_shared_container_user_accessing_private_container(
    mock_sp, mock_registry, monkeypatch
):
    """测试共享容器用户试图访问专属容器的情况。"""
    # 模拟 has_own_container 方法返回 False
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: False)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_prime_ended_accessing_private_container(
    mock_sp, mock_registry, monkeypatch
):
    """测试 prime 到期后试图访问专属容器的情况。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")
    # 设置 prime_end 为过去的时间
    registration = mock_registry.find_reservation_by_customer_course.return_value
    registration.prime_end = datetime.datetime.now() - datetime.timedelta(days=1)

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_non_notebook_resource(mock_sp, mock_registry, monkeypatch):
    """测试非 .ipynb 结尾的资源是否直接允许访问。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")

    url = "http://example.com/course/fa/user123/lab/tree/courseware/image.png"
    result = mock_sp.allow_access(url, 123)
    assert result is True


def test_allow_access_course_not_configured(mock_sp, mock_registry, monkeypatch):
    """测试课程未配置的情况。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")
    # 模拟 get_course_config 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_course_config", lambda course_id: None)

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False


def test_allow_access_non_restricted_resource(mock_sp, mock_registry, monkeypatch):
    """测试非受限资源是否直接允许访问。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")
    # 模拟 get_course_config 函数返回值
    monkeypatch.setattr(
        "provision.cfg.plans.get_course_config",
        lambda course_id: {"restricted": ["courseware", "assignments"]},
    )

    url = "http://example.com/course/fa/user123/lab/tree/supplements/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is True


def test_allow_access_restricted_resource_allowed(mock_sp, mock_registry, monkeypatch):
    """测试受限资源在允许访问的规则中的情况。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")
    # 模拟 get_course_config 函数返回值
    monkeypatch.setattr(
        "provision.cfg.plans.get_course_config",
        lambda course_id: {"restricted": ["courseware", "assignments"]},
    )

    # 设置 rules
    mock_sp.rules = {"fa_gold_prime": ["courseware/01.ipynb", "courseware/02.ipynb"]}

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is True


def test_allow_access_restricted_resource_not_allowed(
    mock_sp, mock_registry, monkeypatch
):
    """测试受限资源不在允许访问的规则中的情况。"""
    # 模拟 has_own_container 方法返回 True
    monkeypatch.setattr(mock_sp, "has_own_container", lambda course_id, plan: True)
    # 模拟 get_shared_account 函数返回值
    monkeypatch.setattr("provision.cfg.plans.get_shared_account", lambda: "shared")
    # 模拟 get_course_config 函数返回值
    monkeypatch.setattr(
        "provision.cfg.plans.get_course_config",
        lambda course_id: {"restricted": ["courseware", "assignments"]},
    )

    # 设置 rules
    mock_sp.rules = {"fa_gold_prime": ["courseware/02.ipynb", "courseware/03.ipynb"]}

    url = "http://example.com/course/fa/user123/lab/tree/courseware/01.ipynb"
    result = mock_sp.allow_access(url, 123)
    assert result is False
