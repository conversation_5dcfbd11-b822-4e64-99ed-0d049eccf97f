"""测试 academy.py 中 preview 账号的特殊处理逻辑"""

import datetime
import pytest
from unittest.mock import Mock, patch

from provision.access.resources import ResourceManager
from provision.conf.plans import ServicePlan
from provision.store.resource import ResourceModel


@pytest.fixture
def mock_resource_manager():
    """创建模拟的 ResourceManager"""
    rm = ResourceManager()
    return rm


@pytest.fixture
def mock_service_plan():
    """创建模拟的 ServicePlan"""
    sp = ServicePlan()
    # 模拟 plans 数据
    sp.plans = {
        "fa": Mock(initial=3, release_every=3),
        "l24": Mock(initial=4, release_every=3),
        "pandas": Mock(initial=3, release_every=3),
    }
    return sp


@pytest.fixture
def mock_resources():
    """创建模拟的资源数据"""
    resources = []
    for i in range(1, 6):  # 创建5个资源
        resource = Mock()
        resource.id = i
        resource.seq = i
        resource.rel_path = f"courseware/{i:02d}.ipynb"
        resources.append(resource)
    return resources


def test_label_course_resources_for_preview(mock_resource_manager, mock_service_plan, mock_resources):
    """测试 preview 账号的资源标记逻辑"""
    
    # 模拟 sp.plans
    with patch('provision.access.resources.sp', mock_service_plan):
        # 模拟 ResourceModel.find_course_sub_resources 返回值
        with patch.object(ResourceModel, 'find_course_sub_resources', return_value=mock_resources):
            
            # 调用 preview 账号的资源标记方法
            result = mock_resource_manager.label_course_resources_for_preview(
                course="fa",
                sub="courseware", 
                url_prefix="/course/fa/preview/lab/tree"
            )
            
            # 验证结果
            assert len(result) == 5
            
            # 验证前3个资源（initial=3）应该是开放的
            for i in range(3):
                assert result[i]["reason"] is None  # 无限制原因
                assert result[i]["url"] is not None  # 有访问URL
                assert result[i]["chapter"] == f"{i+1:02d}"
                assert result[i]["type"] == "courseware"
                assert result[i]["resourceId"] == i+1
            
            # 验证后2个资源应该显示"购买后解锁"
            for i in range(3, 5):
                assert result[i]["reason"] == "购买后解锁"
                assert result[i]["url"] is None  # 无访问URL
                assert result[i]["chapter"] == f"{i+1:02d}"
                assert result[i]["type"] == "courseware"
                assert result[i]["resourceId"] == i+1


def test_label_course_resources_for_preview_different_initial(mock_resource_manager, mock_service_plan, mock_resources):
    """测试不同课程的 initial 值"""
    
    # 模拟 sp.plans，l24 课程的 initial=4
    with patch('provision.access.resources.sp', mock_service_plan):
        # 模拟 ResourceModel.find_course_sub_resources 返回值
        with patch.object(ResourceModel, 'find_course_sub_resources', return_value=mock_resources):
            
            # 调用 l24 课程的资源标记方法
            result = mock_resource_manager.label_course_resources_for_preview(
                course="l24",
                sub="courseware", 
                url_prefix="/course/l24/preview/lab/tree"
            )
            
            # 验证结果
            assert len(result) == 5
            
            # 验证前4个资源（initial=4）应该是开放的
            for i in range(4):
                assert result[i]["reason"] is None  # 无限制原因
                assert result[i]["url"] is not None  # 有访问URL
            
            # 验证最后1个资源应该显示"购买后解锁"
            assert result[4]["reason"] == "购买后解锁"
            assert result[4]["url"] is None  # 无访问URL


def test_label_course_resources_for_preview_no_resources(mock_resource_manager, mock_service_plan):
    """测试没有资源的情况"""
    
    with patch('provision.access.resources.sp', mock_service_plan):
        # 模拟 ResourceModel.find_course_sub_resources 返回 None
        with patch.object(ResourceModel, 'find_course_sub_resources', return_value=None):
            
            result = mock_resource_manager.label_course_resources_for_preview(
                course="fa",
                sub="courseware", 
                url_prefix="/course/fa/preview/lab/tree"
            )
            
            # 验证返回空列表
            assert result == []


def test_label_course_resources_for_preview_all_resources_open(mock_resource_manager, mock_service_plan):
    """测试当 initial 值大于等于资源总数时，所有资源都应该开放"""
    
    # 创建只有2个资源的情况
    limited_resources = []
    for i in range(1, 3):  # 只创建2个资源
        resource = Mock()
        resource.id = i
        resource.seq = i
        resource.rel_path = f"courseware/{i:02d}.ipynb"
        limited_resources.append(resource)
    
    with patch('provision.access.resources.sp', mock_service_plan):
        # 模拟 ResourceModel.find_course_sub_resources 返回只有2个资源
        with patch.object(ResourceModel, 'find_course_sub_resources', return_value=limited_resources):
            
            # fa 课程的 initial=3，但只有2个资源
            result = mock_resource_manager.label_course_resources_for_preview(
                course="fa",
                sub="courseware", 
                url_prefix="/course/fa/preview/lab/tree"
            )
            
            # 验证所有资源都应该开放
            assert len(result) == 2
            for i in range(2):
                assert result[i]["reason"] is None  # 无限制原因
                assert result[i]["url"] is not None  # 有访问URL
