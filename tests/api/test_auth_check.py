import subprocess
import threading
import time

import pytest
import requests
from blacksheep.testing import TestClient

from provision.app import app


@pytest.mark.asyncio
async def test_direct_auth_endpoint(global_client):
    """
    Test the authentication endpoint directly using the test client.
    验证认证端点可以直接通过测试客户端访问。
    """
    # Send a GET request to the auth endpoint
    response = await global_client.get("/ke/24lectures/courseware/lesson01.ipynb")

    # Verify the response status code is 200 (OK)
    assert response.status == 200

    # Verify the response content indicates successful authentication
    content = await response.json()
    assert content.get("status") == "success"
    assert "Authentication successful" in content.get("message", "")


def test_nginx_auth_simulation():
    """
    模拟nginx的鉴权请求。

    nginx使用auth_request模块向Provision服务器发送鉴权请求，
    当用户访问课程资源时，nginx会向Provision服务器发送鉴权请求。
    此测试模拟了该鉴权流程。
    """
    # 检查Provision服务器是否在端口8403上运行
    try:
        response = requests.get("http://localhost:8403/", timeout=2)
        if response.status_code != 200:
            pytest.skip("Provision服务器在端口8403上不可用")
    except requests.exceptions.ConnectionError:
        pytest.skip("Provision服务器在端口8403上不可用")

    # 直接请求Provision服务器上的鉴权URL模拟nginx的请求
    auth_url = "http://localhost:8403/sso/customer/auth"
    response = requests.get(auth_url, timeout=5)

    # 验证返回状态码是200(OK)
    assert response.status_code == 200

    # 验证返回内容表示认证成功
    content = response.json()
    assert content.get("status") == "success"
    assert "Authentication successful" in content.get("message", "")
