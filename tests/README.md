# 单元测试指南

## 测试覆盖范围

本测试套件主要针对以下功能模块进行测试：

1. **客户访问控制模板** - `test_access_control.py`
   - 资源类别管理
   - 课程资源配置
   - 访问权限控制逻辑



## 环境准备

1. 安装测试依赖：

```bash
pip install -r requirements.txt
```

## 运行测试

### 运行所有测试

```bash
pytest tests/
```

### 运行特定模块的测试

```bash
pytest tests/test_access_control.py
```

### 生成测试覆盖率报告

```bash
pytest --cov=provision tests/
```

详细覆盖率报告：

```bash
pytest --cov=provision --cov-report=html tests/
```

然后在浏览器中打开 `htmlcov/index.html` 查看报告。

## 注意事项

1. 测试使用内存数据库，不会影响实际的生产数据。
2. 每个测试函数都会创建自己的测试数据并在测试结束后清理。
3. 在 CI/CD 环境中，应定期运行这些测试以确保代码质量。 
