{"cells": [{"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在写入/Users/<USER>/workspace/provision/tests/assets/course/research/notebook高级技巧.ipynb\n", "正在写入/Users/<USER>/workspace/provision/tests/assets/course/research/notebook入门.ipynb\n", "正在写入/Users/<USER>/workspace/provision/tests/assets/course/research/使用说明.ipynb\n"]}], "source": ["from pathlib import Path\n", "import datetime\n", "import random\n", "import nbformat\n", "\n", "for notebook in Path(\"~/workspace/provision/tests/assets/course/research\").expanduser().glob(\"**/*.ipynb\"):\n", "    nb = nbformat.read(notebook, as_version=4)\n", "\n", "    meta = nb.get(\"metadata\")\n", "    title = nb[\"cells\"][0][\"source\"].split(\"\\n\")[0].replace(\"#\", \"\")\n", "    publish_date = datetime.datetime.now() + datetime.timedelta(days = random.randint(1, 100))\n", "\n", "    meta.update({\n", "        \"title\": title,\n", "        \"excerpt\": f\"this is description for {notebook.stem}\",\n", "        \"date\": publish_date.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "        \"price\": random.randint(1, 100)\n", "    })\n", "\n", "    first_cell = nb.cells[0]\n", "    first_cell[\"source\"] = first_cell[\"source\"][:200]\n", "    new_notebook = nbformat.v4.new_notebook(\n", "        cells = nb.cells[:1],\n", "        metadata = meta\n", "    )\n", "\n", "    print(f\"正在写入{notebook}\")\n", "    nbformat.write(new_notebook, notebook)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["({'title': 'SQL',\n", "  'author': '<PERSON>',\n", "  'date': '2021-08-09',\n", "  'except': 'this is ...'},\n", " '# A great title\\nhello, this is a test')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["txt = \"\"\"\n", "---\n", "title: \"SQL\"\n", "author: \"<PERSON>\"\n", "date: \"2021-08-09\"\n", "except: \"this is ...\"\n", "---\n", "\n", "# A great title\n", "hello, this is a test\n", "\"\"\"\n", "\n", "def process_front_matter(content: str) -> tuple[dict, str]:\n", "    post = frontmatter.loads(content)\n", "\n", "    title = post.metadata.get(\"title\", \"\")\n", "    date = post.metadata.get(\"date\", \"\")\n", "    if not date or not title:\n", "        raise ValueError(f\"必须为文章加上title和date\")\n", "\n", "    content = post.content\n", "    if not content.startswith(\"#\"):\n", "        content = f\"# {title}\\n\\n{post.content}\"\n", "    return post.metadata, post.content\n", "\n", "process_front_matter(txt)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["Postgres(pool=Pool(min_size=1, max_size=10), user='quantide', password='Quant1de', host='localhost', database='provision')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from provision.conf import cfg\n", "from provision.store.db import pg\n", "\n", "cfg.load_config()\n", "cfg.provision.postgres"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["pg.close()\n", "pg.connect(cfg)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 11:19:07.799\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: update customers set nickname = %s where account = %s\u001b[0m\n", "\u001b[32m2025-05-23 11:19:07.827\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: select * from customers where account = 'Allon'\u001b[0m\n"]}, {"data": {"text/plain": ["(7,\n", " 'Allon',\n", " 'e3a291feb9ac8aa9e3fcd4ac754b6c7a06174a616291f6ebf6cebc020625f205',\n", " 'aaron',\n", " 'b39713c54e58f01a18d951fa0e0e555a',\n", " '',\n", " 'llz17265',\n", " '',\n", " '',\n", " datetime.datetime(2025, 4, 25, 10, 33, 34, 672000),\n", " False)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = \"update customers set nickname = %s where account = %s\"\n", "pg.execute(sql, [\"aaron\", \"<PERSON><PERSON>\"])\n", "sql = \"select * from customers where account = 'Allon'\"\n", "pg.fetch_record(sql)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 11:14:35.377\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: select account, phone from customers\u001b[0m\n"]}, {"data": {"text/plain": ["('Allon', '')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = \"select account, phone from customers\"\n", "\n", "pg.fetch_record(sql)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 11:03:19.806\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: \n", "    INSERT INTO customers (nickname, account, phone, wx, occupation, channel, salt, password)\n", "    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)\n", "    RETURNING id\n", "\u001b[0m\n", "\u001b[32m2025-05-23 11:03:19.816\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m235\u001b[0m - \u001b[31m\u001b[1mSQL execution error: duplicate key value violates unique constraint \"customers_account_key\"\n", "DETAIL:  Key (account)=(again) already exists.\n", "\u001b[0m\n", "\u001b[32m2025-05-23 11:03:19.817\u001b[0m | \u001b[31m\u001b[1mERROR   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36mfetch_record\u001b[0m:\u001b[36m308\u001b[0m - \u001b[31m\u001b[1mFetch record error: duplicate key value violates unique constraint \"customers_account_key\"\n", "DETAIL:  Key (account)=(again) already exists.\n", "\u001b[0m\n"]}], "source": ["sql = \"\"\"\n", "    INSERT INTO customers (nickname, account, phone, wx, occupation, channel, salt, password)\n", "    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)\n", "    RETURNING id\n", "\"\"\"\n", "\n", "result = pg.fetch_record(sql, [\"fuck\", \"again\", \"12345dd678901\", \"wx\", \"occupation\", \"channel\", \"salt\", \"password\"], readonly=False)\n", "result"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 11:14:42.795\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: select * from customers\u001b[0m\n"]}, {"data": {"text/plain": ["[CustomerModel(nickname='aaron', account='<PERSON>on', password='e3a291feb9ac8aa9e3fcd4ac754b6c7a06174a616291f6ebf6cebc020625f205', phone='', wx='llz17265', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 33, 34, 672000), id=7, salt='b39713c54e58f01a18d951fa0e0e555a'),\n", " CustomerModel(nickname='李志跃', account='lizhiyue       ', password='37b9e3cdaf995a338afc229458fa9ee393ac1308564bcc81430b9e6d7d2e43c0', phone='', wx='zhiyu__li', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 20, 38, 702000), id=4, salt='29b1497a4a02a70c4748cc489ee4ea00'),\n", " CustomerModel(nickname='nelson', account='nelson', password='06f44a85981bdca65381d23d47cabe95dac286d1c5b48bcf5dc35f5edf93faf5', phone='', wx='Nelson_0x', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 34, 48, 810000), id=8, salt='c277bc8e926c86ddc6cbe47aca886fad'),\n", " CustomerModel(nickname=' 风雪之隅', account='maleicacid', password='823a546a10f58fe6582585f077f902f53678981a0f8a79383ce5c135c0c0bc96', phone='', wx='MaleicAcid', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 40, 4, 491000), id=10, salt='5ae394ad970bf4c8f4e01a5791a0af9d'),\n", " CustomerModel(nickname='<PERSON><PERSON>', account='muj', password='3eb7ad4d584c729ee77b7b701f14e28ae7c2a01a9593d2bba917c830ded881ba', phone='', wx='cmbmujun', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 41, 7, 92000), id=11, salt='1fde03907ab76b6ca4178900aad7b946'),\n", " CustomerModel(nickname='<PERSON><PERSON><PERSON>', account='pse', password='2af723089edfcc49c107ad4282434ce011836704bdcaa5e67b733bb1a463441e', phone='', wx='summer260316', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 42, 4, 990000), id=12, salt='e906dc22b4d2a0759515a22b0acd8aee'),\n", " CustomerModel(nickname='菠萝油老大爷', account='<PERSON>', password='c50230b90914c06d83a766278582822b0d712a4b7d7d29094f0019862e6d9381', phone='', wx='tj1354348', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 43, 47, 526000), id=13, salt='b7ef53622c87c9eb096ff755c557f4b5'),\n", " CustomerModel(nickname='spencer', account='spencer', password='f817958dfd63de2f2d272f1cfa0a17728ed2f88df0e908dd2825e02be90a0f2b', phone='', wx='gunmstart', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 47, 59, 765000), id=14, salt='e6d33b15747046388bd3132162b894e8'),\n", " CustomerModel(nickname='张连学', account='kautz', password='f84dc27285dd0ca18dbc20cd8153471cca99c0d408c83885e6397dc94b7f7f8d', phone='', wx='wxid_q210d4knc90k22', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 49, 20, 301000), id=15, salt='4f1532999311f7971599fe6004d2eabe'),\n", " CustomerModel(nickname='<PERSON><PERSON><PERSON>', account='CosmoSheep', password='c997c0010d94778fbdd4383827c169fa0c2fbc67de79a61807c02437d270b0cb', phone='', wx='NewTanc', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 50, 34, 816000), id=16, salt='a59b0ca3e710fcf7723a935a30c40e62'),\n", " CustomerModel(nickname='卡布', account='flauto', password='7f01396ede681700b3d82fa15628914980e87686aa75ff687ac56e75f44b99ee', phone='', wx='coflute', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 51, 57, 887000), id=17, salt='13fd5b3feda818238816b2d5942dc09a'),\n", " CustomerModel(nickname='黄超', account='andy', password='954f629dc23423154ac16617ad3e2ebb53a0730cfda5d47e6bae7a1427f23d96', phone='', wx='huangchaohuangchao', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 53, 5, 790000), id=18, salt='49449c74d088cef7685cfea83ebcb84b'),\n", " CustomerModel(nickname='烟波江南', account='micha<PERSON><PERSON>', password='617ddc596f0d6c92f2e45b8fca6338263cb794edb7cc30e5d782aa35c48a9166', phone='', wx='yanbo515', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 54, 1, 602000), id=19, salt='86d7801351a6f0ed5e15ca5413cd0abc'),\n", " CustomerModel(nickname='梁偲', account='aden', password='3d25a6e90811f0fb8a2f95c9b663f6208599585d5966de21c5a6eb901709c751', phone='', wx='lc284910196', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 58, 6, 840000), id=20, salt='25c9cdb9718ec022b9c235de6266b00f'),\n", " CustomerModel(nickname='Zeng', account='charles', password='fbf983939ec05d64ff08ecdba72120fa74b91faa4de98dcfef98d8591e0642d1', phone='', wx='Tsang-Zeng', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 0, 48, 731000), id=21, salt='ed0cf2275e8674f28ac4e22c36747670'),\n", " CustomerModel(nickname='handsomedu2002', account='handsomedu2002', password='a96bc1c5ab7f24b315344f5cc34fe42134a37d2e46f59920a0d39c18467ee41f', phone='', wx='TrueHeart2011', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 4, 30, 39000), id=23, salt='4b6cc3a3270a4b8446581e68f1e4cf7f'),\n", " CustomerModel(nickname='素栾', account='samuel', password='5f7f92940d0e0ef19c3087f01e6411a74477f55cd098cdfeecbafe0ead756508', phone='', wx='ljq_18851659283', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 5, 35, 373000), id=24, salt='9c76ed7385a07c9552d64d080a7e576c'),\n", " CustomerModel(nickname='徐杨', account='holaw0rld', password='29964c263777e6ba1617fe86fcd31970a2100ba995a432c40454ba4694afccb0', phone='', wx='holaw0rld', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 6, 56, 960000), id=25, salt='16df489fb72a40c2095283d966455732'),\n", " CustomerModel(nickname='royce', account='roy', password='2ce1fb7ee05e134efc89102f8fd6a1345be46511b6cd321d7280b7adfcf12f38', phone='', wx='roymego', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 8, 0, 765000), id=26, salt='34aafd58a6f309cbbe65babe271e441a'),\n", " CustomerModel(nickname='玉面肥龙', account='Haar', password='772d4032e65f5a1a77715a68ff252c96d1d3ee3293bad84bf0b2943dde720b9d', phone='', wx='huer499797', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 9, 15, 982000), id=27, salt='e0b4279e2ca604aeb3a6bb3191da6966'),\n", " CustomerModel(nickname='ty', account='tyoodz', password='bee62130a88edf576b243f52a5fb6278c5a8c7987b3e83bbd6424c51809435cf', phone='', wx='broccoli_0222', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 15, 20, 872000), id=29, salt='ea9949da346960a5027e0fed152a52cd'),\n", " CustomerModel(nickname='黄翊华', account='alberth', password='0b85ce8ec7cbbca0bf576be130d7aca99704ba32b0d70c92a718682fb146202b', phone='', wx='alberthuang1', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 10, 20, 991000), id=28, salt='ec22580ad18a84f7c74eb9c335fe8f9b'),\n", " CustomerModel(nickname='袁学东', account='even', password='439aa46f8d09039e0205f4c51a374944601a22556f9f5906ac175c3b52ec229a', phone='', wx='wxid_ocz8orn0pgt011', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 16, 58, 808000), id=30, salt='b9b35e7e25f5d8d15a834805d29c67f0'),\n", " CustomerModel(nickname='蓝田', account='quantblue888', password='8f880bb8f58b26aaac2bd188de331ff14bcdfd599e5589e86c7d05d654f8b9be', phone='', wx='rich_itman', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 18, 5, 361000), id=31, salt='e93b14ce3701be47a611f998ba57b992'),\n", " CustomerModel(nickname='王敦词', account='appu', password='29846472079dc22e3afff7e49132c7cf734d94269df20b3eb9bbf8704c18fd88', phone='', wx='wppdavis', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 20, 18, 497000), id=32, salt='8f321c4751c7e087d7b04137a9a975e4'),\n", " CustomerModel(nickname='<PERSON>杨', account='tim', password='91bfdca2133381a46a9d6a99b33e5c0931bd69375d23c09aca88a658a1e8497c', phone='', wx='yrc717', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 21, 10, 584000), id=33, salt='5f8017b403f5ed15b526968acb25b998'),\n", " CustomerModel(nickname='BrokenWind<PERSON>', account='broken', password='833a233ecbab37e269d47b304c4cf37c106841470f5b569bd786b452c6aa8083', phone='', wx=' broken_windows', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 22, 21, 537000), id=34, salt='cbadc177a1cba20d371748b45f1a9038'),\n", " CustomerModel(nickname='和光同尘', account='monodrama', password='a7d244e6144a6075d27029e9af3079a4dae9115e40e249db377d1d03b9aa902f', phone='', wx='wxid_indlq20fxjnl22', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 25, 14, 820000), id=36, salt='52c0ff49f79feebd98a770ef7aee080e'),\n", " CustomerModel(nickname='special', account='special', password='c83f13936d6467ede8533822e1600a7903be7b08fd4fcb493cae52602487b5ad', phone='', wx='GoldWuChen', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 26, 19, 810000), id=37, salt='8eb588307790544741fa9c2f7a6e173f'),\n", " CustomerModel(nickname='<PERSON>', account='stevenzhengzx', password='eb9d0ccbfd6e62faf8e25730e15bce781ed5639d269c89593eb58df3c8670230', phone='', wx='stevenzhengzx', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 28, 39, 333000), id=38, salt='331d41c5ff3d69aa13196c02a5334e78'),\n", " CustomerModel(nickname='小辉辉', account='xhh', password='4f5f7d907fd27f3761cf7034957553276a9c21a725a1cd184110a14187d7a1a5', phone='', wx='g38905554', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 29, 57, 882000), id=39, salt='47a3f5bfef44860cbf46036811229bce'),\n", " CustomerModel(nickname='根号负一', account='jrx', password='15d24818d113aa755c89bdbd6e77abdc9e9414df36242d3780c226f6766e3cdb', phone='', wx='jrx1298652367  ', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 32, 16, 735000), id=41, salt='01d0aeb92a578320ff7a69b1f7b1443e'),\n", " CustomerModel(nickname='peter_hsgou', account='peter', password='d324e6f28b870b13a8e6f2939f5cc504de5036f1364c330a843cc3c2edfd6eb6', phone='', wx='ghs1205', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 33, 42, 810000), id=42, salt='11b6f00860742bf5163833ea0ad47ede'),\n", " CustomerModel(nickname='樊秀峰', account='<PERSON>_<PERSON>', password='d9a72fb3f2b4270e66d0070c65fdf0adc3708f97adde7fb3d4fa8ec6292ede97', phone='', wx='wxid_zzbxralkvipn22', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 34, 59, 33000), id=43, salt='5eb8580511895048c337307e544a4ad2'),\n", " CustomerModel(nickname='沈先生', account='canny', password='7d3291bca9804317725b9a74388d5d4c943ba410474c831e1e3e28efc2f9d2a5', phone='', wx='scanheart', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 36, 42, 681000), id=44, salt='5c92b3c7d4641ea975e28b91ea848d09'),\n", " CustomerModel(nickname='chen', account='lizhiyue', password='757e177723c0012b86f5e586c86f4655cfa753669f4b91ffb3c31d4763ba57fc', phone='', wx='zhiyu__li', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 35, 35, 456000), id=9, salt='fec23d7687fade0f1941841599c47c82'),\n", " CustomerModel(nickname='陈凯', account='mhqb ', password='e2ef671c7795a71fe14ac1901644aee9e50aa13bca896cb49f0cbf043605e5bc', phone='', wx='mhqbck', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 38, 24, 492000), id=46, salt='6827cdbfdacf31f8bfc1c2d8c363f358'),\n", " CustomerModel(nickname='章', account='zenki', password='f3c13a009edd63d885a4ee5c9d8cb4a1555696739ae3912885fe68260f48c97c', phone='', wx='zenki229', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 39, 34, 656000), id=47, salt='05b113dabec70e9f45da8e6d7e810b18'),\n", " CustomerModel(nickname='freddy', account='Ra<PERSON><PERSON>', password='e0a685e4aba95d5ea914d5a832604ba6100e215cb5ee9e62ca97e251a49bb462', phone='', wx='wdf272726', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 40, 17, 408000), id=48, salt='de85fe43da7168ed3fb09423b889b38a'),\n", " CustomerModel(nickname='罗升华', account='<PERSON><PERSON><PERSON><PERSON>', password='4098d1b152ef6bf35f84e5cd49b4ddc18f0f974247350bceb51f2f39e02674a0', phone='', wx='wxid_8e0he8k63qed22', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 41, 21, 367000), id=49, salt='bea5960e95b6001784da415b8fe21f74'),\n", " CustomerModel(nickname='周先生', account='dirk', password='2dfa394e863c35b2ed3d9c2814d8557bd2aa4d2c9f519cc6e98ea01068713e4d', phone='', wx='DIEK4141414141    ', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 37, 35, 289000), id=45, salt='7ddd69d5824c1f448e2f3d51a344408f'),\n", " CustomerModel(nickname='aaron', account='aaron', password='dfa48c9d2234162c9a69ff3fd244e431d94aebfd3801c56ac307dd7d2078eb9d', phone='', wx='aaron', channel='', occupation='', register_time=datetime.datetime(2025, 4, 24, 17, 24, 46, 259000), id=1, salt='9a2bf8bc38ad87e8f080cd85a17d3f52'),\n", " CustomerModel(nickname='壳怼怼', account='becky', password='c4a0939b4a3eee78c66177327dbfd0c8efdeba5a6feae0d89d07abf5520e4373', phone='', wx='BeQueen0222', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 30, 58, 905000), id=40, salt='154ed0fea4839b3321f4dbaa4841d698'),\n", " CustomerModel(nickname='金文', account='BarretX', password='ab8c7f037c19fce77d02d2a76b55ab9d793a3a8862e746dcc30b48f525ce9c0b', phone='', wx='X_AI_CHINA', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 14, 32, 394000), id=2, salt='dcc7bce62b2c35850132280566101726'),\n", " CustomerModel(nickname='Wing Of World', account='wing', password='370057536c6fd2c8ae86925125b659620f9530db391bfc531a2b56e7471ae3f8', phone='', wx='wing_of_world', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 28, 4, 702000), id=5, salt='22edd5574f797c9e254360dba330e574'),\n", " CustomerModel(nickname='easyranger', account='easyranger', password='b94db9dd4fe369122628ab456b04fc463ff345972ab5eccb6bdeb4c0a2545d9c', phone='', wx='sunlecn', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 18, 21, 193000), id=3, salt='a8004f313482ed578ab20bb9ddb922b1'),\n", " CustomerModel(nickname='ning', account='ning', password='00b7a026d467847068e173fd775ceef093e64260810dfbdf2b196d32cffa28db', phone='', wx='mayening111', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 10, 31, 32, 251000), id=6, salt='5e2aa8ffff7130e0de47994338267814'),\n", " CustomerModel(nickname='因次递尽', account='rocky', password='5a5ff7812b51e0b8d96f18fa2209ec34504287524af15b082926ed7c783170a3', phone='', wx='wxid_d2opk2hbhoyg31', channel='', occupation='', register_time=datetime.datetime(2025, 4, 28, 9, 59, 8, 685000), id=50, salt='7cfa0d225c5ac9a86e13b845beb3a463'),\n", " CustomerModel(nickname='paul.lu', account='paul', password='8bfaee014ffebdde155ecc402589ddbd1afbf614b1870cc7e024bc762df3d39b', phone='', wx='bluemeteor', channel='', occupation='', register_time=datetime.datetime(2025, 4, 28, 10, 0, 26, 650000), id=51, salt='0474b5e8118fb02c4b352ab26fccc333'),\n", " CustomerModel(nickname='和气致祥', account='uiuahekt', password='af8c28306d26e51c48083c4e2820797e8fc2dff5b7088c4c3b72e744d8ed0be9', phone='', wx='wjt1023096', channel='', occupation='', register_time=datetime.datetime(2025, 4, 28, 10, 1, 57, 750000), id=52, salt='e72d470563a01e115bb8fd1f0fe978eb'),\n", " CustomerModel(nickname='甲鱼同学', account='michael', password='f507284fc5d28ab42a3015a7e077fd7b9bda0bc34175f40d742e35aa44e55bb1', phone='', wx='truelet_t', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 23, 11, 936000), id=35, salt='a13614f858004cd109a6bb11dd52ec68'),\n", " CustomerModel(nickname='H', account='ichenrong', password='f9df7d0676c6671ddb3c089938300ee8f923df1bbb6ffc0a6d31ee8ef725d65f', phone='', wx=' ichenrong', channel='', occupation='', register_time=datetime.datetime(2025, 4, 25, 11, 1, 53, 251000), id=22, salt='3976382c06e8489b48da8d5143af02d6'),\n", " CustomerModel(nickname='fuck', account='again', password='password', phone='12345dd678901', wx='wx', channel='channel', occupation='occupation', register_time=datetime.datetime(2025, 5, 22, 13, 46, 4, 979319), id=63, salt='salt')]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from provision.store.customer import CustomerModel\n", "\n", "pg.fetchall(CustomerModel, \"select * from customers\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 11:19:15.718\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: select count(*) from customers where id=999\u001b[0m\n", "\u001b[32m2025-05-23 11:19:15.731\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: select count(*) from customers where id=999\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(0,)\n", "0\n"]}], "source": ["sql = \"select count(*) from customers where id=999\"\n", "print(pg.fetch_record(sql))\n", "print(pg.fetch_item(sql, \"count\"))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 15:12:08.683\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: SELECT DISTINCT account FROM customers ORDER BY account limit 3\u001b[0m\n"]}, {"data": {"text/plain": ["['aaron', 'aden', 'again']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = \"SELECT DISTINCT account FROM customers ORDER BY account limit 3\"\n", "pg.fetch_items(sql, \"account\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-05-23 16:40:30.482\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36mprovision.store.db\u001b[0m:\u001b[36m_execute\u001b[0m:\u001b[36m228\u001b[0m - \u001b[34m\u001b[1mExecuting SQL: insert into messages (recipient_id, sender_id, title, content) values (%s, %s, %s, %s) returning id\u001b[0m\n"]}, {"data": {"text/plain": ["4"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = \"insert into messages (recipient_id, sender_id, title, content) values (%s, %s, %s, %s) returning id\"\n", "\n", "args = [\n", "    1, 1, \"hello\", \"this is a test\"\n", "]\n", "result = pg.fetch_item(sql, \"id\", args, readonly=False)\n", "result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0.3迁移"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# 修改registry.csv，将 grace_end 列改为 retention_end， 增加division = course列。\n", "df = pd.read_csv(\"/tmp/registry.csv\")\n", "df.rename(columns={\"grace_end\": \"retention_end\"}, inplace=True)\n", "df[\"division\"] = \"course\"\n", "df.to_csv(\"/tmp/registry_new.csv\", index=False, header=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "provision", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}