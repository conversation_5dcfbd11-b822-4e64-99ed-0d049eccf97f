"""Tests for the customer module."""

from unittest.mock import MagicMock, patch

import pytest

from provision.store.customer import CustomerModel


@pytest.fixture
def mock_pg_conn():
    """Mock pg.conn for testing."""
    with patch("provision.store.customer.pg") as mock_pg:
        # 创建一个模拟的游标
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = (1,)

        # 创建一个模拟的连接
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor

        # 设置 pg.conn 的返回值
        mock_pg.conn = mock_conn

        yield mock_pg


def test_check_account_exists(mock_pg_conn):
    """Test check_account_exists method."""
    # 设置 fetchone 的返回值为 (1,)，表示账号存在
    mock_cursor = mock_pg_conn.conn.cursor.return_value
    mock_cursor.fetchone.return_value = (1,)

    # 调用方法
    result = CustomerModel.check_account_exists("test_account")

    # 验证结果
    assert result is True

    # 验证调用
    mock_pg_conn.conn.cursor.assert_called()
    mock_cursor.execute.assert_called_once_with(
        "SELECT COUNT(*) FROM customers WHERE account = %s", ["test_account"]
    )

    # 设置 fetchone 的返回值为 (0,)，表示账号不存在
    mock_cursor.fetchone.return_value = (0,)

    # 调用方法
    result = CustomerModel.check_account_exists("test_account")

    # 验证结果
    assert result is False


def test_check_phone_exists(mock_pg_conn):
    """Test check_phone_exists method."""
    # 设置 fetchone 的返回值为 (1,)，表示手机号存在
    mock_cursor = mock_pg_conn.conn.cursor.return_value
    mock_cursor.fetchone.return_value = (1,)

    # 调用方法
    result = CustomerModel.check_phone_exists("***********")

    # 验证结果
    assert result is True

    # 验证调用
    mock_pg_conn.conn.cursor.assert_called()
    mock_cursor.execute.assert_called_once_with(
        "SELECT COUNT(*) FROM customers WHERE phone = %s", ["***********"]
    )

    # 设置 fetchone 的返回值为 (0,)，表示手机号不存在
    mock_cursor.fetchone.return_value = (0,)

    # 调用方法
    result = CustomerModel.check_phone_exists("***********")

    # 验证结果
    assert result is False

    # 测试空手机号
    result = CustomerModel.check_phone_exists("")

    # 验证结果
    assert result is False


def test_deactivate_account(mock_pg_conn):
    """Test deactivate_account method."""
    # 设置 fetchone 的返回值为 (1,)，表示账号存在且处于激活状态
    mock_cursor = mock_pg_conn.conn.cursor.return_value
    mock_cursor.fetchone.return_value = (1,)

    # 调用方法
    result = CustomerModel.deactivate_account("test_account")

    # 验证结果
    assert result is True

    # 验证调用
    mock_cursor.execute.assert_any_call(
        "SELECT COUNT(*) FROM customers WHERE account = %s AND is_active = TRUE",
        ["test_account"],
    )
    mock_cursor.execute.assert_any_call(
        "UPDATE customers SET is_active = FALSE WHERE account = %s", ["test_account"]
    )
    mock_pg_conn.conn.commit.assert_called_once()

    # 设置 fetchone 的返回值为 (0,)，表示账号不存在或已经注销
    mock_pg_conn.conn.reset_mock()
    mock_cursor.reset_mock()
    mock_cursor.fetchone.return_value = (0,)

    # 调用方法
    result = CustomerModel.deactivate_account("test_account")

    # 验证结果
    assert result is False

    # 验证调用
    mock_cursor.execute.assert_called_once_with(
        "SELECT COUNT(*) FROM customers WHERE account = %s AND is_active = TRUE",
        ["test_account"],
    )
    mock_pg_conn.conn.commit.assert_not_called()
