server:
  port: 8403
  host: "0.0.0.0"
  secret: "a3c18565d92aa5eb1fb615d9a4fcbd08"
  domain: test.quantide.cn
  url_prefix: "http://localhost:8080/"
  logging:
    handlers:
      - sink: /var/log/provision/app.log
        level: INFO
        format: "<level>{level: <8}</level> <green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> - <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        rotation: "10 MB"
accounts:
  preview: preview
  share: quantide
database:
  # 如果是相对路径，则相对于provision config home
  path: provision.db
nginx:
  ports:
    - 8080: 80
    - 8443: 443
  name: nginx
  network: course

# JupyterLab 配置
jupyterlab:
  prefix: ["/lab/tree", "/api/contents"]

# 管理员用户配置
admin:
  users:
    - username: admin
      password: Quant!de
    - username: manager
      password: Quant!de

# 阿里云短信服务配置
sms:
  access_key_id: "your_access_key_id"
  access_key_secret: "your_access_key_secret"
  sign_name: "量化课程"
  template_code: "SMS_123456789"
  endpoint: dysmsapi.aliyuncs.com

