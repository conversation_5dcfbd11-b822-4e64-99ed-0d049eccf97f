fa:
  plans: [bronze, silver, gold_a, gold_b, platinum, diamond]
  resources: [courseware, assignments]
  own_container: [platinum, diamond]
  # 专属容器过期后的保留时间
  retention: 30
  refund: 15
  initial: 3
  # 在refund期间，多久（天数）释放一个资源
  release_every: 2
  exclude:
    bronze:
      - assignments
      - courseware/19.ipynb
      - courseware/20.ipynb
    silver:
      - courseware/19.ipynb
      - courseware/20.ipynb
    gold_a:
      - courseware/19.ipynb
      - assignments/questions/19.ipynb
      - assignments/answers/19.ipynb
    gold_b:
      - courseware/20.ipynb
      - assignments/questions/20.ipynb
      - assignments/answers/20.ipynb
  prime:
    bronze: 60
    silver: 90
    gold_a: 120
    gold_b: 120
    platinum: 365
    diamond: 365
  grace:
    bronze: 305
    silver: 305
    gold_a: 365
    gold_b: 365
    platinum: 365
    diamond: 365

pandas:
  own_container: []
  plans: [bronze]
  resources: [courseware]
  refund: 15
  initial: 3
  progress: 1/2d
  prime:
    bronze: 60
  grace:
    bronze: 365
blog:
  own_container: []
  plans: [bronze]
  resources: [articles]
  refund: 15
  initial: 3
  progress: 1/2d
  prime:
    bronze: 365
  grace:
    bronze: 30
