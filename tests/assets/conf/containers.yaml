# 在copy项下的src中的绝对路径意味着从$home目录开始
# dst中 ~ 表示容器内的课程目录，即/home/<USER>/notebooks
# cmd中的命令，都运行在容器中
## 目录映射语法
# all是所有人应该加载的目录, student是学生应该加载的目录
    # 如果mode省略，则为ro
    # 如果to省略，则为~/notebooks/{from}
l24:
  container:
    image: l24
    volumes:
      all:
        - from: courseware
        - from: assignments
        - from: supplements
        - from: 课程指南.ipynb
        - from: notebook入门.ipynb
        - from: notebook高级技巧.ipynb
        - from: .config/__init__.py
          to: /usr/local/lib/python3.8/dist-packages/coursea/__init__.py
    postrun:
      cmd:
        - pip install tushare
fa:
  container:
    image: fav2
    volumes:
      all:
        - from: courseware
        - from: assignments
        - from: supplements
        - from: 课程指南.ipynb
        - from: notebook入门.ipynb
        - from: notebook高级技巧.ipynb
      platinum:
        - from: .config/kernel.json
          to: /opt/miniconda/share/jupyter/kernels/python3/kernel.json
    postrun: []
pandas:
  container:
    image: fav2
    volumes:
      all:
        - from: courseware
        - from: 使用说明.ipynb
        - from: notebook入门.ipynb
        - from: notebook高级技巧.ipynb
    postrun:
      cmd:
        - pip install termcolor
