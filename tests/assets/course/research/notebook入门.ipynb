{"cells": [{"cell_type": "markdown", "id": "9fba2b7a", "metadata": {}, "source": ["# NOTEBOOK 入门\n", "\n", "Jupyter Notebook 是一种**交互式编程环境**，或称**探索式编程环境**。它由网页和服务器组成。\n", "\n", "网页由一个个单元格组成，在单元格里编写代码，或者描述性文字。当我们执行代码单元格时，单元格的代码会被发送到后台服务器运行，输出结果再传送回浏览器，显示在对应的代码单元格下方。\n", "\n", "这种方式特别适合于数据科学家。作为从事数据分析的人，他们往往需要先加载一部"]}], "metadata": {"date": "2025-07-28 13:43:12", "excerpt": "this is description for notebook入门", "price": 12, "title": " NOTEBOOK 入门"}, "nbformat": 4, "nbformat_minor": 5}