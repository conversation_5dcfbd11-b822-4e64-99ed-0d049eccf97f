{"cells": [{"cell_type": "markdown", "id": "01e3352c", "metadata": {}, "source": ["# 单因子检验的原理及实现\n", "\n", "因子检验通常有回归法、IC 法和分层回溯法等。这一章先是介绍各自的基本概念，然后再实现一个单因子检验的全流程。最后，我们给出三种方法的区别与联系。\n", "\n", "通过这一章及前一章的介绍，我们将理解因子检验中的大部分流程、方法及背后理论，这对后面我们学习 Alphalens 框架有很大的帮助。\n", "\n", "## 1. 回归法\n", "回归法的思想非常直观。它是将因子在第 T 期的暴露度与 T + "]}], "metadata": {"date": "2025-06-16 13:43:05", "excerpt": "this is description for 03", "price": 90, "title": " 单因子检验的原理及实现"}, "nbformat": 4, "nbformat_minor": 5}