{"cells": [{"cell_type": "markdown", "id": "7cc55257", "metadata": {}, "source": ["#  因子生成与预处理\n", "\n", "在上一章，我们给出了因子分析的一般框架，即包括数据获取、因子生成、因子预处理及单因子检验等基本步骤。有一些开源库已经实现了这些功能，比如 Quantopian 的 Alphalens[<sup>[1]</sup>](#alphalens) 和聚宽的 JQFactor[<sup>[2]</sup>](#jqfactor)。\n", "\n", "在本课程中我们将介绍 Alphalens 框架。"]}], "metadata": {"date": "2025-07-18 13:43:05", "excerpt": "this is description for 02", "price": 43, "title": "  因子生成与预处理"}, "nbformat": 4, "nbformat_minor": 5}