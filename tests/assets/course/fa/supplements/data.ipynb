{"cells": [{"cell_type": "markdown", "id": "a5cbee9e", "metadata": {}, "source": ["# 如何获取数据？\n", "\n", "此 Notebook 将演示如何在本环境下获取数据。我们在全局名字空间下提供了一些辅助函数。"]}], "metadata": {"date": "2025-05-08 13:43:05", "excerpt": "this is description for data", "kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "price": 89, "title": " 如何获取数据？"}, "nbformat": 4, "nbformat_minor": 5}