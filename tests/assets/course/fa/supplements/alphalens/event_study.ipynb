{"cells": [{"cell_type": "markdown", "id": "a421decd", "metadata": {"deletable": true, "editable": true}, "source": ["# Alphalens: event study"]}], "metadata": {"date": "2025-07-31 13:43:05", "excerpt": "this is description for event_study", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}, "price": 23, "title": " Alphalens: event study"}, "nbformat": 4, "nbformat_minor": 5}