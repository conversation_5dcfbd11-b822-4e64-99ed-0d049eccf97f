{"cells": [{"cell_type": "markdown", "id": "ffb67bc1", "metadata": {"deletable": true, "editable": true}, "source": ["# Synthetic data examples"]}], "metadata": {"date": "2025-07-06 13:43:05", "excerpt": "this is description for event_study_synthetic_data", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.5.2"}, "price": 13, "title": " Synthetic data examples"}, "nbformat": 4, "nbformat_minor": 5}