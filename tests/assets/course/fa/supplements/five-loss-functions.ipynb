{"cells": [{"cell_type": "code", "execution_count": 1, "id": "313a088b", "metadata": {}, "outputs": [], "source": ["%matplotlib inline"]}], "metadata": {"date": "2025-05-16 13:43:05", "excerpt": "this is description for five-loss-functions", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}, "price": 34, "title": "%matplotlib inline", "toc": {"nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 5}