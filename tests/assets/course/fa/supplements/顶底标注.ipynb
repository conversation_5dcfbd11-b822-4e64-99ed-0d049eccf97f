{"cells": [{"cell_type": "markdown", "id": "47868a24-49dd-48ab-b48d-573635c89d56", "metadata": {}, "source": ["如果没有coursea环境，可以使用外部数据。\n", "依赖：omicron.plotting.candlestick可以在https://zillionare.github.io/omicron/2.0.0a78/api/plotting/candlestick/找到源码"]}], "metadata": {"date": "2025-07-08 13:43:05", "excerpt": "this is description for 顶底标注", "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "price": 72, "title": "如果没有coursea环境，可以使用外部数据。"}, "nbformat": 4, "nbformat_minor": 5}