import datetime
import itertools
import logging
from typing import Any, List, Tuple

import backtrader as bt
import hdbscan
import numpy as np
import pandas as pd
import pyfolio as pf
import statsmodels.api as sm
import statsmodels.tsa.stattools as ts
from backtrader.feeds import PandasData
from freezegun import freeze_time
from numpy.typing import NDArray
from sklearn.decomposition import PCA
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s %(message)s", datefmt="%Y/%m/%d"
)
logger = logging.getLogger("pairtrade")


def is_notebook():
    try:
        shell = get_ipython().__class__.__name__
        if shell == "ZMQInteractiveShell":
            return True  # Jupyter notebook or qtconsole
        elif shell == "TerminalInteractiveShell":
            return False  # Terminal running IPython
        else:
            return False  # Other type (?)
    except NameError:
        return False  # Probably standard Python interpreter


class PairTradingStrategy(bt.Strategy):
    params = dict(half_life_thresh=10, periods=250, delta=2)

    def __init__(self):
        # status: 0 未持仓 -1 做多pair1, 做空pair2中；1 做空pair1, 做多pair2中
        self.status = 0

        self.pair1 = self.datas[0]
        self.pair2 = self.datas[1]

        log_price1 = np.log(list(self.pair1.close)[: self.p.periods])
        log_price2 = np.log(list(self.pair2.close)[: self.p.periods])

        # 计算对数价格的hedge_ratio
        hr_log = self.hedge_ratio(log_price1, log_price2)

        # 计算spread
        self.spreads = (log_price2 - hr_log * log_price1).tolist()

        self.mean_spread = np.mean(self.spreads)
        self.std_spread = np.std(self.spreads)

    def log(self, msg, *args, **kwargs):
        """日志记录工具，支持自动记录交易时间，而不是执行回测时的系统时间"""
        dt = kwargs.get("dt")
        if dt is None:
            dt = self.datas[0].datetime.datetime()
        else:
            dt = bt.num2date(dt)
            del kwargs["dt"]

        with freeze_time(dt):
            logger.info(msg, *args, **kwargs)

    def notify_order(self, order):
        if order.status in [bt.Order.Submitted, bt.Order.Accepted]:
            return  # Await further notifications

        if order.status == order.Completed:
            name = order.data._name
            price = order.executed.price
            size = order.executed.size

            if order.isbuy():
                self.log("买入 %s(%s, %s)", name, price, size, dt=order.executed.dt)
            else:
                self.log("卖出 %s(%s, %s)", name, price, size, dt=order.executed.dt)

    def notify_trade(self, trade):
        if trade.isclosed:
            name = trade.data._name
            price = trade.price
            size = trade.size
            pnl = trade.pnlcomm
            self.log(
                "交易结束 %s(%s, %s, %s)", name, price, size, pnl, dt=trade.dtclose
            )

    @classmethod
    def reduce_dimension(
        cls, features: pd.DataFrame, variance: float = 0.999
    ) -> pd.DataFrame:
        """对特征数据进行降维

        Args:
            features (pd.DataFrame): 特征数据，要求索引为样本id（比如symbol）,特征为列
            variance: 降维时保留的方差百分比。
        returns:
            pd.DataFrame: 降维后的特征数据，索引为样本id，列为降维后的主成分
        """
        scaler = StandardScaler()
        pca = PCA(n_components=variance)
        pipeline = Pipeline([("scaler", scaler), ("pca", pca)])
        reduced = pipeline.fit_transform(features)
        reduced = pd.DataFrame(data=reduced, index=features.index)

        logger.info("降维效果：%s/%s", pca.n_components_, features.shape[1])
        return reduced

    @classmethod
    def hdbscan_clustering(
        cls,
        features: pd.DataFrame,
        min_clusters: int = 3,
        min_samples: int = 2,
        reduce_before_clustering: bool = False,
        **kwargs,
    ) -> List[Any]:
        """按输入的特征，运用hdbscan进行聚类

        Args:
            features: 特征数据，要求索引为样本id（比如symbol）,特征为列
            min_clusters: 最小分类数
            min_samples: 分类中的最小样本数
            reduce_before_clustering: 是否要在聚类前进行降维
            kwargs: 如果reduce_before_clustering为真，则接受variance参数，表示降维保留的方差百分比
        Returns:
            返回聚类结果，key为聚类编号，value为该聚类中的样本id列表（即feature.index）
        """
        if reduce_before_clustering:
            variance = kwargs.get("variance", 0.999)
            features = cls.reduce_dimension(features, variance)

        clusterer = hdbscan.HDBSCAN(
            min_cluster_size=min_clusters, min_samples=min_samples
        )
        labels = clusterer.fit_predict(features)
        df = pd.DataFrame(labels, columns=["label"], index=features.index)
        valid = df.query("label != -1")
        result = valid.groupby("label").apply(lambda x: x.index.tolist())

        return sorted(result, key=lambda item: len(item))

    @classmethod
    def hedge_ratio(
        cls, price1: NDArray | pd.Series, price2: NDArray | pd.Series, use_log=False
    ) -> float:
        """计算两个序列的对冲比率

        Args:
            price1 (NDArray): 序列一
            price2 (NDArray): 序列二
            use_log (bool, optional):当为True时，计算对数价格的对冲比率. Defaults to False.

        Returns:
            float: 对冲比率
        """
        if use_log:
            price1 = np.log(price1)
            price2 = np.log(price2)

        if isinstance(price1, pd.Series):
            price1 = price1.values  # type: ignore

        if isinstance(price2, pd.Series):
            price2 = price2.values  # type: ignore

        X = sm.add_constant(price1)
        model = sm.OLS(price2, X).fit()
        if isinstance(model.params, pd.Series):
            return model.params.iloc[1]
        else:
            return model.params[1]

    @classmethod
    def halflife_and_hurst(cls, spreads: NDArray | pd.Series, max_lag: int = 20):
        """计算spreads序列的hurst exponent指数和半衰期
            传入的spreads必须能通过adfuller检验。
            本算法来自https://github.com/bartchr808/Quantopian_Pairs_Trader/tree/master及
            https://www.quantstart.com/articles/Basics-of-Statistical-Mean-Reversion-Testing/

        Args:
            spreads: hedge之后的残差序列
            max_lag: 最大自相关延时跨度

        """
        if isinstance(spreads, pd.Series):
            spreads = spreads.values  # type: ignore

        # hurst
        lags = range(2, max_lag)
        tau = [
            np.sqrt(np.std(np.subtract(spreads[lag:], spreads[:-lag]))) for lag in lags
        ]

        hurst, *_ = np.polyfit(np.log10(lags), np.log10(tau), 1)
        hurst *= 2

        # 半衰期
        lag = np.roll(spreads, 1)
        lag[0] = 0
        ret = spreads - lag
        ret[0] = 0

        lag2 = sm.add_constant(lag)
        model = sm.OLS(ret, lag2)
        res = model.fit()
        phi = res.params[1]

        if phi >= 0:
            raise ValueError(f"phi（{phi}）应该小于零。请确保spreads为平稳序列")
        half_life = -np.log(2) / phi

        return half_life, hurst

    @classmethod
    def find_pairs(cls, barss: pd.DataFrame, clusters: List[List[str]]) -> pd.DataFrame:
        pairs = []
        for stocks in clusters:
            if len(stocks) < 2:
                continue  # 至少需要两只股票进行协整性检验

            for pair in itertools.combinations(stocks, 2):
                stock1, stock2 = pair

                # 获取两只股票的价格序列
                price1 = barss.xs(stock1, level=1).close.ffill().dropna()
                price2 = barss.xs(stock2, level=1).close.ffill().dropna()

                min_common_len = min(len(price1), len(price2))
                price1 = price1[-min_common_len:].values
                price2 = price2[-min_common_len:].values

                _, coint_p_value, *_ = ts.coint(price1, price2)

                if coint_p_value >= 0.05:  # 非协整对
                    continue

                # 计算spreads，进一步检验协整性
                hr = cls.hedge_ratio(price1, price2)

                spreads = price2 - hr * price1
                _, p_value, *_ = ts.adfuller(spreads)

                if p_value >= 0.05:  # 残差序列不平稳
                    continue

                try:
                    half_life, hurst = cls.halflife_and_hurst(spreads)
                except ValueError:
                    continue

                pairs.append((stock1, stock2, hr, hurst, half_life, coint_p_value))

                if len(pairs) >= 10:
                    break

        return pd.DataFrame(
            pairs,
            columns=[
                "pair1",
                "pair2",
                "hedge_ratio",
                "hurst",
                "half_life",
                "coint_p_value",
            ],
        )

    def close_position(self, reason: str):
        if self.status != 0:
            self.order_target_percent(self.pair1, target=0)
            self.order_target_percent(self.pair2, target=0)
            self.log(reason)

        self.status = 0

    def next(self):
        if len(self.pair1) < self.p.periods:
            return

        # 获取最新数据
        price1 = np.array(self.pair1.close.get(size=self.p.periods))
        price2 = np.array(self.pair2.close.get(size=self.p.periods))

        hr = self.hedge_ratio(price1, price2)
        spreads = price2 - hr * price1
        halflife, hurst = self.halflife_and_hurst(spreads)

        # 不满足协整条件时，不开仓
        if self.status == 0 and (hurst >= 0.5 or halflife >= self.p.half_life_thresh):
            return

        mean_spread = np.mean(spreads)
        std_spread = np.std(spreads)

        spread = spreads[-1]
        self.spreads.append(spread)

        # self.log("spread %s, mean %s, std %s, upper %s, lower %s", spread, mean_spread, std_spread, mean_spread + self.p.delta * std_spread, mean_spread - self.p.delta * std_spread)

        # 出场条件：spread从上方回归
        if self.status == 1 and spread < mean_spread:
            self.close_position("spread从上方回归，退出")
            return

        # 出场条件：spread从下方回归
        if self.status == -1 and spread > mean_spread:
            self.close_position("spread从上方回归，退出")
            return

        if self.status != 0:
            return

        # 入场条件：如果spread大于均值 delta 倍标准差，则做多pair1，做空pair2
        if self.status == 0 and (spread > mean_spread + self.p.delta * std_spread):
            self.log("入场条件1")
            self.order_target_percent(self.pair1, target=0.5)
            self.order_target_percent(self.pair2, target=-0.5)
            self.status = 1

        # 入场条件：如果spread小于均值 delta 倍标准差，则做空pair1，做多pair2
        if self.status == 0 and (spread < mean_spread - self.p.delta * std_spread):
            self.log("入场条件2")
            self.order_target_percent(self.pair1, target=-0.5)
            self.order_target_percent(self.pair2, target=0.5)
            self.status = -1

    @classmethod
    def backtest(
        cls,
        universe: List[str] | int,
        start: datetime.datetime,
        end: datetime.datetime,
        cash: float = 1_000_000,
        commission: float = 1e-4,
        periods: int = 250,
        delta: float = 1,
        half_life_thresh=10,
    ):
        cerebro = bt.Cerebro()

        barss = load_bars(start, end, universe)
        closes = barss["close"].unstack().ffill().dropna(axis=1, how="any")

        clusters = cls.hdbscan_clustering(closes.T)
        cls.pairs = cls.find_pairs(barss, clusters)

        if len(cls.pairs) == 0:
            raise ValueError("No cointegrated pairs found")

        pair1, pair2 = cls.pairs.iloc[0][["pair1", "pair2"]]
        data = PandasData(dataname=barss.xs(pair1, level=1))
        cerebro.adddata(data, name=pair1)

        data = PandasData(dataname=barss.xs(pair2, level=1))
        cerebro.adddata(data, name=pair2)

        cerebro.addstrategy(
            PairTradingStrategy,
            periods=periods,
            delta=delta,
            half_life_thresh=half_life_thresh,
        )
        cerebro.broker.set_cash(cash)
        cerebro.broker.setcommission(commission)

        if is_notebook():
            cerebro.addanalyzer(bt.analyzers.PyFolio)
        return cerebro.run(), cerebro


if not is_notebook():

    def load_bars(
        start: datetime.date, end: datetime.date, universe: List[str]
    ) -> pd.DataFrame:
        raise NotImplementedError("load_bars函数需要自行实现")


strategies, cerebro = PairTradingStrategy.backtest(
    500,
    datetime.date(2021, 1, 1),
    datetime.date(2023, 12, 31),
    delta=0.5,
    half_life_thresh=20,
)


if is_notebook():
    pyfoliozer = strategies[0].analyzers.getbyname("pyfolio")
    returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()
    pf.tears.create_full_tear_sheet(
        returns,
        positions=positions,
        transactions=transactions,
    )
else:
    cerebro.plot()
