{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a0cbf605", "metadata": {}, "outputs": [], "source": ["import backtrader as bt\n", "import pandas as pd\n", "import numpy as np\n", "import datetime\n", "from copy import deepcopy\n", "from datas import *"]}], "metadata": {"date": "2025-05-14 13:43:05", "excerpt": "this is description for 2.backtrader", "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "price": 22, "title": "import backtrader as bt", "vscode": {"interpreter": {"hash": "e42634819b8c191a5d07eaf23810ff32516dd8d3875f28ec3e488928fbd3c187"}}}, "nbformat": 4, "nbformat_minor": 5}