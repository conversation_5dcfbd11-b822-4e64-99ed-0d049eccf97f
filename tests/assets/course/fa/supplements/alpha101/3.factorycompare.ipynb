{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b0e515d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 10.6% in forward returns computation and 89.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3121468474961446, 'return_min': -0.018882862287128432, 'ic_mean': 0.028515156236632932, 'ic_std': 0.08680884666529107, 'ir': 0.3284821459105296}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.49064536603582454, 'return_min': 3.6875102331590526, 'ic_mean': 0.001101337567051293, 'ic_std': 0.07559754754016347, 'ir': 0.014568429835189751}\n", "Dropped 66.1% entries from factor data: 8.0% in forward returns computation and 58.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 66.1%, consider increasing it.\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.490795559075391, 'return_min': -11.488267110468797, 'ic_mean': 0.06377645167770069, 'ic_std': 0.1309196307482585, 'ir': 0.487142006994616}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.7191270887596524, 'return_min': 0.9881940088507513, 'ic_mean': 5.100519508620071e-05, 'ic_std': 0.11607344127675058, 'ir': 0.000439421753375869}\n", "Dropped 93.4% entries from factor data: 12.7% in forward returns computation and 80.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 93.4%, consider increasing it.\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6646228362416906, 'return_min': -1.1491490498782841, 'ic_mean': 0.027596951366075408, 'ic_std': 0.13866768402398377, 'ir': 0.19901501608191766}\n", "Dropped 6.6% entries from factor data: 6.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.452166769357007, 'return_min': 1.8680210565569233, 'ic_mean': 0.012974464611177693, 'ic_std': 0.1537018950365591, 'ir': 0.08441317270741278}\n", "Dropped 6.6% entries from factor data: 6.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.177980926208251, 'return_min': 3.4807302803763207, 'ic_mean': 0.0034684462359063505, 'ic_std': 0.1463252468456798, 'ir': 0.02370367595938045}\n", "Dropped 7.0% entries from factor data: 7.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.9353591730442155, 'return_min': -1.8990000291696507, 'ic_mean': -0.0008299218150311156, 'ic_std': 0.11361329184958874, 'ir': -0.007304795077409068}\n", "Dropped 6.6% entries from factor data: 6.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.504828315327279, 'return_min': 3.2199487054129783, 'ic_mean': 0.007324161395395641, 'ic_std': 0.10477724220362321, 'ir': 0.06990221579951424}\n", "Dropped 7.2% entries from factor data: 7.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.605194039624557, 'return_min': 4.4406594183787895, 'ic_mean': 0.01842848612979604, 'ic_std': 0.08688915152062648, 'ir': 0.21209191029355753}\n", "Dropped 7.1% entries from factor data: 7.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.1359149850133079, 'return_min': 2.589842165947598, 'ic_mean': -0.002703090317870771, 'ic_std': 0.11146539803100546, 'ir': -0.02425048818395529}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.3663203097835641, 'return_min': 0.20304131186366092, 'ic_mean': 0.01866052579718277, 'ic_std': 0.07949739296368309, 'ir': 0.23473129245518132}\n", "Dropped 7.2% entries from factor data: 7.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.45077736759768783, 'return_min': 3.849412960956755, 'ic_mean': 0.01495616823438208, 'ic_std': 0.08696706317457391, 'ir': 0.17197508675623227}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.2315438972433359, 'return_min': 2.783183261265698, 'ic_mean': 0.027321211911366904, 'ic_std': 0.14070553743167927, 'ir': 0.19417296866964417}\n", "Dropped 7.2% entries from factor data: 7.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3551159676850055, 'return_min': -3.478036016132391, 'ic_mean': 0.020322876952368004, 'ic_std': 0.1338962377687519, 'ir': 0.15178079153700363}\n", "Dropped 64.9% entries from factor data: 64.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 64.9%, consider increasing it.\n", "Dropped 6.6% entries from factor data: 6.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8765731127202692, 'return_min': -1.5401925270952521, 'ic_mean': 0.022784909518849178, 'ic_std': 0.1192110255261068, 'ir': 0.19113089094145375}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.348442140476028, 'return_min': 4.2035606357759825, 'ic_mean': -0.009535132809523408, 'ic_std': 0.10188374998859775, 'ir': -0.09358835742295044}\n", "Dropped 84.1% entries from factor data: 4.3% in forward returns computation and 79.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 84.1%, consider increasing it.\n", "Dropped 7.7% entries from factor data: 6.9% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.003627561512516, 'return_min': -2.0677145559211407, 'ic_mean': 0.04109736330964462, 'ic_std': 0.1083220010702326, 'ir': 0.37939996402945303}\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.928174070088163, 'return_min': -3.278744313574, 'ic_mean': 0.03340232033438042, 'ic_std': 0.1522403203728759, 'ir': 0.21940521573108557}\n", "Dropped 7.2% entries from factor data: 4.3% in forward returns computation and 2.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.002124868696221, 'return_min': -2.9114381151706947, 'ic_mean': 0.031249945212268593, 'ic_std': 0.10962770900564041, 'ir': 0.28505516986276497}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.7215432185845323, 'return_min': -0.4169211974214271, 'ic_mean': -0.006841996130160891, 'ic_std': 0.11162433876077245, 'ir': -0.06129484130539224}\n", "Dropped 8.5% entries from factor data: 8.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.4809504066822825, 'return_min': -0.7085413397422968, 'ic_mean': 0.019444254723143654, 'ic_std': 0.1282313971755444, 'ir': 0.15163411731781362}\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.7098186213227997, 'return_min': -1.455703171334699, 'ic_mean': 0.02256043364991146, 'ic_std': 0.12432763688197676, 'ir': 0.18145952272323732}\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.1531407292997287, 'return_min': 0.30201149318287435, 'ic_mean': 0.022020711607006267, 'ic_std': 0.12141837039971343, 'ir': 0.1813622727311636}\n", "Dropped 21.9% entries from factor data: 21.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.966516092430265, 'return_min': -4.703905377346773, 'ic_mean': 0.020253986365794077, 'ic_std': 0.11885146659941105, 'ir': 0.1704142737595334}\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.741383022008662, 'return_min': 1.3985564075169599, 'ic_mean': 0.02084908757303083, 'ic_std': 0.16575522085621536, 'ir': 0.12578238842393027}\n", "Dropped 6.6% entries from factor data: 6.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.505724082501828, 'return_min': 5.812262843956084, 'ic_mean': 0.02381310886547292, 'ic_std': 0.13779312855683265, 'ir': 0.17281782564107487}\n", "Dropped 43.8% entries from factor data: 12.2% in forward returns computation and 31.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 43.8%, consider increasing it.\n", "Dropped 46.7% entries from factor data: 46.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 46.7%, consider increasing it.\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.907215626577365, 'return_min': 0.20847145064006867, 'ic_mean': 0.017522684382269015, 'ic_std': 0.13025118342173927, 'ir': 0.1345299437743491}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8288735238504152, 'return_min': 0.98026606133228, 'ic_mean': 0.04917918797440046, 'ic_std': 0.14120973782057955, 'ir': 0.34827051401290265}\n", "Dropped 64.9% entries from factor data: 64.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 64.9%, consider increasing it.\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5825386521862708, 'return_min': 1.1405871966352343, 'ic_mean': 0.017325054899942273, 'ic_std': 0.13349607662501842, 'ir': 0.12977950616936254}\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -12.648250114389858, 'return_min': 8.205735041832973, 'ic_mean': -0.017556050216047826, 'ic_std': 0.12600233618142873, 'ir': -0.13933114851750966}\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6690572946398419, 'return_min': -8.079886058921515, 'ic_mean': 0.029485983086045057, 'ic_std': 0.13119590090955574, 'ir': 0.22474774655019292}\n", "Dropped 13.6% entries from factor data: 13.2% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.747919858520856, 'return_min': -1.6795463511265485, 'ic_mean': -0.008188463059712173, 'ic_std': 0.11650227783547026, 'ir': -0.0702858623182998}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.1438641164217564, 'return_min': 3.3888014728478133, 'ic_mean': 0.009063746467795753, 'ic_std': 0.10625735122141095, 'ir': 0.08529994737878804}\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.554377192275139, 'return_min': -1.2288228418610991, 'ic_mean': 0.004793805652864609, 'ic_std': 0.09511156070261373, 'ir': 0.05040192398748927}\n", "Dropped 74.2% entries from factor data: 6.6% in forward returns computation and 67.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 74.2%, consider increasing it.\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.2540395494696774, 'return_min': -2.1048297929449067, 'ic_mean': -0.009962511990264862, 'ic_std': 0.13676486568502655, 'ir': -0.07284408857760893}\n", "Dropped 19.3% entries from factor data: 6.6% in forward returns computation and 12.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -8.950871480767342, 'return_min': 0.08884351964777437, 'ic_mean': 0.024799194150559332, 'ic_std': 0.14494602359667075, 'ir': 0.17109261458297048}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.17290291023375204, 'return_min': -3.0089998365168125, 'ic_mean': 0.02543527672446265, 'ic_std': 0.08725735687545828, 'ir': 0.29149721737235534}\n", "Dropped 34.9% entries from factor data: 6.6% in forward returns computation and 28.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -25.867361066910235, 'return_min': -2.0557545289578982, 'ic_mean': 0.030386159772186873, 'ic_std': 0.14493462124801867, 'ir': 0.20965425314210262}\n", "Dropped 61.6% entries from factor data: 61.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 61.6%, consider increasing it.\n", "Dropped 7.2% entries from factor data: 7.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.692462864523181, 'return_min': 0.7695057109624592, 'ic_mean': 0.010496962943540643, 'ic_std': 0.11884280639786525, 'ir': 0.08832644786591978}\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.04208993448662213, 'return_min': -1.8810937928481763, 'ic_mean': 0.015896580615044068, 'ic_std': 0.12687617009665922, 'ir': 0.1252920907285697}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.9608206847278034, 'return_min': 2.0502324223126145, 'ic_mean': 0.023087542241340003, 'ic_std': 0.08135779251387347, 'ir': 0.2837778844282558}\n", "Dropped 11.8% entries from factor data: 11.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.028598041250660344, 'return_min': -3.7973447450290188, 'ic_mean': 0.022581559198980954, 'ic_std': 0.1279684629061728, 'ir': 0.176461908552718}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1164881411440675, 'return_min': -2.1229487260765456, 'ic_mean': 0.008180866745139417, 'ic_std': 0.13410127132651242, 'ir': 0.061005139356363605}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 34.2% entries from factor data: 34.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.314623707262431, 'return_min': -5.063126660235229, 'ic_mean': 0.022803331116916035, 'ic_std': 0.09006149969821907, 'ir': 0.2531973284180939}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 58.6% entries from factor data: 9.8% in forward returns computation and 48.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 58.6%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.391471459423979, 'return_min': 0.3369371080763628, 'ic_mean': 0.011509247178151171, 'ic_std': 0.09262833365987053, 'ir': 0.12425190784941578}\n", "Dropped 34.5% entries from factor data: 10.1% in forward returns computation and 24.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.4679544303386471, 'return_min': -0.04913480749824117, 'ic_mean': -0.0306823546784965, 'ic_std': 0.147794615783286, 'ir': -0.20760130208996658}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.9% entries from factor data: 9.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.4806450486937184, 'return_min': 4.100721047166633, 'ic_mean': 0.0005771100350351355, 'ic_std': 0.13074783869980475, 'ir': 0.0044139164423220205}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.9302944451936597, 'return_min': 2.3064961362861958, 'ic_mean': -0.0009745286711316146, 'ic_std': 0.096585242539585, 'ir': -0.010089829931650363}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.14296282788662, 'return_min': -8.593116577565452, 'ic_mean': 0.04567382627263698, 'ic_std': 0.14380522297123438, 'ir': 0.3176089527831211}\n", "Dropped 26.0% entries from factor data: 12.5% in forward returns computation and 13.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.247360077485611, 'return_min': 4.931226550783929, 'ic_mean': -0.06391298181736878, 'ic_std': 0.15434148590383734, 'ir': -0.4141011176812814}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.716008682907221, 'return_min': 0.8249509910318586, 'ic_mean': -0.003184780171962407, 'ic_std': 0.09729438713621405, 'ir': -0.032733441935387825}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.9034920984384307, 'return_min': 3.5683708439693262, 'ic_mean': 0.036987416163672106, 'ic_std': 0.1514068352864074, 'ir': 0.2442915875872129}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 22.6% entries from factor data: 8.5% in forward returns computation and 14.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.024729434144337, 'return_min': -4.362055955623445, 'ic_mean': 0.04106614493445225, 'ic_std': 0.14896737740510863, 'ir': 0.27567206760158713}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 47.1% entries from factor data: 4.3% in forward returns computation and 42.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.1%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.2469269506752987, 'return_min': 0.05348701016094637, 'ic_mean': -0.006089436064318933, 'ic_std': 0.08546073984042633, 'ir': -0.07125419316155261}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 6.4% entries from factor data: 6.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.277499139824226, 'return_min': -5.9637904686205, 'ic_mean': -0.014012180813935229, 'ic_std': 0.15879785775287802, 'ir': -0.08823910481047578}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    3.312147   -0.018883  0.028515  0.086809  0.328482\n", "alpha003    0.490645    3.687510  0.001101  0.075598  0.014568\n", "alpha005    6.490796  -11.488267  0.063776  0.130920  0.487142\n", "alpha006   -0.719127    0.988194  0.000051  0.116073  0.000439\n", "alpha008    0.664623   -1.149149  0.027597  0.138668  0.199015\n", "alpha009   -9.452167    1.868021  0.012974  0.153702  0.084413\n", "alpha010   -9.177981    3.480730  0.003468  0.146325  0.023704\n", "alpha011    1.935359   -1.899000 -0.000830  0.113613 -0.007305\n", "alpha012   -2.504828    3.219949  0.007324  0.104777  0.069902\n", "alpha013   -0.605194    4.440659  0.018428  0.086889  0.212092\n", "alpha014    0.135915    2.589842 -0.002703  0.111465 -0.024250\n", "alpha015    1.366320    0.203041  0.018661  0.079497  0.234731\n", "alpha016   -0.450777    3.849413  0.014956  0.086967  0.171975\n", "alpha017   -0.231544    2.783183  0.027321  0.140706  0.194173\n", "alpha018   -1.355116   -3.478036  0.020323  0.133896  0.151781\n", "alpha020    0.876573   -1.540193  0.022785  0.119211  0.191131\n", "alpha022   -4.348442    4.203561 -0.009535  0.101884 -0.093588\n", "alpha024    8.003628   -2.067715  0.041097  0.108322  0.379400\n", "alpha025   -2.928174   -3.278744  0.033402  0.152240  0.219405\n", "alpha026    3.002125   -2.911438  0.031250  0.109628  0.285055\n", "alpha028    2.721543   -0.416921 -0.006842  0.111624 -0.061295\n", "alpha029    4.480950   -0.708541  0.019444  0.128231  0.151634\n", "alpha030   -0.709819   -1.455703  0.022560  0.124328  0.181460\n", "alpha031    1.153141    0.302011  0.022021  0.121418  0.181362\n", "alpha032    7.966516   -4.703905  0.020254  0.118851  0.170414\n", "alpha033   -6.741383    1.398556  0.020849  0.165755  0.125782\n", "alpha034    5.505724    5.812263  0.023813  0.137793  0.172818\n", "alpha037   -3.907216    0.208471  0.017523  0.130251  0.134530\n", "alpha038    0.828874    0.980266  0.049179  0.141210  0.348271\n", "alpha040   -0.582539    1.140587  0.017325  0.133496  0.129780\n", "alpha041  -12.648250    8.205735 -0.017556  0.126002 -0.139331\n", "alpha042    1.669057   -8.079886  0.029486  0.131196  0.224748\n", "alpha043   -5.747920   -1.679546 -0.008188  0.116502 -0.070286\n", "alpha044   -2.143864    3.388801  0.009064  0.106257  0.085300\n", "alpha045   -4.554377   -1.228823  0.004794  0.095112  0.050402\n", "alpha047   -2.254040   -2.104830 -0.009963  0.136765 -0.072844\n", "alpha049   -8.950871    0.088844  0.024799  0.144946  0.171093\n", "alpha050    0.172903   -3.009000  0.025435  0.087257  0.291497\n", "alpha051  -25.867361   -2.055755  0.030386  0.144935  0.209654\n", "alpha053   -1.692463    0.769506  0.010497  0.118843  0.088326\n", "alpha054   -0.042090   -1.881094  0.015897  0.126876  0.125292\n", "alpha055    0.960821    2.050232  0.023088  0.081358  0.283778\n", "alpha057    0.028598   -3.797345  0.022582  0.127968  0.176462\n", "alpha060   -1.116488   -2.122949  0.008181  0.134101  0.061005\n", "alpha066    2.314624   -5.063127  0.022803  0.090061  0.253197\n", "alpha072    4.391471    0.336937  0.011509  0.092628  0.124252\n", "alpha073    1.467954   -0.049135 -0.030682  0.147795 -0.207601\n", "alpha077   -1.480645    4.100721  0.000577  0.130748  0.004414\n", "alpha078   -2.930294    2.306496 -0.000975  0.096585 -0.010090\n", "alpha083    4.142963   -8.593117  0.045674  0.143805  0.317609\n", "alpha084   -9.247360    4.931227 -0.063913  0.154341 -0.414101\n", "alpha085   -0.716009    0.824951 -0.003185  0.097294 -0.032733\n", "alpha088    1.903492    3.568371  0.036987  0.151407  0.244292\n", "alpha094    5.024729   -4.362056  0.041066  0.148967  0.275672\n", "alpha098   -2.246927    0.053487 -0.006089  0.085461 -0.071254\n", "alpha101    4.277499   -5.963790 -0.014012  0.158798 -0.088239\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 99.6% entries from factor data: 12.1% in forward returns computation and 87.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 99.6%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.2738234901243928, 'return_min': 2.135495851653957, 'ic_mean': 0.02410175785471791, 'ic_std': 0.09451804077690742, 'ir': 0.25499637589405505}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.08148226921300683, 'return_min': -0.42720154114550546, 'ic_mean': 0.0055501844664398794, 'ic_std': 0.08138756895238557, 'ir': 0.06819449871622191}\n", "Dropped 73.3% entries from factor data: 9.1% in forward returns computation and 64.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 73.3%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.768703187610022, 'return_min': -14.081932724567814, 'ic_mean': 0.07130741749713408, 'ic_std': 0.13732266101245688, 'ir': 0.5192691211442925}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.494026895358715, 'return_min': -1.4141733631911624, 'ic_mean': -0.00863237126549362, 'ic_std': 0.11720688535504378, 'ir': -0.07365071803882844}\n", "Dropped 87.1% entries from factor data: 15.1% in forward returns computation and 72.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 87.1%, consider increasing it.\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.535037850326052, 'return_min': -10.033457484107666, 'ic_mean': 0.06225408234042347, 'ic_std': 0.14337238539058728, 'ir': 0.4342125031317962}\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -10.093539932182205, 'return_min': -0.5166417961832703, 'ic_mean': 0.02526530080743112, 'ic_std': 0.1474150641083012, 'ir': 0.17138886694014865}\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -13.408226338451623, 'return_min': 1.587424173972174, 'ic_mean': 0.006476168027966814, 'ic_std': 0.1304009867159806, 'ir': 0.04966348944944878}\n", "Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.606600975445186, 'return_min': -6.043045378405498, 'ic_mean': 0.021674639709877562, 'ic_std': 0.11643682536102379, 'ir': 0.18614935303047997}\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.676336506450387, 'return_min': -1.2329519960352364, 'ic_mean': 0.01345486969564808, 'ic_std': 0.09693038823375236, 'ir': 0.13880961317518925}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.12640229028804129, 'return_min': -0.1967769130595265, 'ic_mean': 0.02315175269172668, 'ic_std': 0.09932070910888922, 'ir': 0.23310096050909682}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3766963398542664, 'return_min': 2.572667499975978, 'ic_mean': -0.0021013887124720606, 'ic_std': 0.11389163172227704, 'ir': -0.01845077360552937}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.7135897882546445, 'return_min': -1.2543760621763766, 'ic_mean': 0.01834205135024952, 'ic_std': 0.09719097355176874, 'ir': 0.18872175758667167}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6154909229704195, 'return_min': 0.8877406696394807, 'ic_mean': 0.021279920903092785, 'ic_std': 0.10291869816383127, 'ir': 0.20676438084378326}\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.270918816785761, 'return_min': 1.3137396252149713, 'ic_mean': 0.03186131294784404, 'ic_std': 0.15066434758757005, 'ir': 0.2114721462509597}\n", "Dropped 8.2% entries from factor data: 8.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.043474879485975, 'return_min': -5.357654926939626, 'ic_mean': 0.032536628263255464, 'ic_std': 0.13185444985625278, 'ir': 0.2467617004866106}\n", "Dropped 47.2% entries from factor data: 47.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.2%, consider increasing it.\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.100938593561196, 'return_min': -3.7869248237176745, 'ic_mean': 0.020537049088417657, 'ic_std': 0.11535066987465542, 'ir': 0.1780401371811193}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.054639370773884, 'return_min': -2.400092059520187, 'ic_mean': -0.007232762022848665, 'ic_std': 0.09862619752385846, 'ir': -0.07333509964326672}\n", "Dropped 78.0% entries from factor data: 4.3% in forward returns computation and 73.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 78.0%, consider increasing it.\n", "Dropped 8.3% entries from factor data: 7.8% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.11891059935376092, 'return_min': -7.957849039130771, 'ic_mean': 0.03512130575842516, 'ic_std': 0.10989729988310452, 'ir': 0.31958297242773903}\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.229733444737315, 'return_min': -12.453921076077679, 'ic_mean': 0.056366514189477204, 'ic_std': 0.14909327801543237, 'ir': 0.3780620759015226}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.018488481285498, 'return_min': -1.2681860276542167, 'ic_mean': 0.013301906330209263, 'ic_std': 0.11639616883174195, 'ir': 0.11428130722616836}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.164530235229112, 'return_min': 2.707495577136676, 'ic_mean': 0.001852588552596838, 'ic_std': 0.11306039259565866, 'ir': 0.016385831590221937}\n", "Dropped 9.8% entries from factor data: 9.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.36818940688521096, 'return_min': 2.2030415447393814, 'ic_mean': 0.0011168563674619947, 'ic_std': 0.11740782469604999, 'ir': 0.009512622947860217}\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.8141264379667454, 'return_min': -6.36928494499811, 'ic_mean': 0.03335660001301694, 'ic_std': 0.1130912490873264, 'ir': 0.2949529718896266}\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.054479211586191, 'return_min': -5.624944365224538, 'ic_mean': 0.0394683244464274, 'ic_std': 0.11644752733963107, 'ir': 0.3389365609397099}\n", "Dropped 19.2% entries from factor data: 19.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.26204673158259, 'return_min': -6.269211004448216, 'ic_mean': 0.01652519511881206, 'ic_std': 0.06817882883993517, 'ir': 0.24238015524743903}\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.167398011158621, 'return_min': 3.059175939486014, 'ic_mean': 0.03663938439308788, 'ic_std': 0.1630458620624989, 'ir': 0.22471827208373577}\n", "Dropped 7.6% entries from factor data: 7.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.06394121415887, 'return_min': 3.9493084610664475, 'ic_mean': 0.034954314308612004, 'ic_std': 0.13656875238372185, 'ir': 0.2559466473736224}\n", "Dropped 37.9% entries from factor data: 13.9% in forward returns computation and 24.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 37.9%, consider increasing it.\n", "Dropped 38.9% entries from factor data: 38.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 38.9%, consider increasing it.\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -8.1851548550127, 'return_min': 0.25512015107365116, 'ic_mean': 0.02743280937611549, 'ic_std': 0.1300944757479834, 'ir': 0.21086836484323757}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.745138752695553, 'return_min': -3.8745065475054297, 'ic_mean': 0.0564781939508272, 'ic_std': 0.1537922395460542, 'ir': 0.36723695628292347}\n", "Dropped 47.2% entries from factor data: 47.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.2%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0765140541524332, 'return_min': -2.6696189372021895, 'ic_mean': 0.02324340330215534, 'ic_std': 0.13968545060744716, 'ir': 0.16639816960948506}\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -13.69584187440931, 'return_min': 2.5947673738357935, 'ic_mean': -0.009312472176636688, 'ic_std': 0.12957028019476027, 'ir': -0.07187197683480256}\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.39670883077082, 'return_min': -12.084132876796216, 'ic_mean': 0.06529124495684553, 'ic_std': 0.1411158561248354, 'ir': 0.46267830384054714}\n", "Dropped 15.7% entries from factor data: 15.0% in forward returns computation and 0.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.4925187498846544, 'return_min': -0.3686512285117072, 'ic_mean': 0.004395809470934728, 'ic_std': 0.10814396252425802, 'ir': 0.040647756641511025}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.9616422789968677, 'return_min': -0.21675697023382234, 'ic_mean': 0.013595810762681796, 'ic_std': 0.11384228125561828, 'ir': 0.11942672452385368}\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.756109210345416, 'return_min': -6.705428167147476, 'ic_mean': 0.00856162162065079, 'ic_std': 0.0885616543757467, 'ir': 0.09667413827124097}\n", "Dropped 67.6% entries from factor data: 7.5% in forward returns computation and 60.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 67.6%, consider increasing it.\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7066045013948141, 'return_min': -7.018468990005466, 'ic_mean': 0.01652935390004474, 'ic_std': 0.12836526000655907, 'ir': 0.1287681254196045}\n", "Dropped 16.2% entries from factor data: 7.5% in forward returns computation and 8.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.934621402234445, 'return_min': 0.6920164645607407, 'ic_mean': 0.02922530409826468, 'ic_std': 0.14624818770861342, 'ir': 0.19983361541883524}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.3387798666264494, 'return_min': -2.414409285378394, 'ic_mean': 0.017561651559269097, 'ic_std': 0.09840226747389613, 'ir': 0.178467956177207}\n", "Dropped 32.3% entries from factor data: 7.5% in forward returns computation and 24.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.360632668135798, 'return_min': -0.5195646290101497, 'ic_mean': 0.03002972021209816, 'ic_std': 0.14130147096328574, 'ir': 0.21252234677656515}\n", "Dropped 43.8% entries from factor data: 43.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 43.8%, consider increasing it.\n", "Dropped 8.4% entries from factor data: 8.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.0656466582570356, 'return_min': -1.6235486465210336, 'ic_mean': 0.025692366344231592, 'ic_std': 0.11903589287819538, 'ir': 0.2158371372113918}\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.4058932751925823, 'return_min': -4.9588712320713935, 'ic_mean': 0.03681638752705178, 'ic_std': 0.1399514389037139, 'ir': 0.26306544481033406}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.4562459124298783, 'return_min': 2.9387071025310796, 'ic_mean': 0.01045026954743898, 'ic_std': 0.10304303621961747, 'ir': 0.10141655303291076}\n", "Dropped 13.6% entries from factor data: 13.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.807053065525423, 'return_min': -8.805953873122219, 'ic_mean': 0.04840576240468433, 'ic_std': 0.12842055083837406, 'ir': 0.3769315899104518}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7528506621401476, 'return_min': 1.076306514562475, 'ic_mean': 0.016146714403397808, 'ic_std': 0.1405509786952894, 'ir': 0.11488155083148467}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 37.2% entries from factor data: 37.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 37.2%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 37.0% entries from factor data: 11.2% in forward returns computation and 25.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 37.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.171626538651772, 'return_min': -2.91184285164503, 'ic_mean': -0.0007625349332854568, 'ic_std': 0.0887233698894255, 'ir': -0.008594521761693584}\n", "Dropped 33.5% entries from factor data: 11.6% in forward returns computation and 21.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.114598879553233, 'return_min': 3.560578377468371, 'ic_mean': -0.04234377395181862, 'ic_std': 0.13367734770837303, 'ir': -0.3167610270379891}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.861758401522142, 'return_min': 0.474140967217096, 'ic_mean': 0.0065125175143567965, 'ic_std': 0.10638189730785656, 'ir': 0.06121828693758248}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6775979431149501, 'return_min': -0.31466048913131317, 'ic_mean': 0.0028277599173360114, 'ic_std': 0.09941094412744864, 'ir': 0.028445157041368754}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.9478715507957887, 'return_min': -9.441481041035926, 'ic_mean': 0.0660872101830289, 'ic_std': 0.1381166950103844, 'ir': 0.47848821011869774}\n", "Dropped 22.2% entries from factor data: 14.2% in forward returns computation and 8.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.955682827250898, 'return_min': 0.9787383196413479, 'ic_mean': -0.06706410470798677, 'ic_std': 0.15221683855347806, 'ir': -0.4405826933820154}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.0300654171555585, 'return_min': -0.4581556363547623, 'ic_mean': -0.005826672227778809, 'ic_std': 0.10525651329120396, 'ir': -0.05535688049687401}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.561127407991858, 'return_min': 0.46907741527268954, 'ic_mean': 0.053211429652103126, 'ic_std': 0.1411674966304266, 'ir': 0.3769382536506224}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 17.5% entries from factor data: 9.8% in forward returns computation and 7.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.645467631720912, 'return_min': -5.746432633014775, 'ic_mean': 0.0583959284760707, 'ic_std': 0.148859811591545, 'ir': 0.3922880719230166}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 48.3% entries from factor data: 4.3% in forward returns computation and 44.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 48.3%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.8482402484876204, 'return_min': -0.09055912455702142, 'ic_mean': -0.020226262817788488, 'ic_std': 0.09177780464152434, 'ir': -0.2203829444035016}\n", "Dropped 100.0% entries from factor data: 4.3% in forward returns computation and 95.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.3% entries from factor data: 7.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.952732347587197, 'return_min': -7.293537194242861, 'ic_mean': -0.029102467684441285, 'ic_std': 0.1603825472487926, 'ir': -0.18145657481855698}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002   -1.273823    2.135496  0.024102  0.094518  0.254996\n", "alpha003   -0.081482   -0.427202  0.005550  0.081388  0.068194\n", "alpha005    2.768703  -14.081933  0.071307  0.137323  0.519269\n", "alpha006   -3.494027   -1.414173 -0.008632  0.117207 -0.073651\n", "alpha008    5.535038  -10.033457  0.062254  0.143372  0.434213\n", "alpha009  -10.093540   -0.516642  0.025265  0.147415  0.171389\n", "alpha010  -13.408226    1.587424  0.006476  0.130401  0.049663\n", "alpha011    5.606601   -6.043045  0.021675  0.116437  0.186149\n", "alpha012   -6.676337   -1.232952  0.013455  0.096930  0.138810\n", "alpha013    0.126402   -0.196777  0.023152  0.099321  0.233101\n", "alpha014   -1.376696    2.572667 -0.002101  0.113892 -0.018451\n", "alpha015    2.713590   -1.254376  0.018342  0.097191  0.188722\n", "alpha016    1.615491    0.887741  0.021280  0.102919  0.206764\n", "alpha017   -2.270919    1.313740  0.031861  0.150664  0.211472\n", "alpha018   -4.043475   -5.357655  0.032537  0.131854  0.246762\n", "alpha020   -1.100939   -3.786925  0.020537  0.115351  0.178040\n", "alpha022   -6.054639   -2.400092 -0.007233  0.098626 -0.073335\n", "alpha024   -0.118911   -7.957849  0.035121  0.109897  0.319583\n", "alpha025   -3.229733  -12.453921  0.056367  0.149093  0.378062\n", "alpha026    4.018488   -1.268186  0.013302  0.116396  0.114281\n", "alpha028    2.164530    2.707496  0.001853  0.113060  0.016386\n", "alpha029    0.368189    2.203042  0.001117  0.117408  0.009513\n", "alpha030    1.814126   -6.369285  0.033357  0.113091  0.294953\n", "alpha031    2.054479   -5.624944  0.039468  0.116448  0.338937\n", "alpha032    2.262047   -6.269211  0.016525  0.068179  0.242380\n", "alpha033   -7.167398    3.059176  0.036639  0.163046  0.224718\n", "alpha034   -1.063941    3.949308  0.034954  0.136569  0.255947\n", "alpha037   -8.185155    0.255120  0.027433  0.130094  0.210868\n", "alpha038    2.745139   -3.874507  0.056478  0.153792  0.367237\n", "alpha040    1.076514   -2.669619  0.023243  0.139685  0.166398\n", "alpha041  -13.695842    2.594767 -0.009312  0.129570 -0.071872\n", "alpha042   12.396709  -12.084133  0.065291  0.141116  0.462678\n", "alpha043   -3.492519   -0.368651  0.004396  0.108144  0.040648\n", "alpha044   -2.961642   -0.216757  0.013596  0.113842  0.119427\n", "alpha045   -4.756109   -6.705428  0.008562  0.088562  0.096674\n", "alpha047    0.706605   -7.018469  0.016529  0.128365  0.128768\n", "alpha049   -9.934621    0.692016  0.029225  0.146248  0.199834\n", "alpha050    1.338780   -2.414409  0.017562  0.098402  0.178468\n", "alpha051   -2.360633   -0.519565  0.030030  0.141301  0.212522\n", "alpha053   -1.065647   -1.623549  0.025692  0.119036  0.215837\n", "alpha054    1.405893   -4.958871  0.036816  0.139951  0.263065\n", "alpha055   -3.456246    2.938707  0.010450  0.103043  0.101417\n", "alpha057   -3.807053   -8.805954  0.048406  0.128421  0.376932\n", "alpha060    0.752851    1.076307  0.016147  0.140551  0.114882\n", "alpha072   -2.171627   -2.911843 -0.000763  0.088723 -0.008595\n", "alpha073   -4.114599    3.560578 -0.042344  0.133677 -0.316761\n", "alpha077   -3.861758    0.474141  0.006513  0.106382  0.061218\n", "alpha078    1.677598   -0.314660  0.002828  0.099411  0.028445\n", "alpha083    2.947872   -9.441481  0.066087  0.138117  0.478488\n", "alpha084   -7.955683    0.978738 -0.067064  0.152217 -0.440583\n", "alpha085    3.030065   -0.458156 -0.005827  0.105257 -0.055357\n", "alpha088    1.561127    0.469077  0.053211  0.141167  0.376938\n", "alpha094    4.645468   -5.746433  0.058396  0.148860  0.392288\n", "alpha098   -1.848240   -0.090559 -0.020226  0.091778 -0.220383\n", "alpha101    5.952732   -7.293537 -0.029102  0.160383 -0.181457\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 19.3% in forward returns computation and 80.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.2% entries from factor data: 4.8% in forward returns computation and 2.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.8739150744749864, 'return_min': 1.8431631573334606, 'ic_mean': 0.017252458079070842, 'ic_std': 0.10464953331141591, 'ir': 0.16485938860072127}\n", "Dropped 8.0% entries from factor data: 4.8% in forward returns computation and 3.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.014199479103638168, 'return_min': -5.049067487051806, 'ic_mean': 0.004024572392637595, 'ic_std': 0.08935829771085312, 'ir': 0.045038597374139386}\n", "Dropped 66.8% entries from factor data: 14.5% in forward returns computation and 52.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 66.8%, consider increasing it.\n", "Dropped 14.8% entries from factor data: 14.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.12499746579309, 'return_min': -14.858263375663316, 'ic_mean': 0.05474877504759822, 'ic_std': 0.18752181098078047, 'ir': 0.29195950466375103}\n", "Dropped 8.0% entries from factor data: 4.8% in forward returns computation and 3.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.572965559631603, 'return_min': -7.404075786853204, 'ic_mean': -0.007232246391327812, 'ic_std': 0.17457818395418162, 'ir': -0.04142697688518703}\n", "Dropped 89.1% entries from factor data: 23.8% in forward returns computation and 65.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 89.1%, consider increasing it.\n", "Dropped 16.5% entries from factor data: 16.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.5477416467180038, 'return_min': -8.500218763971557, 'ic_mean': 0.03741613871488302, 'ic_std': 0.21154076002743655, 'ir': 0.1768743702633488}\n", "Dropped 11.9% entries from factor data: 11.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -19.880964667106092, 'return_min': 6.7224837243839985, 'ic_mean': 0.01970766640555429, 'ic_std': 0.19993102437790386, 'ir': 0.09857232746581354}\n", "Dropped 11.9% entries from factor data: 11.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -21.37740340361183, 'return_min': 9.143033498266728, 'ic_mean': 0.006296182873524444, 'ic_std': 0.18691868494216451, 'ir': 0.03368407431002726}\n", "Dropped 12.6% entries from factor data: 12.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.328063501537471, 'return_min': -18.4859103881585, 'ic_mean': 0.017199420683037297, 'ic_std': 0.16655302675853936, 'ir': 0.10326693556864744}\n", "Dropped 11.9% entries from factor data: 11.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.399800377072662, 'return_min': -15.489167657321001, 'ic_mean': 0.027967542179163456, 'ic_std': 0.14325054813895882, 'ir': 0.19523514948113016}\n", "Dropped 13.0% entries from factor data: 13.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7597536328273211, 'return_min': -3.3451382941773744, 'ic_mean': 0.025120362657844293, 'ic_std': 0.09758737329493335, 'ir': 0.25741406710399206}\n", "Dropped 13.3% entries from factor data: 13.0% in forward returns computation and 0.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.9562427992857465, 'return_min': -4.566077145440328, 'ic_mean': 0.004122085377622884, 'ic_std': 0.16197501575555212, 'ir': 0.02544889629054775}\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.203963406645531, 'return_min': -4.389423603926579, 'ic_mean': 0.013260768051706532, 'ic_std': 0.12928441138053232, 'ir': 0.10257051031988024}\n", "Dropped 13.0% entries from factor data: 13.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7499625036665467, 'return_min': -2.0090540117279065, 'ic_mean': 0.021843663374929836, 'ic_std': 0.10078372229852416, 'ir': 0.2167380096383849}\n", "Dropped 19.6% entries from factor data: 19.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.760594768031481, 'return_min': 4.175582895880048, 'ic_mean': 0.036547154133805576, 'ic_std': 0.20510554356581157, 'ir': 0.17818706163872555}\n", "Dropped 13.0% entries from factor data: 13.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.21221545444150003, 'return_min': -7.8903924934825564, 'ic_mean': 0.04519355605880177, 'ic_std': 0.1946283545013007, 'ir': 0.23220437831169016}\n", "Dropped 58.1% entries from factor data: 58.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 58.1%, consider increasing it.\n", "Dropped 11.9% entries from factor data: 11.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.29820851959273, 'return_min': 0.3218016804407142, 'ic_mean': 0.008682929656265952, 'ic_std': 0.17120229792483507, 'ir': 0.05071736630590157}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 18.3% entries from factor data: 18.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.5179670315722547, 'return_min': -0.3866269942875977, 'ic_mean': 0.0051244009096843065, 'ic_std': 0.14138971388018287, 'ir': 0.036243095548144685}\n", "Dropped 73.6% entries from factor data: 4.8% in forward returns computation and 68.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 73.6%, consider increasing it.\n", "Dropped 12.5% entries from factor data: 12.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.653238497531632, 'return_min': 0.4609842824865318, 'ic_mean': 0.042347071575076965, 'ic_std': 0.2045614269514689, 'ir': 0.20701396253519283}\n", "Dropped 19.0% entries from factor data: 18.3% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.042343650743074, 'return_min': -19.606856373661774, 'ic_mean': 0.06197875148818309, 'ic_std': 0.19736155307165562, 'ir': 0.31403660198032896}\n", "Dropped 6.8% entries from factor data: 4.8% in forward returns computation and 2.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 10.028966720310173, 'return_min': -14.423861925493275, 'ic_mean': 0.022476631464930723, 'ic_std': 0.1576805554944695, 'ir': 0.14254535947343913}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 11.5% entries from factor data: 11.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.460291452652037, 'return_min': 4.689057608469049, 'ic_mean': -0.002172537346559661, 'ic_std': 0.15301208349928527, 'ir': -0.014198469146195302}\n", "Dropped 15.5% entries from factor data: 15.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.15003319400852533, 'return_min': -3.847471416110748, 'ic_mean': 0.014117374401915288, 'ic_std': 0.17344753083519368, 'ir': 0.08139276664208803}\n", "Dropped 18.3% entries from factor data: 18.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.860733053418052, 'return_min': -1.7322060478419665, 'ic_mean': 0.0338316698239172, 'ic_std': 0.1730500754153834, 'ir': 0.19550219635968857}\n", "Dropped 18.3% entries from factor data: 18.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.258574934402965, 'return_min': -7.375272439581471, 'ic_mean': 0.04311182823104711, 'ic_std': 0.15359158182478025, 'ir': 0.2806913485677215}\n", "Dropped 32.1% entries from factor data: 32.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.698439845707281, 'return_min': -12.8948118837513, 'ic_mean': 0.018623339852176877, 'ic_std': 0.10126531725029844, 'ir': 0.1839063991291845}\n", "Dropped 11.8% entries from factor data: 11.5% in forward returns computation and 0.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -13.168508733308881, 'return_min': -2.212046019129943, 'ic_mean': 0.03472637880797082, 'ic_std': 0.23171071819516692, 'ir': 0.14986954025459126}\n", "Dropped 11.9% entries from factor data: 11.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7203981967317539, 'return_min': 5.355079960889242, 'ic_mean': 0.0348102546506666, 'ic_std': 0.1851789179096266, 'ir': 0.1879817370336679}\n", "Dropped 38.3% entries from factor data: 22.4% in forward returns computation and 15.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 38.3%, consider increasing it.\n", "Dropped 52.5% entries from factor data: 52.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 52.5%, consider increasing it.\n", "Dropped 11.5% entries from factor data: 11.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.5293758806155253, 'return_min': -12.538299954595322, 'ic_mean': 0.060436342864356694, 'ic_std': 0.18800468568955844, 'ir': 0.3214618967750188}\n", "Dropped 15.1% entries from factor data: 14.8% in forward returns computation and 0.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.19323771002567, 'return_min': -4.422693809318368, 'ic_mean': 0.051763901583051636, 'ic_std': 0.20792638394581642, 'ir': 0.24895302174129474}\n", "Dropped 58.1% entries from factor data: 58.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 58.1%, consider increasing it.\n", "Dropped 14.8% entries from factor data: 14.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.374141180776501, 'return_min': -13.906578801551328, 'ic_mean': 0.016038926340622452, 'ic_std': 0.17832376413053846, 'ir': 0.08994273095806495}\n", "Dropped 11.5% entries from factor data: 11.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -28.685841149385105, 'return_min': 9.344582031447501, 'ic_mean': -0.02065794266885701, 'ic_std': 0.1661433483673375, 'ir': -0.12433806632561044}\n", "Dropped 11.5% entries from factor data: 11.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.0509200561357197, 'return_min': -12.146512723638647, 'ic_mean': 0.027967070440009956, 'ic_std': 0.19152383853957827, 'ir': 0.14602396575416685}\n", "Dropped 27.7% entries from factor data: 24.3% in forward returns computation and 3.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -9.408133618493686, 'return_min': 5.871438787909966, 'ic_mean': -0.020328239944588603, 'ic_std': 0.14570640402948026, 'ir': -0.13951507540104868}\n", "Dropped 5.6% entries from factor data: 4.8% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.797402750096214, 'return_min': -6.445985401436483, 'ic_mean': 0.018848558695628907, 'ic_std': 0.14476717761576724, 'ir': 0.13019911699636552}\n", "Dropped 18.2% entries from factor data: 18.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.693531082815451, 'return_min': -11.968115988060246, 'ic_mean': 0.025251024832018215, 'ic_std': 0.12401017157441797, 'ir': 0.20362059427411713}\n", "Dropped 80.9% entries from factor data: 11.7% in forward returns computation and 69.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 80.9%, consider increasing it.\n", "Dropped 18.3% entries from factor data: 18.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -11.082045616849223, 'return_min': 0.5805762099386769, 'ic_mean': -0.008175420735419226, 'ic_std': 0.18732931626702745, 'ir': -0.04364197178708335}\n", "Dropped 43.4% entries from factor data: 11.8% in forward returns computation and 31.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 43.4%, consider increasing it.\n", "Dropped 5.6% entries from factor data: 4.8% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.882070595128578, 'return_min': -3.3684902856268018, 'ic_mean': 0.013721110998353825, 'ic_std': 0.13274407678523312, 'ir': 0.10336514691011965}\n", "Dropped 55.9% entries from factor data: 11.8% in forward returns computation and 44.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 55.9%, consider increasing it.\n", "Dropped 56.2% entries from factor data: 56.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 56.2%, consider increasing it.\n", "Dropped 13.5% entries from factor data: 13.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -26.07250690059515, 'return_min': 15.249876290621689, 'ic_mean': -0.02608477779601336, 'ic_std': 0.1825639410391158, 'ir': -0.14288022951051702}\n", "Dropped 16.2% entries from factor data: 11.5% in forward returns computation and 4.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -14.539885539692765, 'return_min': 10.524192148781975, 'ic_mean': -0.01973522310529415, 'ic_std': 0.1815716257384849, 'ir': -0.10869111858765047}\n", "Dropped 12.5% entries from factor data: 4.8% in forward returns computation and 7.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.467066132529563, 'return_min': 0.6552130974935011, 'ic_mean': 0.017252388802181322, 'ic_std': 0.10771363645242167, 'ir': 0.16016903124240817}\n", "Dropped 21.8% entries from factor data: 21.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.6780852067264114, 'return_min': -5.476290765190628, 'ic_mean': 0.035579756321986726, 'ic_std': 0.18074165290906044, 'ir': 0.19685421566819775}\n", "Dropped 14.8% entries from factor data: 14.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -25.547148273378227, 'return_min': 16.95530689837188, 'ic_mean': -0.034400299945834864, 'ic_std': 0.18906132123387182, 'ir': -0.18195313415418882}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 28.6% entries from factor data: 28.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.992409018025114, 'return_min': -7.8300916243345675, 'ic_mean': 0.02577726943544067, 'ic_std': 0.10807783532726009, 'ir': 0.23850652964492677}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 97.6% entries from factor data: 17.9% in forward returns computation and 79.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 97.6%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 4.8% in forward returns computation and 4.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.495958307184083, 'return_min': -5.8925474524695876, 'ic_mean': 0.009815077717108442, 'ic_std': 0.13117372435078772, 'ir': 0.0748250289125034}\n", "Dropped 40.1% entries from factor data: 18.6% in forward returns computation and 21.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 40.1%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 19.3% entries from factor data: 18.3% in forward returns computation and 1.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.28690053607949295, 'return_min': 0.04503036204805966, 'ic_mean': 0.009415485124408549, 'ic_std': 0.13760803853738915, 'ir': 0.06842249351479776}\n", "Dropped 8.0% entries from factor data: 4.8% in forward returns computation and 3.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.3000633814063143, 'return_min': -0.7430207195335825, 'ic_mean': -0.002738195278706931, 'ic_std': 0.13245672347473128, 'ir': -0.020672376659153096}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 13.8% entries from factor data: 13.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.582255803436633, 'return_min': -7.860075377161824, 'ic_mean': 0.03072902856770557, 'ic_std': 0.20422198541023984, 'ir': 0.15046875832676532}\n", "Dropped 33.0% entries from factor data: 23.0% in forward returns computation and 9.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -29.43876019574465, 'return_min': 18.056714696437215, 'ic_mean': -0.07682877505687259, 'ic_std': 0.21472565577958644, 'ir': -0.35779969923918403}\n", "Dropped 11.3% entries from factor data: 4.8% in forward returns computation and 6.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.3408086418907903, 'return_min': -7.009042207352101, 'ic_mean': 0.000456117662702064, 'ic_std': 0.1313918222424336, 'ir': 0.0034714311356491613}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 14.1% entries from factor data: 14.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 14.700918707375443, 'return_min': -9.200698864556545, 'ic_mean': 0.06325033311532322, 'ic_std': 0.1921982186517635, 'ir': 0.32908907043474755}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 24.4% entries from factor data: 15.5% in forward returns computation and 8.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 15.456070325707838, 'return_min': -21.208481719667827, 'ic_mean': 0.055521468485669856, 'ic_std': 0.20367963033151185, 'ir': 0.2725921506991265}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 52.8% entries from factor data: 4.8% in forward returns computation and 48.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 52.8%, consider increasing it.\n", "Dropped 12.5% entries from factor data: 4.8% in forward returns computation and 7.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.361369101184454, 'return_min': -2.8119072227872355, 'ic_mean': -0.009428996280075349, 'ic_std': 0.10237502313735915, 'ir': -0.09210250695058918}\n", "Dropped 100.0% entries from factor data: 4.8% in forward returns computation and 95.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 11.8% entries from factor data: 11.5% in forward returns computation and 0.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.33557734609203, 'return_min': -7.432006574676908, 'ic_mean': -0.020250693371484253, 'ic_std': 0.21815193173429415, 'ir': -0.092828393544318}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    2.873915    1.843163  0.017252  0.104650  0.164859\n", "alpha003    0.014199   -5.049067  0.004025  0.089358  0.045039\n", "alpha005    4.124997  -14.858263  0.054749  0.187522  0.291960\n", "alpha006   -4.572966   -7.404076 -0.007232  0.174578 -0.041427\n", "alpha008   -1.547742   -8.500219  0.037416  0.211541  0.176874\n", "alpha009  -19.880965    6.722484  0.019708  0.199931  0.098572\n", "alpha010  -21.377403    9.143033  0.006296  0.186919  0.033684\n", "alpha011    4.328064  -18.485910  0.017199  0.166553  0.103267\n", "alpha012    6.399800  -15.489168  0.027968  0.143251  0.195235\n", "alpha013   -1.759754   -3.345138  0.025120  0.097587  0.257414\n", "alpha014    2.956243   -4.566077  0.004122  0.161975  0.025449\n", "alpha015    8.203963   -4.389424  0.013261  0.129284  0.102571\n", "alpha016   -1.749963   -2.009054  0.021844  0.100784  0.216738\n", "alpha017   -7.760595    4.175583  0.036547  0.205106  0.178187\n", "alpha018   -0.212215   -7.890392  0.045194  0.194628  0.232204\n", "alpha020   -7.298209    0.321802  0.008683  0.171202  0.050717\n", "alpha022   -2.517967   -0.386627  0.005124  0.141390  0.036243\n", "alpha024    5.653238    0.460984  0.042347  0.204561  0.207014\n", "alpha025   -4.042344  -19.606856  0.061979  0.197362  0.314037\n", "alpha026   10.028967  -14.423862  0.022477  0.157681  0.142545\n", "alpha028   -9.460291    4.689058 -0.002173  0.153012 -0.014198\n", "alpha029    0.150033   -3.847471  0.014117  0.173448  0.081393\n", "alpha030    2.860733   -1.732206  0.033832  0.173050  0.195502\n", "alpha031   11.258575   -7.375272  0.043112  0.153592  0.280691\n", "alpha032    4.698440  -12.894812  0.018623  0.101265  0.183906\n", "alpha033  -13.168509   -2.212046  0.034726  0.231711  0.149870\n", "alpha034   -1.720398    5.355080  0.034810  0.185179  0.187982\n", "alpha037    2.529376  -12.538300  0.060436  0.188005  0.321462\n", "alpha038    9.193238   -4.422694  0.051764  0.207926  0.248953\n", "alpha040    2.374141  -13.906579  0.016039  0.178324  0.089943\n", "alpha041  -28.685841    9.344582 -0.020658  0.166143 -0.124338\n", "alpha042    3.050920  -12.146513  0.027967  0.191524  0.146024\n", "alpha043   -9.408134    5.871439 -0.020328  0.145706 -0.139515\n", "alpha044    5.797403   -6.445985  0.018849  0.144767  0.130199\n", "alpha045    5.693531  -11.968116  0.025251  0.124010  0.203621\n", "alpha047  -11.082046    0.580576 -0.008175  0.187329 -0.043642\n", "alpha050    9.882071   -3.368490  0.013721  0.132744  0.103365\n", "alpha053  -26.072507   15.249876 -0.026085  0.182564 -0.142880\n", "alpha054  -14.539886   10.524192 -0.019735  0.181572 -0.108691\n", "alpha055    5.467066    0.655213  0.017252  0.107714  0.160169\n", "alpha057    3.678085   -5.476291  0.035580  0.180742  0.196854\n", "alpha060  -25.547148   16.955307 -0.034400  0.189061 -0.181953\n", "alpha066    6.992409   -7.830092  0.025777  0.108078  0.238507\n", "alpha072    9.495958   -5.892547  0.009815  0.131174  0.074825\n", "alpha077    0.286901    0.045030  0.009415  0.137608  0.068422\n", "alpha078   -2.300063   -0.743021 -0.002738  0.132457 -0.020672\n", "alpha083   -7.582256   -7.860075  0.030729  0.204222  0.150469\n", "alpha084  -29.438760   18.056715 -0.076829  0.214726 -0.357800\n", "alpha085   -2.340809   -7.009042  0.000456  0.131392  0.003471\n", "alpha088   14.700919   -9.200699  0.063250  0.192198  0.329089\n", "alpha094   15.456070  -21.208482  0.055521  0.203680  0.272592\n", "alpha098   -5.361369   -2.811907 -0.009429  0.102375 -0.092103\n", "alpha101   12.335577   -7.432007 -0.020251  0.218152 -0.092828\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 12.9% in forward returns computation and 87.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.507652236456547, 'return_min': 0.608682014144879, 'ic_mean': 0.028752666138028713, 'ic_std': 0.08429204516477722, 'ir': 0.3411077057369048}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.052238789522544, 'return_min': -2.1553611149249807, 'ic_mean': 0.01618864252357087, 'ic_std': 0.09042190626162427, 'ir': 0.17903451932025294}\n", "Dropped 67.9% entries from factor data: 10.2% in forward returns computation and 57.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 67.9%, consider increasing it.\n", "Dropped 10.4% entries from factor data: 10.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.4559049565997526, 'return_min': -13.297041215301064, 'ic_mean': 0.07515753657473942, 'ic_std': 0.1746263088264531, 'ir': 0.4303906844267801}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.983584546310091, 'return_min': -3.9468205699599856, 'ic_mean': 0.0012510631074836658, 'ic_std': 0.1437593752621038, 'ir': 0.008702480135314394}\n", "Dropped 89.7% entries from factor data: 15.3% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 89.7%, consider increasing it.\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.8252934961981993, 'return_min': -6.432593013298016, 'ic_mean': 0.05089575899058228, 'ic_std': 0.17832097835382965, 'ir': 0.2854165531191369}\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.1998492918936545, 'return_min': -5.921572545620757, 'ic_mean': 0.05851873970254346, 'ic_std': 0.1641799443058314, 'ir': 0.35643050038764673}\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.241363248635778, 'return_min': -4.733789466425664, 'ic_mean': 0.04444990989710133, 'ic_std': 0.15109933036175463, 'ir': 0.29417674976243463}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.684060613908581, 'return_min': -7.964318477829968, 'ic_mean': 0.014975770707446809, 'ic_std': 0.12666401827321877, 'ir': 0.11823224078635766}\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.671192222781672, 'return_min': 1.3301334956650201, 'ic_mean': 0.012063092603481694, 'ic_std': 0.1289698883314847, 'ir': 0.09353417886566316}\n", "Dropped 9.5% entries from factor data: 9.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.515724183998035, 'return_min': -3.8805140747744105, 'ic_mean': 0.02775669848117555, 'ic_std': 0.09699683032358423, 'ir': 0.2861608816347751}\n", "Dropped 9.4% entries from factor data: 9.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7628448066175917, 'return_min': -2.9758295056514505, 'ic_mean': 0.009051424834513896, 'ic_std': 0.13548750178216915, 'ir': 0.06680634534886014}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.590339803301255, 'return_min': -0.6094648503962574, 'ic_mean': 0.02002359412653619, 'ic_std': 0.09372199762830977, 'ir': 0.2136488192019484}\n", "Dropped 9.5% entries from factor data: 9.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6270137999597747, 'return_min': -3.8225110828327136, 'ic_mean': 0.030017101937923063, 'ic_std': 0.09642918520010256, 'ir': 0.31128648319109864}\n", "Dropped 13.0% entries from factor data: 13.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.17524490851794639, 'return_min': -3.164677858980003, 'ic_mean': 0.04345182928252015, 'ic_std': 0.1593900770544204, 'ir': 0.27261313932161807}\n", "Dropped 9.5% entries from factor data: 9.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.0634806432706654, 'return_min': -8.514926928889777, 'ic_mean': 0.04767195607357609, 'ic_std': 0.15830800296336692, 'ir': 0.30113421419767117}\n", "Dropped 56.4% entries from factor data: 56.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 56.4%, consider increasing it.\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5422115990072474, 'return_min': -2.0977742493943907, 'ic_mean': 0.012405743946959605, 'ic_std': 0.13685542136472148, 'ir': 0.09064853860555612}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.7768337797738916, 'return_min': -1.639683734545594, 'ic_mean': 0.0029376844468461958, 'ic_std': 0.1071412787323197, 'ir': 0.027418792099594653}\n", "Dropped 80.6% entries from factor data: 5.5% in forward returns computation and 75.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 80.6%, consider increasing it.\n", "Dropped 9.6% entries from factor data: 9.2% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.8732156675705927, 'return_min': -3.1476307724143116, 'ic_mean': 0.02299423699290755, 'ic_std': 0.13172101959910032, 'ir': 0.17456771184198003}\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.4794783177562998, 'return_min': -12.551044683946078, 'ic_mean': 0.07989561942437673, 'ic_std': 0.17768470001437284, 'ir': 0.44964827820242265}\n", "Dropped 8.2% entries from factor data: 5.5% in forward returns computation and 2.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.906242737168661, 'return_min': -3.172807102250763, 'ic_mean': 0.020780547266196436, 'ic_std': 0.12195289807510445, 'ir': 0.1703981421860002}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.795397059350945, 'return_min': -0.9800993039221773, 'ic_mean': -0.0010819407986017177, 'ic_std': 0.11159130046443405, 'ir': -0.009695565820084243}\n", "Dropped 10.8% entries from factor data: 10.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.48044940509762, 'return_min': -5.593056845361044, 'ic_mean': 0.03135447734970555, 'ic_std': 0.13034647802172336, 'ir': 0.24054717722775798}\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.8946194819646767, 'return_min': -2.256531520916649, 'ic_mean': 0.025941649447682864, 'ic_std': 0.12281801714079119, 'ir': 0.21122022689834602}\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.021629708157448, 'return_min': -1.6864947238548478, 'ic_mean': 0.03794545566331911, 'ic_std': 0.1310259958775531, 'ir': 0.28960249765077184}\n", "Dropped 25.3% entries from factor data: 25.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.097983928754932, 'return_min': -7.082793553983002, 'ic_mean': 0.009327715651804031, 'ic_std': 0.11496225521293882, 'ir': 0.08113720137558864}\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.751633992667649, 'return_min': -7.244244214682505, 'ic_mean': 0.06554046688196917, 'ic_std': 0.18882410184036366, 'ir': 0.3470979935462827}\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.704360463885447, 'return_min': -1.017823494118275, 'ic_mean': 0.05564894660113976, 'ic_std': 0.16686008521802426, 'ir': 0.33350664137817754}\n", "Dropped 41.9% entries from factor data: 14.6% in forward returns computation and 27.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 41.9%, consider increasing it.\n", "Dropped 46.8% entries from factor data: 46.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 46.8%, consider increasing it.\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.4278266287701697, 'return_min': -4.119256173878982, 'ic_mean': 0.05570618752681884, 'ic_std': 0.1666973772553804, 'ir': 0.3341755487938899}\n", "Dropped 10.4% entries from factor data: 10.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.41304521847362885, 'return_min': -5.765908366071049, 'ic_mean': 0.0578147917665973, 'ic_std': 0.16755471302131078, 'ir': 0.3450502270219281}\n", "Dropped 56.4% entries from factor data: 56.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 56.4%, consider increasing it.\n", "Dropped 10.4% entries from factor data: 10.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.412529585014255, 'return_min': -2.8655200561367167, 'ic_mean': 0.023884966956565216, 'ic_std': 0.17106557268435899, 'ir': 0.1396246280403625}\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -10.753780219134867, 'return_min': 2.48493119115345, 'ic_mean': -0.0017513044670756696, 'ic_std': 0.1492701536733003, 'ir': -0.011732449012604737}\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.03586807912593, 'return_min': -9.107466211971671, 'ic_mean': 0.06005967537592079, 'ic_std': 0.15002576031587686, 'ir': 0.40032908514821786}\n", "Dropped 17.5% entries from factor data: 15.7% in forward returns computation and 1.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.647479642387765, 'return_min': 3.1226750823543092, 'ic_mean': -0.012752992160622022, 'ic_std': 0.1276482038271502, 'ir': -0.09990733734013982}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0361817720161426, 'return_min': -2.7517640871721483, 'ic_mean': 0.028319055306300932, 'ic_std': 0.11333887554244428, 'ir': 0.2498617987055618}\n", "Dropped 12.3% entries from factor data: 12.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.786269580314585, 'return_min': -1.8607208916476825, 'ic_mean': 0.001550943731201654, 'ic_std': 0.1145849000830944, 'ir': 0.013535323852243573}\n", "Dropped 77.0% entries from factor data: 8.8% in forward returns computation and 68.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 77.0%, consider increasing it.\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6848336093134844, 'return_min': -4.271936879807203, 'ic_mean': 0.007002507895734075, 'ic_std': 0.1740507373694785, 'ir': 0.04023256667318224}\n", "Dropped 25.5% entries from factor data: 8.9% in forward returns computation and 16.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.369896716097665, 'return_min': -9.905272225051442, 'ic_mean': 0.06516698016829812, 'ic_std': 0.16421173517938628, 'ir': 0.39684727828440014}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0242091213208404, 'return_min': -3.7435806285213147, 'ic_mean': 0.025385296018949913, 'ic_std': 0.10528774335316678, 'ir': 0.24110399948263675}\n", "Dropped 42.3% entries from factor data: 8.9% in forward returns computation and 33.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 42.3%, consider increasing it.\n", "Dropped 53.9% entries from factor data: 53.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 53.9%, consider increasing it.\n", "Dropped 9.8% entries from factor data: 9.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.6648018387588337, 'return_min': 3.4640075458391983, 'ic_mean': 0.024282054733678, 'ic_std': 0.15918342131805546, 'ir': 0.15254135470025731}\n", "Dropped 9.5% entries from factor data: 8.7% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8876463059226403, 'return_min': -4.662957529985023, 'ic_mean': 0.0468910584810102, 'ic_std': 0.15574619704477372, 'ir': 0.3010735374009166}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.9363793530936526, 'return_min': -1.389137455312106, 'ic_mean': 0.02723733368380211, 'ic_std': 0.10201104924582864, 'ir': 0.26700375974140744}\n", "Dropped 14.3% entries from factor data: 14.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.2570195975980525, 'return_min': -7.030723132652739, 'ic_mean': 0.057419078628811036, 'ic_std': 0.15441520701925449, 'ir': 0.37184860051802593}\n", "Dropped 10.4% entries from factor data: 10.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.3846484425260606, 'return_min': -3.0501104623070763, 'ic_mean': 0.03095168794706311, 'ic_std': 0.13490171120562094, 'ir': 0.22943880897022637}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 35.0% entries from factor data: 35.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5522990891536637, 'return_min': -1.3583281126061664, 'ic_mean': 0.0267676266919103, 'ic_std': 0.08360658138514493, 'ir': 0.32016171751601274}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 75.1% entries from factor data: 12.1% in forward returns computation and 63.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 75.1%, consider increasing it.\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.23985699885354, 'return_min': 1.5045878829922366, 'ic_mean': 0.00842136473100255, 'ic_std': 0.10071566807243452, 'ir': 0.0836152397355486}\n", "Dropped 39.6% entries from factor data: 12.4% in forward returns computation and 27.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 39.6%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 12.2% entries from factor data: 12.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.4327744433118017, 'return_min': -3.593150748799623, 'ic_mean': 0.015045834724664383, 'ic_std': 0.11399093509942504, 'ir': 0.13199150188162878}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5782983217028637, 'return_min': -0.3921365228876095, 'ic_mean': 0.00869432243118676, 'ic_std': 0.08866754050838607, 'ir': 0.09805530165082747}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.8% entries from factor data: 9.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.217439473079732, 'return_min': -11.888072170169739, 'ic_mean': 0.08549084796401202, 'ic_std': 0.18689626631725703, 'ir': 0.4574240547903243}\n", "Dropped 26.2% entries from factor data: 14.9% in forward returns computation and 11.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -11.609777397113552, 'return_min': 5.560884961284263, 'ic_mean': -0.08016786157515762, 'ic_std': 0.17075763357491386, 'ir': -0.4694833249723317}\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.916060136926024, 'return_min': 0.15269965446318423, 'ic_mean': 0.0009798099446858913, 'ic_std': 0.10113502896706562, 'ir': 0.009688136293558228}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 10.0% entries from factor data: 10.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.496067842141294, 'return_min': -5.234986241404727, 'ic_mean': 0.06648288590244722, 'ic_std': 0.17130996169472776, 'ir': 0.3880853468458472}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 23.3% entries from factor data: 10.8% in forward returns computation and 12.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.530546726462358, 'return_min': -13.539669977897795, 'ic_mean': 0.05965641981022122, 'ic_std': 0.15241952419505192, 'ir': 0.3913961818558011}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 47.1% entries from factor data: 5.5% in forward returns computation and 41.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.1%, consider increasing it.\n", "Dropped 5.5% entries from factor data: 5.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.4494168251287807, 'return_min': 0.7682202430325802, 'ic_mean': -0.008577092645715845, 'ic_std': 0.08228498300976415, 'ir': -0.10423642725548189}\n", "Dropped 100.0% entries from factor data: 5.5% in forward returns computation and 94.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.7% entries from factor data: 8.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3806914434466577, 'return_min': -7.519618319413723, 'ic_mean': -0.044982018076591954, 'ic_std': 0.17342842052770607, 'ir': -0.25936935791562404}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    1.507652    0.608682  0.028753  0.084292  0.341108\n", "alpha003    4.052239   -2.155361  0.016189  0.090422  0.179035\n", "alpha005    2.455905  -13.297041  0.075158  0.174626  0.430391\n", "alpha006   -1.983585   -3.946821  0.001251  0.143759  0.008702\n", "alpha008    1.825293   -6.432593  0.050896  0.178321  0.285417\n", "alpha009   -5.199849   -5.921573  0.058519  0.164180  0.356431\n", "alpha010   -6.241363   -4.733789  0.044450  0.151099  0.294177\n", "alpha011    5.684061   -7.964318  0.014976  0.126664  0.118232\n", "alpha012   -3.671192    1.330133  0.012063  0.128970  0.093534\n", "alpha013   -1.515724   -3.880514  0.027757  0.096997  0.286161\n", "alpha014   -1.762845   -2.975830  0.009051  0.135488  0.066806\n", "alpha015    1.590340   -0.609465  0.020024  0.093722  0.213649\n", "alpha016   -0.627014   -3.822511  0.030017  0.096429  0.311286\n", "alpha017    0.175245   -3.164678  0.043452  0.159390  0.272613\n", "alpha018    2.063481   -8.514927  0.047672  0.158308  0.301134\n", "alpha020   -0.542212   -2.097774  0.012406  0.136855  0.090649\n", "alpha022   -3.776834   -1.639684  0.002938  0.107141  0.027419\n", "alpha024   -0.873216   -3.147631  0.022994  0.131721  0.174568\n", "alpha025   -1.479478  -12.551045  0.079896  0.177685  0.449648\n", "alpha026    0.906243   -3.172807  0.020781  0.121953  0.170398\n", "alpha028   -1.795397   -0.980099 -0.001082  0.111591 -0.009696\n", "alpha029    4.480449   -5.593057  0.031354  0.130346  0.240547\n", "alpha030   -1.894619   -2.256532  0.025942  0.122818  0.211220\n", "alpha031    4.021630   -1.686495  0.037945  0.131026  0.289602\n", "alpha032    2.097984   -7.082794  0.009328  0.114962  0.081137\n", "alpha033   -6.751634   -7.244244  0.065540  0.188824  0.347098\n", "alpha034    2.704360   -1.017823  0.055649  0.166860  0.333507\n", "alpha037   -3.427827   -4.119256  0.055706  0.166697  0.334176\n", "alpha038   -0.413045   -5.765908  0.057815  0.167555  0.345050\n", "alpha040    1.412530   -2.865520  0.023885  0.171066  0.139625\n", "alpha041  -10.753780    2.484931 -0.001751  0.149270 -0.011732\n", "alpha042    8.035868   -9.107466  0.060060  0.150026  0.400329\n", "alpha043   -5.647480    3.122675 -0.012753  0.127648 -0.099907\n", "alpha044    1.036182   -2.751764  0.028319  0.113339  0.249862\n", "alpha045   -5.786270   -1.860721  0.001551  0.114585  0.013535\n", "alpha047   -0.684834   -4.271937  0.007003  0.174051  0.040233\n", "alpha049   -4.369897   -9.905272  0.065167  0.164212  0.396847\n", "alpha050    1.024209   -3.743581  0.025385  0.105288  0.241104\n", "alpha053   -1.664802    3.464008  0.024282  0.159183  0.152541\n", "alpha054    0.887646   -4.662958  0.046891  0.155746  0.301074\n", "alpha055    0.936379   -1.389137  0.027237  0.102011  0.267004\n", "alpha057   -1.257020   -7.030723  0.057419  0.154415  0.371849\n", "alpha060   -2.384648   -3.050110  0.030952  0.134902  0.229439\n", "alpha066    1.552299   -1.358328  0.026768  0.083607  0.320162\n", "alpha072   -2.239857    1.504588  0.008421  0.100716  0.083615\n", "alpha077   -1.432774   -3.593151  0.015046  0.113991  0.131992\n", "alpha078    0.578298   -0.392137  0.008694  0.088668  0.098055\n", "alpha083    5.217439  -11.888072  0.085491  0.186896  0.457424\n", "alpha084  -11.609777    5.560885 -0.080168  0.170758 -0.469483\n", "alpha085   -2.916060    0.152700  0.000980  0.101135  0.009688\n", "alpha088    4.496068   -5.234986  0.066483  0.171310  0.388085\n", "alpha094    3.530547  -13.539670  0.059656  0.152420  0.391396\n", "alpha098   -2.449417    0.768220 -0.008577  0.082285 -0.104236\n", "alpha101   -1.380691   -7.519618 -0.044982  0.173428 -0.259369\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 13.3% in forward returns computation and 86.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.838948373733221, 'return_min': -0.9917777644019576, 'ic_mean': 0.028807785722627002, 'ic_std': 0.10107743353018475, 'ir': 0.2850070952189752}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.3059233186464283, 'return_min': -2.00435230334417, 'ic_mean': 0.004305462482494705, 'ic_std': 0.09300893137817647, 'ir': 0.04629084990761366}\n", "Dropped 70.4% entries from factor data: 11.2% in forward returns computation and 59.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 70.4%, consider increasing it.\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.317456753535012, 'return_min': -8.144857513006087, 'ic_mean': 0.07164213754653416, 'ic_std': 0.1853241550392178, 'ir': 0.38657744065458405}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.4452239670292855, 'return_min': -2.3167565879878005, 'ic_mean': 0.009636420028618269, 'ic_std': 0.12262757012661095, 'ir': 0.07858281802916607}\n", "Dropped 95.8% entries from factor data: 15.1% in forward returns computation and 80.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 95.8%, consider increasing it.\n", "Dropped 12.1% entries from factor data: 12.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.097384351078425, 'return_min': -2.737523673974618, 'ic_mean': 0.03891075519503544, 'ic_std': 0.15655137384479673, 'ir': 0.2485494329395737}\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.6996230744091854, 'return_min': 3.3103144659185624, 'ic_mean': 0.028940960595831837, 'ic_std': 0.1691965531758813, 'ir': 0.171049350903428}\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.1304783201879793, 'return_min': 3.1445068967905776, 'ic_mean': 0.018319621877061786, 'ic_std': 0.15281349935583394, 'ir': 0.11988222214847409}\n", "Dropped 10.5% entries from factor data: 10.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.834257448740111, 'return_min': -4.955065579287377, 'ic_mean': 0.02827758774498035, 'ic_std': 0.1205407981923491, 'ir': 0.23458935206200726}\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.352908774696516, 'return_min': -0.3940798316304228, 'ic_mean': 0.01929300326088231, 'ic_std': 0.11340890870847244, 'ir': 0.1701189393372673}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.04343897459069268, 'return_min': -0.3831641209273151, 'ic_mean': 0.019430627642075104, 'ic_std': 0.09305238304009773, 'ir': 0.20881386383949072}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.479293847594711, 'return_min': -1.0533328494899408, 'ic_mean': 0.011207156500705397, 'ic_std': 0.11411595092699055, 'ir': 0.09820850117505085}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.5115169435108164, 'return_min': -1.9257421722662649, 'ic_mean': 0.019261031993951475, 'ic_std': 0.09182950312019143, 'ir': 0.20974775360312678}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3518177290294808, 'return_min': -1.167054842275972, 'ic_mean': 0.013810372354656215, 'ic_std': 0.09063322271665489, 'ir': 0.15237648999673498}\n", "Dropped 13.4% entries from factor data: 13.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.517843383111565, 'return_min': 2.39482205238728, 'ic_mean': 0.022685187066482017, 'ic_std': 0.16753838012513744, 'ir': 0.1354029270758022}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.2572243394171068, 'return_min': -1.0839485485991673, 'ic_mean': 0.02714574370910788, 'ic_std': 0.14195743031710106, 'ir': 0.19122453575322107}\n", "Dropped 41.4% entries from factor data: 41.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 41.4%, consider increasing it.\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.8478197231973645, 'return_min': -0.3902083501350351, 'ic_mean': 0.016007487604046636, 'ic_std': 0.11983826067004585, 'ir': 0.13357576715937589}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1185566846361183, 'return_min': 1.1042070659672554, 'ic_mean': 0.009050677251110627, 'ic_std': 0.1042547269332228, 'ir': 0.08681311166742361}\n", "Dropped 94.4% entries from factor data: 5.3% in forward returns computation and 89.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 94.4%, consider increasing it.\n", "Dropped 10.8% entries from factor data: 10.4% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.753810771958463, 'return_min': -0.9251742537530472, 'ic_mean': 0.028558899490536558, 'ic_std': 0.13139471499134528, 'ir': 0.2173519649737638}\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.9540853756750955, 'return_min': 1.520912853683054, 'ic_mean': 0.03788329641977804, 'ic_std': 0.1785864287568851, 'ir': 0.21212864092461178}\n", "Dropped 5.7% entries from factor data: 5.3% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.724877530377757, 'return_min': -0.7376102437550713, 'ic_mean': 0.02713931583294292, 'ic_std': 0.11216136381711386, 'ir': 0.24196670679927962}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.810796240531154, 'return_min': -0.5936802064909763, 'ic_mean': 0.007012963054301636, 'ic_std': 0.10727887901936332, 'ir': 0.06537133048375561}\n", "Dropped 11.6% entries from factor data: 11.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.101974637915397, 'return_min': -0.12128809140699026, 'ic_mean': 0.014307972337985983, 'ic_std': 0.13822826677618943, 'ir': 0.10350974277318081}\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.794159829248132, 'return_min': -0.6352820908661627, 'ic_mean': 0.015913164025014692, 'ic_std': 0.14422203095367728, 'ir': 0.11033795544125881}\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.969155110012835, 'return_min': 3.860802998294499, 'ic_mean': 0.018381640039734966, 'ic_std': 0.1300768905568107, 'ir': 0.14131364888144246}\n", "Dropped 18.5% entries from factor data: 18.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1513545961427951, 'return_min': -3.51487082825952, 'ic_mean': 0.008143764292236393, 'ic_std': 0.06191264744004484, 'ir': 0.13153636016167256}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5582525217873169, 'return_min': 0.6314719828925952, 'ic_mean': 0.04312851697079056, 'ic_std': 0.19073396127712802, 'ir': 0.22611870839366005}\n", "Dropped 10.2% entries from factor data: 10.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.4876833584340794, 'return_min': 6.046950782236937, 'ic_mean': 0.024569637316144827, 'ic_std': 0.14496201765083058, 'ir': 0.16949017207614764}\n", "Dropped 48.0% entries from factor data: 14.8% in forward returns computation and 33.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 48.0%, consider increasing it.\n", "Dropped 33.9% entries from factor data: 33.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.2880638718715254, 'return_min': -2.3377100634169867, 'ic_mean': 0.0273913294229565, 'ic_std': 0.12369372842941047, 'ir': 0.2214447714589522}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.92868064235574, 'return_min': 0.33991521897647203, 'ic_mean': 0.030186345000784424, 'ic_std': 0.1550304151349189, 'ir': 0.19471240514007554}\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.008685929456797581, 'return_min': -3.431377126806856, 'ic_mean': 0.0494813983179626, 'ic_std': 0.1762129605878252, 'ir': 0.2808045342005413}\n", "Dropped 41.4% entries from factor data: 41.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 41.4%, consider increasing it.\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5958967219488187, 'return_min': 0.9491557993657196, 'ic_mean': 0.017647928295721094, 'ic_std': 0.13350138778489568, 'ir': 0.1321928452470947}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.474375306737887, 'return_min': 4.523450319093847, 'ic_mean': 0.0016369542956809408, 'ic_std': 0.13515143013445907, 'ir': 0.012112001286648409}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3588048054511432, 'return_min': -7.123435411864332, 'ic_mean': 0.04042060780011587, 'ic_std': 0.1663738920973977, 'ir': 0.24295042503695866}\n", "Dropped 15.6% entries from factor data: 15.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5287351517269112, 'return_min': -0.6094649299748234, 'ic_mean': 0.008172145725860018, 'ic_std': 0.12436851756638274, 'ir': 0.0657091190421086}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.483737819251262, 'return_min': -0.8057218129253751, 'ic_mean': 0.01847845003248095, 'ic_std': 0.1021175270112341, 'ir': 0.1809527764068171}\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5543316585421287, 'return_min': -1.3705988036039063, 'ic_mean': 0.014480667164122961, 'ic_std': 0.10151147139331633, 'ir': 0.14265054939471983}\n", "Dropped 84.2% entries from factor data: 10.2% in forward returns computation and 74.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 84.2%, consider increasing it.\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.9354300913522184, 'return_min': 1.8641372136896095, 'ic_mean': 0.004945623986469939, 'ic_std': 0.15572689280298357, 'ir': 0.031758316739337045}\n", "Dropped 14.8% entries from factor data: 10.2% in forward returns computation and 4.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.27614856405788757, 'return_min': 1.546275812387421, 'ic_mean': 0.03839627352676107, 'ic_std': 0.16306688219342144, 'ir': 0.2354633449189113}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5186222495168202, 'return_min': -0.31792879739200863, 'ic_mean': 0.017386934024773842, 'ic_std': 0.09738367023489555, 'ir': 0.17854054979480094}\n", "Dropped 32.1% entries from factor data: 10.2% in forward returns computation and 21.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.090655752581942, 'return_min': 0.3845761850773677, 'ic_mean': 0.03623769798358197, 'ic_std': 0.1585614168572789, 'ir': 0.22854045266383763}\n", "Dropped 38.8% entries from factor data: 38.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 38.8%, consider increasing it.\n", "Dropped 10.9% entries from factor data: 10.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.12658727868597452, 'return_min': 0.046830827233890204, 'ic_mean': 0.025209892381757592, 'ic_std': 0.1653989958026872, 'ir': 0.152418654414515}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.6301290202956906, 'return_min': -4.520311214689654, 'ic_mean': 0.041718212735871146, 'ic_std': 0.1877993028988794, 'ir': 0.22214253243705773}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.9093774181343512, 'return_min': -1.6472104154485745, 'ic_mean': 0.03147733751246703, 'ic_std': 0.09728700913635535, 'ir': 0.3235512921190647}\n", "Dropped 14.5% entries from factor data: 14.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.9796359637577225, 'return_min': -4.639386997975503, 'ic_mean': 0.04956372207145063, 'ic_std': 0.18321202159850564, 'ir': 0.2705265824753876}\n", "Dropped 11.3% entries from factor data: 11.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3050346971408295, 'return_min': -3.4198718632316716, 'ic_mean': 0.025375271697981112, 'ic_std': 0.15870419788526943, 'ir': 0.15989036229731884}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 36.1% entries from factor data: 36.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 36.1%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 23.9% entries from factor data: 12.6% in forward returns computation and 11.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8031033606381577, 'return_min': 0.07625874187011661, 'ic_mean': 0.012347726658805083, 'ic_std': 0.08379992680413716, 'ir': 0.1473477021962683}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.539943310488791, 'return_min': -2.1479150092107435, 'ic_mean': 0.016608592608159757, 'ic_std': 0.09720911266144057, 'ir': 0.17085427645044024}\n", "Dropped 29.8% entries from factor data: 12.9% in forward returns computation and 16.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3577701800635573, 'return_min': 0.2487160994735227, 'ic_mean': -0.011011313871793302, 'ic_std': 0.13509834046597996, 'ir': -0.08150591512681192}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 12.8% entries from factor data: 12.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.119426772567822, 'return_min': 0.7366497484828649, 'ic_mean': 0.00411248172085533, 'ic_std': 0.1148816090581666, 'ir': 0.035797563722955054}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.2201902938702158, 'return_min': 3.0476719880989656, 'ic_mean': -0.0004162310875653405, 'ic_std': 0.0896056230129348, 'ir': -0.004645144730540588}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 10.9% entries from factor data: 10.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.02329843449623, 'return_min': -10.187048832726031, 'ic_mean': 0.07440962727475246, 'ic_std': 0.18913933445345485, 'ir': 0.39341170090171734}\n", "Dropped 21.9% entries from factor data: 15.0% in forward returns computation and 6.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7911771186795988, 'return_min': 4.531171759889485, 'ic_mean': -0.05598158215404965, 'ic_std': 0.17581550798333556, 'ir': -0.3184109456337366}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.2533612939823477, 'return_min': -2.598861203847447, 'ic_mean': 0.01915089216549555, 'ic_std': 0.10133651788222839, 'ir': 0.18898312835016098}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 11.0% entries from factor data: 11.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.8640456948693096, 'return_min': -3.0087301824377466, 'ic_mean': 0.035600349276804145, 'ic_std': 0.14164349901472684, 'ir': 0.25133768598234596}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 17.3% entries from factor data: 11.6% in forward returns computation and 5.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7428282694220698, 'return_min': 2.6589840210022864, 'ic_mean': 0.023572000230597256, 'ic_std': 0.16739246715625844, 'ir': 0.1408187634189843}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 44.5% entries from factor data: 5.3% in forward returns computation and 39.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 44.5%, consider increasing it.\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.42402092450322115, 'return_min': -1.0853805121291416, 'ic_mean': 0.006421144282281454, 'ic_std': 0.08520199540130731, 'ir': 0.0753637781842716}\n", "Dropped 100.0% entries from factor data: 5.3% in forward returns computation and 94.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.620849735195385, 'return_min': -1.9632958853976756, 'ic_mean': -0.03310829369357441, 'ic_std': 0.18451508267560945, 'ir': -0.17943407776469486}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    4.838948   -0.991778  0.028808  0.101077  0.285007\n", "alpha003   -0.305923   -2.004352  0.004305  0.093009  0.046291\n", "alpha005    7.317457   -8.144858  0.071642  0.185324  0.386577\n", "alpha006    1.445224   -2.316757  0.009636  0.122628  0.078583\n", "alpha008    3.097384   -2.737524  0.038911  0.156551  0.248549\n", "alpha009   -2.699623    3.310314  0.028941  0.169197  0.171049\n", "alpha010   -3.130478    3.144507  0.018320  0.152813  0.119882\n", "alpha011    6.834257   -4.955066  0.028278  0.120541  0.234589\n", "alpha012    2.352909   -0.394080  0.019293  0.113409  0.170119\n", "alpha013   -0.043439   -0.383164  0.019431  0.093052  0.208814\n", "alpha014    3.479294   -1.053333  0.011207  0.114116  0.098209\n", "alpha015    2.511517   -1.925742  0.019261  0.091830  0.209748\n", "alpha016   -1.351818   -1.167055  0.013810  0.090633  0.152376\n", "alpha017   -5.517843    2.394822  0.022685  0.167538  0.135403\n", "alpha018   -1.257224   -1.083949  0.027146  0.141957  0.191225\n", "alpha020    2.847820   -0.390208  0.016007  0.119838  0.133576\n", "alpha022   -1.118557    1.104207  0.009051  0.104255  0.086813\n", "alpha024    0.753811   -0.925174  0.028559  0.131395  0.217352\n", "alpha025    0.954085    1.520913  0.037883  0.178586  0.212129\n", "alpha026    3.724878   -0.737610  0.027139  0.112161  0.241967\n", "alpha028    1.810796   -0.593680  0.007013  0.107279  0.065371\n", "alpha029    6.101975   -0.121288  0.014308  0.138228  0.103510\n", "alpha030   -1.794160   -0.635282  0.015913  0.144222  0.110338\n", "alpha031   -4.969155    3.860803  0.018382  0.130077  0.141314\n", "alpha032   -1.151355   -3.514871  0.008144  0.061913  0.131536\n", "alpha033   -0.558253    0.631472  0.043129  0.190734  0.226119\n", "alpha034    3.487683    6.046951  0.024570  0.144962  0.169490\n", "alpha036    1.288064   -2.337710  0.027391  0.123694  0.221445\n", "alpha037   -1.928681    0.339915  0.030186  0.155030  0.194712\n", "alpha038   -0.008686   -3.431377  0.049481  0.176213  0.280805\n", "alpha040    0.595897    0.949156  0.017648  0.133501  0.132193\n", "alpha041   -3.474375    4.523450  0.001637  0.135151  0.012112\n", "alpha042    3.358805   -7.123435  0.040421  0.166374  0.242950\n", "alpha043   -0.528735   -0.609465  0.008172  0.124369  0.065709\n", "alpha044   -0.483738   -0.805722  0.018478  0.102118  0.180953\n", "alpha045    0.554332   -1.370599  0.014481  0.101511  0.142651\n", "alpha047   -0.935430    1.864137  0.004946  0.155727  0.031758\n", "alpha049    0.276149    1.546276  0.038396  0.163067  0.235463\n", "alpha050    0.518622   -0.317929  0.017387  0.097384  0.178541\n", "alpha051    6.090656    0.384576  0.036238  0.158561  0.228540\n", "alpha053   -0.126587    0.046831  0.025210  0.165399  0.152419\n", "alpha054    2.630129   -4.520311  0.041718  0.187799  0.222143\n", "alpha055    2.909377   -1.647210  0.031477  0.097287  0.323551\n", "alpha057    4.979636   -4.639387  0.049564  0.183212  0.270527\n", "alpha060    3.305035   -3.419872  0.025375  0.158704  0.159890\n", "alpha071    0.803103    0.076259  0.012348  0.083800  0.147348\n", "alpha072    3.539943   -2.147915  0.016609  0.097209  0.170854\n", "alpha073    3.357770    0.248716 -0.011011  0.135098 -0.081506\n", "alpha077   -1.119427    0.736650  0.004112  0.114882  0.035798\n", "alpha078   -1.220190    3.047672 -0.000416  0.089606 -0.004645\n", "alpha083    9.023298  -10.187049  0.074410  0.189139  0.393412\n", "alpha084   -1.791177    4.531172 -0.055982  0.175816 -0.318411\n", "alpha085    2.253361   -2.598861  0.019151  0.101337  0.188983\n", "alpha088   -1.864046   -3.008730  0.035600  0.141643  0.251338\n", "alpha094   -1.742828    2.658984  0.023572  0.167392  0.140819\n", "alpha098    0.424021   -1.085381  0.006421  0.085202  0.075364\n", "alpha101    2.620850   -1.963296 -0.033108  0.184515 -0.179434\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 9.6% in forward returns computation and 90.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.10850763777270345, 'return_min': -0.8845423047521006, 'ic_mean': 0.017582333839091524, 'ic_std': 0.1097536218246183, 'ir': 0.1601982107450391}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.403933849508654, 'return_min': 0.6437257555225173, 'ic_mean': 0.0005507887296556778, 'ic_std': 0.08722481677691898, 'ir': 0.006314587407667961}\n", "Dropped 73.7% entries from factor data: 8.3% in forward returns computation and 65.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 73.7%, consider increasing it.\n", "Dropped 8.4% entries from factor data: 8.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.912027521961296, 'return_min': -15.389324518675362, 'ic_mean': 0.07876434190955893, 'ic_std': 0.17838641600801033, 'ir': 0.4415377788969204}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5095289464929209, 'return_min': -0.1457483376532398, 'ic_mean': 0.005561503017342348, 'ic_std': 0.12646761415346564, 'ir': 0.04397570915344056}\n", "Dropped 92.4% entries from factor data: 10.5% in forward returns computation and 81.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 92.4%, consider increasing it.\n", "Dropped 8.9% entries from factor data: 8.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.5300500354985545, 'return_min': -6.01952975291975, 'ic_mean': 0.0326134579084693, 'ic_std': 0.1654914453861553, 'ir': 0.19707035510125334}\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.9476100317183942, 'return_min': -1.583938182598832, 'ic_mean': 0.02660467898184374, 'ic_std': 0.16117970177807295, 'ir': 0.16506221744023025}\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.205653857997424, 'return_min': -0.9617677264872615, 'ic_mean': 0.017592329690143647, 'ic_std': 0.14716247927410678, 'ir': 0.11954358051671542}\n", "Dropped 7.9% entries from factor data: 7.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.461018354389168, 'return_min': -0.389114369906407, 'ic_mean': 0.012963230036430058, 'ic_std': 0.11863480925906426, 'ir': 0.10927003733046088}\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7902783388402668, 'return_min': 0.2905878901482417, 'ic_mean': 0.009355035426360712, 'ic_std': 0.12442685230321561, 'ir': 0.07518502038099814}\n", "Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3905959354354458, 'return_min': 0.08001918848643541, 'ic_mean': 0.014817510215033037, 'ic_std': 0.107862339247288, 'ir': 0.13737427093122864}\n", "Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.83116419134355, 'return_min': 0.7456969339725283, 'ic_mean': 0.012192315601087226, 'ic_std': 0.11798127168527972, 'ir': 0.10334111021968613}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.614175681872055, 'return_min': -1.7872800483809126, 'ic_mean': 0.017201419366166024, 'ic_std': 0.08837354784415595, 'ir': 0.1946444358723744}\n", "Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.292648902746496, 'return_min': -1.0317760445721635, 'ic_mean': 0.014931773945643764, 'ic_std': 0.10176146639247724, 'ir': 0.14673308546925187}\n", "Dropped 9.6% entries from factor data: 9.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.04897858141839073, 'return_min': 0.39437286797783244, 'ic_mean': 0.01832803947476365, 'ic_std': 0.14738578926373666, 'ir': 0.12435418344143676}\n", "Dropped 8.0% entries from factor data: 8.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.9731338735494717, 'return_min': -1.1801146519896388, 'ic_mean': 0.0134267436493753, 'ic_std': 0.16708233229329164, 'ir': 0.08036004444686809}\n", "Dropped 29.6% entries from factor data: 29.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.4276424741012086, 'return_min': -3.2239766697006456, 'ic_mean': 0.02263988989789656, 'ic_std': 0.1634198250793285, 'ir': 0.13853820909982328}\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.409016103529284, 'return_min': 3.581321678596261, 'ic_mean': -0.007286953500759306, 'ic_std': 0.13015477172512185, 'ir': -0.05598683324610536}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3384886148279946, 'return_min': -0.4239102412839646, 'ic_mean': 0.006582341507024286, 'ic_std': 0.10755264716527636, 'ir': 0.06120111108849965}\n", "Dropped 92.7% entries from factor data: 5.2% in forward returns computation and 87.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 92.7%, consider increasing it.\n", "Dropped 12.6% entries from factor data: 7.9% in forward returns computation and 4.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.063966205770983, 'return_min': -1.5014716525707517, 'ic_mean': 0.013269769303719701, 'ic_std': 0.14329829462674942, 'ir': 0.09260242306639872}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.3756958089206073, 'return_min': -0.8536121295943389, 'ic_mean': 0.035873306559529534, 'ic_std': 0.17217837045490442, 'ir': 0.20834966938501248}\n", "Dropped 7.6% entries from factor data: 5.2% in forward returns computation and 2.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.43760601109887887, 'return_min': -3.5998662173986062, 'ic_mean': 0.016809060294181916, 'ic_std': 0.11954445729756956, 'ir': 0.140609281886996}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.520379602366177, 'return_min': -6.402580751482256, 'ic_mean': 0.025418216764603003, 'ic_std': 0.11049731834664762, 'ir': 0.23003469355574788}\n", "Dropped 8.6% entries from factor data: 8.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7473658436429886, 'return_min': -0.5020966227708978, 'ic_mean': 0.008944891194262861, 'ic_std': 0.1345357159264767, 'ir': 0.06648711186218545}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.8827480282801456, 'return_min': 1.135578649640312, 'ic_mean': 0.017490808880645907, 'ic_std': 0.12751945566780093, 'ir': 0.13716188474181507}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1650739530078003, 'return_min': -0.2133820352101523, 'ic_mean': 0.014323666749416212, 'ic_std': 0.12046941031091997, 'ir': 0.11889878694058686}\n", "Dropped 12.9% entries from factor data: 12.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.921003553921885, 'return_min': -3.763724636750565, 'ic_mean': 0.009608236448351985, 'ic_std': 0.07832986026424683, 'ir': 0.12266377618878}\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.0167550483031196, 'return_min': -1.6169756102268895, 'ic_mean': 0.03515095130340301, 'ic_std': 0.17852380730394546, 'ir': 0.19689783583629722}\n", "Dropped 7.8% entries from factor data: 7.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.3796007411591713, 'return_min': 8.777918319797795, 'ic_mean': 0.004786348132730479, 'ic_std': 0.14347119003771253, 'ir': 0.033361040160553135}\n", "Dropped 49.8% entries from factor data: 10.3% in forward returns computation and 39.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 49.8%, consider increasing it.\n", "Dropped 23.2% entries from factor data: 23.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.618064514168129, 'return_min': -3.9229340598190277, 'ic_mean': 0.026294049804687802, 'ic_std': 0.12692127565713154, 'ir': 0.20716818097321396}\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.2577223134925255, 'return_min': -1.638039711611139, 'ic_mean': 0.017855120173774042, 'ic_std': 0.14698581571031238, 'ir': 0.12147512389198072}\n", "Dropped 8.4% entries from factor data: 8.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.10376407822033, 'return_min': -1.4466241661792445, 'ic_mean': 0.02569263621447589, 'ic_std': 0.17847362433822267, 'ir': 0.14395760891696896}\n", "Dropped 29.6% entries from factor data: 29.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.960617564711111, 'return_min': -5.996771420800906, 'ic_mean': 0.02895130427753851, 'ic_std': 0.18625463077007895, 'ir': 0.15543937972354252}\n", "Dropped 8.4% entries from factor data: 8.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.0748638726963975, 'return_min': 0.21069660753436636, 'ic_mean': 0.013660451694524052, 'ic_std': 0.14791941111712867, 'ir': 0.0923506360075159}\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -10.710167826800676, 'return_min': 8.21809537840057, 'ic_mean': -0.023027583928740856, 'ic_std': 0.14092974590435023, 'ir': -0.16339761191629337}\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.561251655019749, 'return_min': -14.907112862450056, 'ic_mean': 0.051196718379155896, 'ic_std': 0.16702002256812762, 'ir': 0.30653042426857957}\n", "Dropped 12.6% entries from factor data: 10.6% in forward returns computation and 1.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.304617055774784, 'return_min': -1.1999824809150184, 'ic_mean': 0.006359605803450492, 'ic_std': 0.12626628440520132, 'ir': 0.050366618717011355}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3209343355879977, 'return_min': 2.545718247128903, 'ic_mean': 0.007647274272662373, 'ic_std': 0.12259235029187927, 'ir': 0.06237970195085607}\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.4310110477678819, 'return_min': -4.046892698518212, 'ic_mean': 0.005757781655463866, 'ic_std': 0.10356924655318983, 'ir': 0.05559354583608808}\n", "Dropped 69.9% entries from factor data: 7.7% in forward returns computation and 62.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 69.9%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.1394520676312183, 'return_min': -6.463641073697346, 'ic_mean': 0.020747644616685046, 'ic_std': 0.14692756427243053, 'ir': 0.14121002222711}\n", "Dropped 25.6% entries from factor data: 7.7% in forward returns computation and 17.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -21.279341354701884, 'return_min': -1.0535591668092703, 'ic_mean': 0.025890551224962848, 'ic_std': 0.1645044065636206, 'ir': 0.1573851531749085}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5138405230664667, 'return_min': 4.492412777949983, 'ic_mean': 0.005238103621374174, 'ic_std': 0.09611479684120847, 'ir': 0.054498410167042856}\n", "Dropped 41.4% entries from factor data: 7.7% in forward returns computation and 33.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 41.4%, consider increasing it.\n", "Dropped 27.0% entries from factor data: 27.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6634501327894569, 'return_min': -2.7149968605788555, 'ic_mean': 0.01367230954536651, 'ic_std': 0.164271660022294, 'ir': 0.08322987387788607}\n", "Dropped 8.3% entries from factor data: 8.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.738291734298517, 'return_min': -4.83492435277344, 'ic_mean': 0.044362064747330295, 'ic_std': 0.13687766236356796, 'ir': 0.3241001050229648}\n", "Dropped 8.9% entries from factor data: 7.7% in forward returns computation and 1.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.063316415354624, 'return_min': -11.602309206126815, 'ic_mean': 0.0615856900637672, 'ic_std': 0.15343241213606842, 'ir': 0.40138644244966426}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0231559162710013, 'return_min': 2.1515750874501194, 'ic_mean': 0.012621507253940173, 'ic_std': 0.10075291168643283, 'ir': 0.12527188587086519}\n", "Dropped 10.1% entries from factor data: 10.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.32521099466599, 'return_min': -12.28924005343024, 'ic_mean': 0.0692101774907488, 'ic_std': 0.15510505071297204, 'ir': 0.44621485356286006}\n", "Dropped 8.4% entries from factor data: 8.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.854875465233892, 'return_min': -9.212416276808133, 'ic_mean': 0.05514997202896119, 'ic_std': 0.14987959957505212, 'ir': 0.3679618319326032}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 32.9% entries from factor data: 32.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.2415369133876872, 'return_min': -5.459408788294207, 'ic_mean': 0.012014990767317713, 'ic_std': 0.09401915340466827, 'ir': 0.1277930116601235}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 29.8% entries from factor data: 9.2% in forward returns computation and 20.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5180783322944471, 'return_min': 5.1643858061889425, 'ic_mean': 7.605317426182312e-05, 'ic_std': 0.08635358001454807, 'ir': 0.0008807182545183462}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0510311964417873, 'return_min': -0.19401129944696116, 'ic_mean': 0.009899278451051577, 'ic_std': 0.10411947965531307, 'ir': 0.09507614217649839}\n", "Dropped 35.0% entries from factor data: 9.3% in forward returns computation and 25.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 35.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.3% entries from factor data: 9.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.4359175425302535, 'return_min': 2.665229347813547, 'ic_mean': -0.005286444511907405, 'ic_std': 0.1280488430267947, 'ir': -0.04128459412008273}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3569343624306747, 'return_min': 1.356063708695121, 'ic_mean': 0.007476725166909044, 'ic_std': 0.09744288396239043, 'ir': 0.07672930913862115}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.3% entries from factor data: 8.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 16.945162443351425, 'return_min': -20.967567075990125, 'ic_mean': 0.10065398955005533, 'ic_std': 0.16643459428369275, 'ir': 0.6047660342686184}\n", "Dropped 20.7% entries from factor data: 10.4% in forward returns computation and 10.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.625257721428746, 'return_min': 0.6992679932227297, 'ic_mean': -0.036758460449432075, 'ic_std': 0.17347301145515714, 'ir': -0.21189728673692942}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.873742212729958, 'return_min': -0.18687502571057557, 'ic_mean': 0.005014079504774535, 'ic_std': 0.10747641715566628, 'ir': 0.04665283452380313}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 8.3% entries from factor data: 8.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.652660687693098, 'return_min': 0.44767354545394156, 'ic_mean': 0.02331348651347403, 'ic_std': 0.1466785835769976, 'ir': 0.15894267550815164}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 20.7% entries from factor data: 8.6% in forward returns computation and 12.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.5635525840046505, 'return_min': 0.2363638999569595, 'ic_mean': 0.005468667948475259, 'ic_std': 0.1713648394608464, 'ir': 0.031912427109790774}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 56.9% entries from factor data: 5.2% in forward returns computation and 51.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 56.9%, consider increasing it.\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.22895963249447426, 'return_min': 0.20860249566378997, 'ic_mean': -0.004982935571539421, 'ic_std': 0.09253559135263119, 'ir': -0.053848854248422486}\n", "Dropped 100.0% entries from factor data: 5.2% in forward returns computation and 94.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5503991311273104, 'return_min': -0.19464112831490965, 'ic_mean': -0.02748950573891089, 'ic_std': 0.16745530772307568, 'ir': -0.16416025333977982}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    0.108508   -0.884542  0.017582  0.109754  0.160198\n", "alpha003   -1.403934    0.643726  0.000551  0.087225  0.006315\n", "alpha005    7.912028  -15.389325  0.078764  0.178386  0.441538\n", "alpha006   -0.509529   -0.145748  0.005562  0.126468  0.043976\n", "alpha008    2.530050   -6.019530  0.032613  0.165491  0.197070\n", "alpha009   -1.947610   -1.583938  0.026605  0.161180  0.165062\n", "alpha010   -1.205654   -0.961768  0.017592  0.147162  0.119544\n", "alpha011    4.461018   -0.389114  0.012963  0.118635  0.109270\n", "alpha012    0.790278    0.290588  0.009355  0.124427  0.075185\n", "alpha013   -1.390596    0.080019  0.014818  0.107862  0.137374\n", "alpha014    3.831164    0.745697  0.012192  0.117981  0.103341\n", "alpha015    2.614176   -1.787280  0.017201  0.088374  0.194644\n", "alpha016   -1.292649   -1.031776  0.014932  0.101761  0.146733\n", "alpha017   -0.048979    0.394373  0.018328  0.147386  0.124354\n", "alpha018   -2.973134   -1.180115  0.013427  0.167082  0.080360\n", "alpha019    0.427642   -3.223977  0.022640  0.163420  0.138538\n", "alpha020   -4.409016    3.581322 -0.007287  0.130155 -0.055987\n", "alpha022    3.338489   -0.423910  0.006582  0.107553  0.061201\n", "alpha024   -1.063966   -1.501472  0.013270  0.143298  0.092602\n", "alpha025    1.375696   -0.853612  0.035873  0.172178  0.208350\n", "alpha026   -0.437606   -3.599866  0.016809  0.119544  0.140609\n", "alpha028    5.520380   -6.402581  0.025418  0.110497  0.230035\n", "alpha029    0.747366   -0.502097  0.008945  0.134536  0.066487\n", "alpha030   -1.882748    1.135579  0.017491  0.127519  0.137162\n", "alpha031   -1.165074   -0.213382  0.014324  0.120469  0.118899\n", "alpha032    1.921004   -3.763725  0.009608  0.078330  0.122664\n", "alpha033   -1.016755   -1.616976  0.035151  0.178524  0.196898\n", "alpha034   -2.379601    8.777918  0.004786  0.143471  0.033361\n", "alpha036    3.618065   -3.922934  0.026294  0.126921  0.207168\n", "alpha037   -0.257722   -1.638040  0.017855  0.146986  0.121475\n", "alpha038   -5.103764   -1.446624  0.025693  0.178474  0.143958\n", "alpha039    3.960618   -5.996771  0.028951  0.186255  0.155439\n", "alpha040    2.074864    0.210697  0.013660  0.147919  0.092351\n", "alpha041  -10.710168    8.218095 -0.023028  0.140930 -0.163398\n", "alpha042    4.561252  -14.907113  0.051197  0.167020  0.306530\n", "alpha043   -4.304617   -1.199982  0.006360  0.126266  0.050367\n", "alpha044   -1.320934    2.545718  0.007647  0.122592  0.062380\n", "alpha045   -0.431011   -4.046893  0.005758  0.103569  0.055594\n", "alpha047    0.139452   -6.463641  0.020748  0.146928  0.141210\n", "alpha049  -21.279341   -1.053559  0.025891  0.164504  0.157385\n", "alpha050   -0.513841    4.492413  0.005238  0.096115  0.054498\n", "alpha052    1.663450   -2.714997  0.013672  0.164272  0.083230\n", "alpha053    5.738292   -4.834924  0.044362  0.136878  0.324100\n", "alpha054    5.063316  -11.602309  0.061586  0.153432  0.401386\n", "alpha055    1.023156    2.151575  0.012622  0.100753  0.125272\n", "alpha057   11.325211  -12.289240  0.069210  0.155105  0.446215\n", "alpha060    9.854875   -9.212416  0.055150  0.149880  0.367962\n", "alpha066   -2.241537   -5.459409  0.012015  0.094019  0.127793\n", "alpha071   -0.518078    5.164386  0.000076  0.086354  0.000881\n", "alpha072    1.051031   -0.194011  0.009899  0.104119  0.095076\n", "alpha077   -3.435918    2.665229 -0.005286  0.128049 -0.041285\n", "alpha078    3.356934    1.356064  0.007477  0.097443  0.076729\n", "alpha083   16.945162  -20.967567  0.100654  0.166435  0.604766\n", "alpha084   -6.625258    0.699268 -0.036758  0.173473 -0.211897\n", "alpha085    3.873742   -0.186875  0.005014  0.107476  0.046653\n", "alpha088   -2.652661    0.447674  0.023313  0.146679  0.158943\n", "alpha094   -6.563553    0.236364  0.005469  0.171365  0.031912\n", "alpha098   -0.228960    0.208602 -0.004983  0.092536 -0.053849\n", "alpha101    0.550399   -0.194641 -0.027490  0.167455 -0.164160\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 99.6% entries from factor data: 5.8% in forward returns computation and 93.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 99.6%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.5269158957375026, 'return_min': -2.732298632018848, 'ic_mean': 0.022637714437928322, 'ic_std': 0.07929747223073734, 'ir': 0.28547838665093633}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.3080609822168086, 'return_min': -5.127145200589656, 'ic_mean': 0.01062514820527827, 'ic_std': 0.08771547647913107, 'ir': 0.12113196703441682}\n", "Dropped 83.0% entries from factor data: 5.0% in forward returns computation and 78.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 83.0%, consider increasing it.\n", "Dropped 5.0% entries from factor data: 5.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.082291546187228, 'return_min': -11.457657360782747, 'ic_mean': 0.06231949187815281, 'ic_std': 0.13384305631631854, 'ir': 0.4656161745953393}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.8342024219883974, 'return_min': -4.807046256548597, 'ic_mean': 0.00924531668224347, 'ic_std': 0.1184846443198902, 'ir': 0.07802966144104333}\n", "Dropped 86.2% entries from factor data: 6.7% in forward returns computation and 79.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 86.2%, consider increasing it.\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.625064000403409, 'return_min': -1.121514100752563, 'ic_mean': 0.01536056450365289, 'ic_std': 0.14249548516542038, 'ir': 0.10779685044632183}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.619622883395344, 'return_min': 0.1302551042781097, 'ic_mean': 0.028930597192261518, 'ic_std': 0.1455098944500384, 'ir': 0.19882219900994427}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6794483373662175, 'return_min': 1.133704000650937, 'ic_mean': 0.01836486783982765, 'ic_std': 0.13513313138067926, 'ir': 0.13590203714063698}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.966765666636341, 'return_min': -5.009402725445478, 'ic_mean': 0.019101055308193247, 'ic_std': 0.11492020894784835, 'ir': 0.16621145647987334}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3063494193541487, 'return_min': 2.1901699594439883, 'ic_mean': 0.008716240688440135, 'ic_std': 0.10402097024720364, 'ir': 0.08379311082877013}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.1195582822984846, 'return_min': -2.152255431634442, 'ic_mean': 0.02186741619518386, 'ic_std': 0.0938347373271424, 'ir': 0.23304180112899986}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.2428807200270953, 'return_min': -1.9934012606037665, 'ic_mean': 0.005846025165429915, 'ic_std': 0.11431121599469815, 'ir': 0.051141308528299255}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.135073592033244, 'return_min': -1.569290204898044, 'ic_mean': 0.01589052651667338, 'ic_std': 0.08712529459274833, 'ir': 0.18238706211497838}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.4244820332829384, 'return_min': -2.9089093389722542, 'ic_mean': 0.02345060204450327, 'ic_std': 0.09330968656822755, 'ir': 0.2513201244905727}\n", "Dropped 5.8% entries from factor data: 5.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.1106371483071698, 'return_min': 0.7384582104275061, 'ic_mean': 0.01904753369002277, 'ic_std': 0.1437040079254837, 'ir': 0.13254698992041808}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6727542801243658, 'return_min': -2.5141533600236965, 'ic_mean': 0.02062338062306759, 'ic_std': 0.12974272645565418, 'ir': 0.15895596760190348}\n", "Dropped 19.4% entries from factor data: 19.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.186361722875343, 'return_min': -4.771658156618175, 'ic_mean': 0.03080472735096858, 'ic_std': 0.13448304883989257, 'ir': 0.2290602988012477}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.078316858624476, 'return_min': 2.8243179842268873, 'ic_mean': -0.003354040869039774, 'ic_std': 0.12554734141851814, 'ir': -0.026715347622208237}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.059914390388773, 'return_min': 0.9876241944994923, 'ic_mean': 0.009253334892118327, 'ic_std': 0.09518023812519258, 'ir': 0.09721907692589737}\n", "Dropped 75.4% entries from factor data: 4.1% in forward returns computation and 71.3% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 75.4%, consider increasing it.\n", "Dropped 5.0% entries from factor data: 4.6% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.1028839537762245, 'return_min': 1.4712035726383021, 'ic_mean': 0.01573511963451097, 'ic_std': 0.12875645916176112, 'ir': 0.12220839045241531}\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.557611397615016, 'return_min': -2.630890994269608, 'ic_mean': 0.04082193107035829, 'ic_std': 0.15069689357841806, 'ir': 0.2708876745963964}\n", "Dropped 6.2% entries from factor data: 4.1% in forward returns computation and 2.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.908654822286749, 'return_min': -3.045814210865805, 'ic_mean': 0.017825721289744093, 'ic_std': 0.09891136760995044, 'ir': 0.18021913679364426}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.8427891387639015, 'return_min': -5.1439461557833965, 'ic_mean': 0.020518599625434154, 'ic_std': 0.10270896663022346, 'ir': 0.19977418037225475}\n", "Dropped 5.1% entries from factor data: 5.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.2262007287811016, 'return_min': -0.8599276007148493, 'ic_mean': 0.009692181678166689, 'ic_std': 0.12432293105723538, 'ir': 0.0779597263010525}\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.0535378754416023, 'return_min': -3.7754249069099632, 'ic_mean': 0.013483594989006245, 'ic_std': 0.11166336749738125, 'ir': 0.1207521794407863}\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.8151910338114483, 'return_min': 0.2271370041806442, 'ic_mean': 0.023709489034368984, 'ic_std': 0.11543196162902948, 'ir': 0.20539795650848913}\n", "Dropped 6.8% entries from factor data: 6.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.968745797544802, 'return_min': 0.7420295450666359, 'ic_mean': 0.0007497965926771986, 'ic_std': 0.0790962380438273, 'ir': 0.009479548095090636}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.325210633537857, 'return_min': -4.695585787336531, 'ic_mean': 0.04460763632533326, 'ic_std': 0.15473063397586712, 'ir': 0.2882922093648927}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.4139321413604335, 'return_min': -0.9229932105070393, 'ic_mean': 0.03337888309777455, 'ic_std': 0.1357947250907262, 'ir': 0.2458039741637511}\n", "Dropped 36.7% entries from factor data: 6.3% in forward returns computation and 30.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 36.7%, consider increasing it.\n", "Dropped 14.4% entries from factor data: 14.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.690250131206945, 'return_min': -6.424887540932289, 'ic_mean': 0.03373703566560343, 'ic_std': 0.10080199588154105, 'ir': 0.3346861872184555}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.169881945279563, 'return_min': -6.31229096001551, 'ic_mean': 0.03296968337897795, 'ic_std': 0.12631752805189214, 'ir': 0.26100640099158506}\n", "Dropped 5.0% entries from factor data: 5.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7496919115301814, 'return_min': -3.635728802483529, 'ic_mean': 0.030951606730072127, 'ic_std': 0.13840801064591662, 'ir': 0.22362583340103281}\n", "Dropped 19.4% entries from factor data: 19.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.587100257058442, 'return_min': -4.284217458611073, 'ic_mean': 0.031000273677031443, 'ic_std': 0.1447401394207175, 'ir': 0.21417882973652982}\n", "Dropped 5.0% entries from factor data: 5.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.1540854993907779, 'return_min': -3.6039940322729613, 'ic_mean': 0.012919084301964964, 'ic_std': 0.12341693962093266, 'ir': 0.1046783718802712}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.981316246871481, 'return_min': 5.390763969097545, 'ic_mean': -0.008228041017915081, 'ic_std': 0.12200283549603595, 'ir': -0.06744139170587082}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.46680818988953376, 'return_min': -9.573699250176038, 'ic_mean': 0.030479589065425913, 'ic_std': 0.13840233043541156, 'ir': 0.22022453646219398}\n", "Dropped 8.3% entries from factor data: 6.7% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.15356097551100234, 'return_min': -1.8575416520738575, 'ic_mean': 0.0056106987061370454, 'ic_std': 0.10694521553071452, 'ir': 0.052463297944597254}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.64033472475478, 'return_min': -3.157454475913868, 'ic_mean': 0.021000666344349675, 'ic_std': 0.10472777340768456, 'ir': 0.20052623732004904}\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7684425737131129, 'return_min': 2.5239299512569424, 'ic_mean': 0.0016723960487462137, 'ic_std': 0.08816665584343973, 'ir': 0.018968577550632512}\n", "Dropped 71.4% entries from factor data: 4.5% in forward returns computation and 66.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 71.4%, consider increasing it.\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.5508940306061554, 'return_min': -5.089525801043049, 'ic_mean': 0.015112273085789957, 'ic_std': 0.1349740094700459, 'ir': 0.11196431924283726}\n", "Dropped 22.5% entries from factor data: 4.5% in forward returns computation and 18.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.013487586449042, 'return_min': 0.95966072966025, 'ic_mean': 0.029890194252107306, 'ic_std': 0.15429056963020354, 'ir': 0.19372664397925765}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.14329818537439643, 'return_min': -4.163975986468671, 'ic_mean': 0.017603536333573932, 'ic_std': 0.08792513779280701, 'ir': 0.20021050606774304}\n", "Dropped 35.5% entries from factor data: 4.5% in forward returns computation and 31.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 35.5%, consider increasing it.\n", "Dropped 16.5% entries from factor data: 16.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.609744631074442, 'return_min': -3.565641624810878, 'ic_mean': 0.0231533113786279, 'ic_std': 0.1413624957118279, 'ir': 0.16378680400370607}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.360755331760281, 'return_min': -5.198980746706772, 'ic_mean': 0.039095605288415165, 'ic_std': 0.12231203689416119, 'ir': 0.31963824886871356}\n", "Dropped 4.9% entries from factor data: 4.5% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.405641132418925, 'return_min': -11.342687734584578, 'ic_mean': 0.048231767617062125, 'ic_std': 0.12381252393669322, 'ir': 0.3895548372935487}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.616558219821208, 'return_min': -4.315985361372787, 'ic_mean': 0.026383135235255446, 'ic_std': 0.0824549029344659, 'ir': 0.319970484426189}\n", "Dropped 6.2% entries from factor data: 6.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.592116104897297, 'return_min': -9.953082305028671, 'ic_mean': 0.05575228858932666, 'ic_std': 0.13265268038948239, 'ir': 0.4202876898200022}\n", "Dropped 5.0% entries from factor data: 5.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.538537778884354, 'return_min': -5.41495318917673, 'ic_mean': 0.03285338828859206, 'ic_std': 0.1348373733348321, 'ir': 0.24365194512510688}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 30.5% entries from factor data: 30.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8548819935527696, 'return_min': -0.09506690636928994, 'ic_mean': 0.008542853088686839, 'ic_std': 0.08463260046544822, 'ir': 0.10094045369874355}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 24.5% entries from factor data: 5.5% in forward returns computation and 19.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.44185709872513, 'return_min': -1.0452090197898833, 'ic_mean': 0.008777000279204326, 'ic_std': 0.08598864287682546, 'ir': 0.10207162231618135}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.9768780070060785, 'return_min': -2.2311017520726395, 'ic_mean': 0.012081031933016862, 'ic_std': 0.09207030552787424, 'ir': 0.1312152910077977}\n", "Dropped 29.0% entries from factor data: 5.7% in forward returns computation and 23.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.993110017368572, 'return_min': -1.2356105390387562, 'ic_mean': -0.012643939291387275, 'ic_std': 0.1313539243628791, 'ir': -0.09625855757805192}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.6% entries from factor data: 5.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1630392664097045, 'return_min': 0.21289323063999888, 'ic_mean': 0.0010943022404003605, 'ic_std': 0.10706314813411442, 'ir': 0.010221091565788489}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.032798647562337635, 'return_min': -0.7698126378086201, 'ic_mean': 0.005814596397627227, 'ic_std': 0.09033416294117655, 'ir': 0.06436763466124719}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 5.0% entries from factor data: 5.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.5847841814265, 'return_min': -13.223762762020197, 'ic_mean': 0.07402149749962507, 'ic_std': 0.1473789063829409, 'ir': 0.5022529975035359}\n", "Dropped 16.8% entries from factor data: 6.4% in forward returns computation and 10.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.2405023500082777, 'return_min': 5.36763938213225, 'ic_mean': -0.03785989668875191, 'ic_std': 0.14596534572759093, 'ir': -0.259375925840701}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.9616532910404771, 'return_min': -2.9574458874370713, 'ic_mean': 0.01173671311946528, 'ic_std': 0.10265329561617889, 'ir': 0.11433352479348448}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.9% entries from factor data: 4.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.4740087317720594, 'return_min': 0.020697923051038458, 'ic_mean': 0.03278170560940921, 'ic_std': 0.1350085401491006, 'ir': 0.2428120885775506}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 14.9% entries from factor data: 5.1% in forward returns computation and 9.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.1419182535287185, 'return_min': 3.734358159788176, 'ic_mean': 0.015273193119828886, 'ic_std': 0.14238180500923342, 'ir': 0.1072692758659607}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 49.2% entries from factor data: 4.1% in forward returns computation and 45.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 49.2%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.06252158509911787, 'return_min': -3.9660982477396356, 'ic_mean': 0.0020802819065692036, 'ic_std': 0.08850246588042171, 'ir': 0.023505355312697544}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.3748381736594695, 'return_min': 3.733849329428107, 'ic_mean': -0.043605051543204564, 'ic_std': 0.14417835764655848, 'ir': -0.30243825949313974}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    2.526916   -2.732299  0.022638  0.079297  0.285478\n", "alpha003    2.308061   -5.127145  0.010625  0.087715  0.121132\n", "alpha005   11.082292  -11.457657  0.062319  0.133843  0.465616\n", "alpha006    3.834202   -4.807046  0.009245  0.118485  0.078030\n", "alpha008    1.625064   -1.121514  0.015361  0.142495  0.107797\n", "alpha009    1.619623    0.130255  0.028931  0.145510  0.198822\n", "alpha010    0.679448    1.133704  0.018365  0.135133  0.135902\n", "alpha011    4.966766   -5.009403  0.019101  0.114920  0.166211\n", "alpha012    3.306349    2.190170  0.008716  0.104021  0.083793\n", "alpha013    3.119558   -2.152255  0.021867  0.093835  0.233042\n", "alpha014    1.242881   -1.993401  0.005846  0.114311  0.051141\n", "alpha015    3.135074   -1.569290  0.015891  0.087125  0.182387\n", "alpha016    3.424482   -2.908909  0.023451  0.093310  0.251320\n", "alpha017    1.110637    0.738458  0.019048  0.143704  0.132547\n", "alpha018    0.672754   -2.514153  0.020623  0.129743  0.158956\n", "alpha019    5.186362   -4.771658  0.030805  0.134483  0.229060\n", "alpha020   -4.078317    2.824318 -0.003354  0.125547 -0.026715\n", "alpha022    2.059914    0.987624  0.009253  0.095180  0.097219\n", "alpha024    2.102884    1.471204  0.015735  0.128756  0.122208\n", "alpha025    7.557611   -2.630891  0.040822  0.150697  0.270888\n", "alpha026    2.908655   -3.045814  0.017826  0.098911  0.180219\n", "alpha028    2.842789   -5.143946  0.020519  0.102709  0.199774\n", "alpha029    1.226201   -0.859928  0.009692  0.124323  0.077960\n", "alpha030   -2.053538   -3.775425  0.013484  0.111663  0.120752\n", "alpha031    2.815191    0.227137  0.023709  0.115432  0.205398\n", "alpha032   -1.968746    0.742030  0.000750  0.079096  0.009480\n", "alpha033    4.325211   -4.695586  0.044608  0.154731  0.288292\n", "alpha034    5.413932   -0.922993  0.033379  0.135795  0.245804\n", "alpha036    8.690250   -6.424888  0.033737  0.100802  0.334686\n", "alpha037    4.169882   -6.312291  0.032970  0.126318  0.261006\n", "alpha038    0.749692   -3.635729  0.030952  0.138408  0.223626\n", "alpha039    5.587100   -4.284217  0.031000  0.144740  0.214179\n", "alpha040    1.154085   -3.603994  0.012919  0.123417  0.104678\n", "alpha041   -4.981316    5.390764 -0.008228  0.122003 -0.067441\n", "alpha042    0.466808   -9.573699  0.030480  0.138402  0.220225\n", "alpha043    0.153561   -1.857542  0.005611  0.106945  0.052463\n", "alpha044    4.640335   -3.157454  0.021001  0.104728  0.200526\n", "alpha045    0.768443    2.523930  0.001672  0.088167  0.018969\n", "alpha047    3.550894   -5.089526  0.015112  0.134974  0.111964\n", "alpha049    8.013488    0.959661  0.029890  0.154291  0.193727\n", "alpha050   -0.143298   -4.163976  0.017604  0.087925  0.200211\n", "alpha052    4.609745   -3.565642  0.023153  0.141362  0.163787\n", "alpha053    5.360755   -5.198981  0.039096  0.122312  0.319638\n", "alpha054    4.405641  -11.342688  0.048232  0.123813  0.389555\n", "alpha055    2.616558   -4.315985  0.026383  0.082455  0.319970\n", "alpha057    9.592116   -9.953082  0.055752  0.132653  0.420288\n", "alpha060    3.538538   -5.414953  0.032853  0.134837  0.243652\n", "alpha066    0.854882   -0.095067  0.008543  0.084633  0.100940\n", "alpha071    1.441857   -1.045209  0.008777  0.085989  0.102072\n", "alpha072    2.976878   -2.231102  0.012081  0.092070  0.131215\n", "alpha073   -2.993110   -1.235611 -0.012644  0.131354 -0.096259\n", "alpha077   -1.163039    0.212893  0.001094  0.107063  0.010221\n", "alpha078    0.032799   -0.769813  0.005815  0.090334  0.064368\n", "alpha083   11.584784  -13.223763  0.074021  0.147379  0.502253\n", "alpha084   -2.240502    5.367639 -0.037860  0.145965 -0.259376\n", "alpha085    1.961653   -2.957446  0.011737  0.102653  0.114334\n", "alpha088   -2.474009    0.020698  0.032782  0.135009  0.242812\n", "alpha094   -2.141918    3.734358  0.015273  0.142382  0.107269\n", "alpha098   -0.062522   -3.966098  0.002080  0.088502  0.023505\n", "alpha101   -6.374838    3.733849 -0.043605  0.144178 -0.302438\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 4.9% in forward returns computation and 95.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.387523498650566, 'return_min': 1.227309268008181, 'ic_mean': 0.009144619333415214, 'ic_std': 0.09799384289725478, 'ir': 0.09331830514089771}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5520345842268135, 'return_min': -0.3765341339256878, 'ic_mean': 0.00822644443890023, 'ic_std': 0.08643626665692673, 'ir': 0.09517352793072059}\n", "Dropped 95.1% entries from factor data: 4.5% in forward returns computation and 90.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 95.1%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.962478600054279, 'return_min': -7.910268232861339, 'ic_mean': 0.05629259926501336, 'ic_std': 0.15796849881367056, 'ir': 0.3563533216290956}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.3559692054431203, 'return_min': -4.415440632175027, 'ic_mean': 0.016222684036572968, 'ic_std': 0.12944929256620435, 'ir': 0.1253207624002749}\n", "Dropped 91.5% entries from factor data: 5.5% in forward returns computation and 86.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 91.5%, consider increasing it.\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.943986128289502, 'return_min': 2.0573554298186636, 'ic_mean': 0.01429764100969351, 'ic_std': 0.14929650213225248, 'ir': 0.09576675143419046}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.336840558838869, 'return_min': 8.377963183985315, 'ic_mean': 0.01626690658785927, 'ic_std': 0.1600172731729796, 'ir': 0.10165719153503291}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.2209449206189724, 'return_min': 9.392622400403727, 'ic_mean': 0.009924462116902061, 'ic_std': 0.14803528359798107, 'ir': 0.06704119366470693}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.22492680145281, 'return_min': -3.760489350471685, 'ic_mean': 0.006889774966141608, 'ic_std': 0.12505052055621363, 'ir': 0.05509593191213039}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.3360668795734405, 'return_min': 11.245152561376592, 'ic_mean': -0.0010746656613880115, 'ic_std': 0.1141796397462927, 'ir': -0.009412060361864164}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.515242855847255, 'return_min': 0.17055889004025104, 'ic_mean': 0.014865002138251374, 'ic_std': 0.09832367995609058, 'ir': 0.1511843550291221}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.6371293916914738, 'return_min': -1.034533274477667, 'ic_mean': 0.01673090350876417, 'ic_std': 0.12198240131145197, 'ir': 0.13715833865285154}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6308609222281945, 'return_min': 0.459555155514213, 'ic_mean': 0.01729151330913632, 'ic_std': 0.08750403114923287, 'ir': 0.19760819109746705}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.46789375553801804, 'return_min': 0.11467010148979995, 'ic_mean': 0.018267743277242003, 'ic_std': 0.09867029115953625, 'ir': 0.1851392456895215}\n", "Dropped 4.9% entries from factor data: 4.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.22829054025064188, 'return_min': 5.836937877001791, 'ic_mean': 0.023538023384459098, 'ic_std': 0.14959970670253106, 'ir': 0.1573400369778991}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.553670669920404, 'return_min': 6.389479375592533, 'ic_mean': 0.011438924690168552, 'ic_std': 0.1460352134358622, 'ir': 0.07832990702062732}\n", "Dropped 15.9% entries from factor data: 15.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.288634930538215, 'return_min': 5.160964441166538, 'ic_mean': 0.022625351361429933, 'ic_std': 0.1563469920389323, 'ir': 0.14471241861689252}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.0540255040678588, 'return_min': 0.1542173911683875, 'ic_mean': 0.003432927496072023, 'ic_std': 0.15694322294185847, 'ir': 0.021873690572442192}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.12056139118555, 'return_min': 6.3558689400844415, 'ic_mean': -0.0034419591444908495, 'ic_std': 0.09970485813670817, 'ir': -0.0345214787806175}\n", "Dropped 87.7% entries from factor data: 4.1% in forward returns computation and 83.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 87.7%, consider increasing it.\n", "Dropped 6.0% entries from factor data: 4.3% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.6938355216320495, 'return_min': 3.4350324932463927, 'ic_mean': 0.013518373634506914, 'ic_std': 0.12726567713545464, 'ir': 0.10622167687929476}\n", "Dropped 5.2% entries from factor data: 4.8% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.614713880666255, 'return_min': 1.7778828898507015, 'ic_mean': 0.03656408120116096, 'ic_std': 0.1710369946891819, 'ir': 0.21377878667482011}\n", "Dropped 5.8% entries from factor data: 4.1% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.667418704167314, 'return_min': -3.250092979906194, 'ic_mean': 0.027716705163088568, 'ic_std': 0.11280179914240131, 'ir': 0.24571155224305352}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.9639895745428007, 'return_min': -0.9964418534513797, 'ic_mean': 0.004976781833038002, 'ic_std': 0.12463814800689761, 'ir': 0.03992984421400887}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.01751035128639, 'return_min': -2.201277009109548, 'ic_mean': 0.01513703101260384, 'ic_std': 0.13494607197356293, 'ir': 0.11217096423206237}\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.0947966948149368, 'return_min': -6.423583477893624, 'ic_mean': 0.026577362826022966, 'ic_std': 0.1422099431243009, 'ir': 0.1868882178146474}\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.34905008951424676, 'return_min': 1.4065833938681216, 'ic_mean': 0.009338967832920248, 'ic_std': 0.12727259876414065, 'ir': 0.07337767849171573}\n", "Dropped 5.3% entries from factor data: 5.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.166516379575988, 'return_min': 4.162174192285928, 'ic_mean': -0.0052931304624541445, 'ic_std': 0.08128075401730527, 'ir': -0.06512157184623556}\n", "Dropped 4.7% entries from factor data: 4.3% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5103104351021912, 'return_min': 1.8882268474351704, 'ic_mean': 0.03223007327488121, 'ic_std': 0.17503521366071373, 'ir': 0.1841347955123797}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.758878111235429, 'return_min': 6.393436684279941, 'ic_mean': 0.018482408175282933, 'ic_std': 0.1455482704321392, 'ir': 0.1269847324218134}\n", "Dropped 35.4% entries from factor data: 5.2% in forward returns computation and 30.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 35.4%, consider increasing it.\n", "Dropped 11.4% entries from factor data: 11.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.745185726043097, 'return_min': 3.4063960362651358, 'ic_mean': 0.01225994774300666, 'ic_std': 0.13482173614800116, 'ir': 0.09093450428162597}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.11654193692089976, 'return_min': 0.9663187999509049, 'ic_mean': 0.027605194300076248, 'ic_std': 0.1362266649728061, 'ir': 0.20264163631684637}\n", "Dropped 4.9% entries from factor data: 4.5% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.1913134941955441, 'return_min': 3.2993876875409, 'ic_mean': 0.027677098498096138, 'ic_std': 0.16341139499516164, 'ir': 0.16937067637734574}\n", "Dropped 15.9% entries from factor data: 15.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.0637688185297662, 'return_min': 6.551899347224666, 'ic_mean': 0.02163268223187699, 'ic_std': 0.16228919471323167, 'ir': 0.1332971198119652}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.1337262002731876, 'return_min': 1.9623157859527396, 'ic_mean': 0.009829354488603054, 'ic_std': 0.14661475506242164, 'ir': 0.06704205510842466}\n", "Dropped 4.7% entries from factor data: 4.3% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.8112914303057845, 'return_min': 10.286558081908925, 'ic_mean': -0.0020430595528140396, 'ic_std': 0.14404936074023933, 'ir': -0.014183051853303525}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.751437182105688, 'return_min': -0.2652411659631593, 'ic_mean': 0.013311903084287249, 'ic_std': 0.17407658618068528, 'ir': 0.07647153115968146}\n", "Dropped 7.1% entries from factor data: 5.4% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.22134813034336, 'return_min': -1.149428498702365, 'ic_mean': -0.0009797919747332631, 'ic_std': 0.12568011910505372, 'ir': -0.007795918572564948}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5437854546895835, 'return_min': -0.3794925082578615, 'ic_mean': 0.014526912677936685, 'ic_std': 0.11658531628586774, 'ir': 0.12460327887533133}\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.9262128674913939, 'return_min': 6.088506400521165, 'ic_mean': -0.0036335795951562546, 'ic_std': 0.09985247523199682, 'ir': -0.036389479446693844}\n", "Dropped 77.0% entries from factor data: 4.3% in forward returns computation and 72.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 77.0%, consider increasing it.\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.1313729543083184, 'return_min': 0.29406822281696776, 'ic_mean': -0.012260254534154089, 'ic_std': 0.13771613848233652, 'ir': -0.08902554681873098}\n", "Dropped 22.8% entries from factor data: 4.3% in forward returns computation and 18.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -7.461771087881042, 'return_min': 5.3311727235683115, 'ic_mean': 0.018048046538073452, 'ic_std': 0.16742897293355366, 'ir': 0.10779524130053675}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.6257862157830214, 'return_min': -0.5431503781594405, 'ic_mean': 0.014923787506530305, 'ic_std': 0.08452375111122974, 'ir': 0.176563241814614}\n", "Dropped 38.4% entries from factor data: 4.3% in forward returns computation and 34.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 38.4%, consider increasing it.\n", "Dropped 13.3% entries from factor data: 13.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.946968789964924, 'return_min': 2.7630081801532924, 'ic_mean': 0.019538136455259182, 'ic_std': 0.16048685682269287, 'ir': 0.12174290681538531}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.2892041897449857, 'return_min': -2.1777636892073016, 'ic_mean': 0.02469129254696148, 'ic_std': 0.12630578044743943, 'ir': 0.19548822278356812}\n", "Dropped 7.2% entries from factor data: 4.3% in forward returns computation and 2.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.9906088352396036, 'return_min': -5.7755050037855415, 'ic_mean': 0.029921050705466676, 'ic_std': 0.13355657402592716, 'ir': 0.22403278104197358}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.4826087880581706, 'return_min': 1.2383565543028219, 'ic_mean': 0.01723451514288564, 'ic_std': 0.09631665063560291, 'ir': 0.1789359890439857}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.942788794564002, 'return_min': -3.53630444639208, 'ic_mean': 0.04247547054848353, 'ic_std': 0.14193911270024154, 'ir': 0.29925134616127025}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.2307289187372135, 'return_min': -7.514502933884115, 'ic_mean': 0.02461559806077155, 'ic_std': 0.14921045271179834, 'ir': 0.16497234351481296}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 28.9% entries from factor data: 28.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.011971050929426852, 'return_min': 0.8513885761263218, 'ic_mean': 0.005125607764249015, 'ic_std': 0.09356320256793721, 'ir': 0.05478230355066414}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 29.3% entries from factor data: 4.8% in forward returns computation and 24.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7067341484477971, 'return_min': -2.4071370833500527, 'ic_mean': 0.010167096780878161, 'ic_std': 0.07037430882297141, 'ir': 0.14447171064164602}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.6850446803640118, 'return_min': -3.0824939900686577, 'ic_mean': 0.010511169280428341, 'ic_std': 0.10508032259517168, 'ir': 0.10002985355234642}\n", "Dropped 31.0% entries from factor data: 4.8% in forward returns computation and 26.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.20632191908287467, 'return_min': 1.870781484598627, 'ic_mean': -0.020222907918823408, 'ic_std': 0.15133056070192946, 'ir': -0.13363399848002788}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0846718405499445, 'return_min': 5.030473906850741, 'ic_mean': 0.0004571806312160598, 'ic_std': 0.11578567424846462, 'ir': 0.003948507742287663}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.586651672605415, 'return_min': 3.816549606145969, 'ic_mean': -0.014406834413345698, 'ic_std': 0.10299997460511862, 'ir': -0.13987221325615498}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.061089554415982, 'return_min': -10.234035815676057, 'ic_mean': 0.06457051162338942, 'ic_std': 0.1641617528014345, 'ir': 0.3933346868042529}\n", "Dropped 16.7% entries from factor data: 5.3% in forward returns computation and 11.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.919257873199246, 'return_min': 1.110812928257765, 'ic_mean': -0.01760447500420693, 'ic_std': 0.15295095101807507, 'ir': -0.11509882669592889}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.31160956702481, 'return_min': -2.8096716437420177, 'ic_mean': 0.005360362413331745, 'ic_std': 0.11590037973370575, 'ir': 0.04624973986839202}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.6825650653840007, 'return_min': 3.23297124499744, 'ic_mean': 0.028029941521311576, 'ic_std': 0.13728794699070096, 'ir': 0.20416899032811783}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 13.2% entries from factor data: 4.6% in forward returns computation and 8.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.445460105174659, 'return_min': 3.8663420938789272, 'ic_mean': 0.005931689026562152, 'ic_std': 0.15490747481046566, 'ir': 0.03829181925416941}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 55.6% entries from factor data: 4.1% in forward returns computation and 51.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 55.6%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5941124735241452, 'return_min': 2.1389045599673473, 'ic_mean': -0.0026558831979614905, 'ic_std': 0.08961538099809184, 'ir': -0.02963646606622184}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.7% entries from factor data: 4.3% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.35552267366468, 'return_min': 0.49569259629178575, 'ic_mean': -0.025742127271284245, 'ic_std': 0.166306571050943, 'ir': -0.15478719276461375}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    0.387523    1.227309  0.009145  0.097994  0.093318\n", "alpha003    1.552035   -0.376534  0.008226  0.086436  0.095174\n", "alpha005    7.962479   -7.910268  0.056293  0.157968  0.356353\n", "alpha006    2.355969   -4.415441  0.016223  0.129449  0.125321\n", "alpha008    5.943986    2.057355  0.014298  0.149297  0.095767\n", "alpha009    0.336841    8.377963  0.016267  0.160017  0.101657\n", "alpha010    2.220945    9.392622  0.009924  0.148035  0.067041\n", "alpha011    3.224927   -3.760489  0.006890  0.125051  0.055096\n", "alpha012    3.336067   11.245153 -0.001075  0.114180 -0.009412\n", "alpha013   -2.515243    0.170559  0.014865  0.098324  0.151184\n", "alpha014    3.637129   -1.034533  0.016731  0.121982  0.137158\n", "alpha015    1.630861    0.459555  0.017292  0.087504  0.197608\n", "alpha016    0.467894    0.114670  0.018268  0.098670  0.185139\n", "alpha017   -0.228291    5.836938  0.023538  0.149600  0.157340\n", "alpha018   -1.553671    6.389479  0.011439  0.146035  0.078330\n", "alpha019    4.288635    5.160964  0.022625  0.156347  0.144712\n", "alpha020   -3.054026    0.154217  0.003433  0.156943  0.021874\n", "alpha022    3.120561    6.355869 -0.003442  0.099705 -0.034521\n", "alpha024    3.693836    3.435032  0.013518  0.127266  0.106222\n", "alpha025    4.614714    1.777883  0.036564  0.171037  0.213779\n", "alpha026    4.667419   -3.250093  0.027717  0.112802  0.245712\n", "alpha028   -0.963990   -0.996442  0.004977  0.124638  0.039930\n", "alpha029    3.017510   -2.201277  0.015137  0.134946  0.112171\n", "alpha030   -1.094797   -6.423583  0.026577  0.142210  0.186888\n", "alpha031    0.349050    1.406583  0.009339  0.127273  0.073378\n", "alpha032   -1.166516    4.162174 -0.005293  0.081281 -0.065122\n", "alpha033    1.510310    1.888227  0.032230  0.175035  0.184135\n", "alpha034    1.758878    6.393437  0.018482  0.145548  0.126985\n", "alpha036    1.745186    3.406396  0.012260  0.134822  0.090935\n", "alpha037   -0.116542    0.966319  0.027605  0.136227  0.202642\n", "alpha038    0.191313    3.299388  0.027677  0.163411  0.169371\n", "alpha039    3.063769    6.551899  0.021633  0.162289  0.133297\n", "alpha040    0.133726    1.962316  0.009829  0.146615  0.067042\n", "alpha041   -2.811291   10.286558 -0.002043  0.144049 -0.014183\n", "alpha042   -3.751437   -0.265241  0.013312  0.174077  0.076472\n", "alpha043   -3.221348   -1.149428 -0.000980  0.125680 -0.007796\n", "alpha044    1.543785   -0.379493  0.014527  0.116585  0.124603\n", "alpha045    0.926213    6.088506 -0.003634  0.099852 -0.036389\n", "alpha047   -1.131373    0.294068 -0.012260  0.137716 -0.089026\n", "alpha049   -7.461771    5.331173  0.018048  0.167429  0.107795\n", "alpha050   -2.625786   -0.543150  0.014924  0.084524  0.176563\n", "alpha052    5.946969    2.763008  0.019538  0.160487  0.121743\n", "alpha053    1.289204   -2.177764  0.024691  0.126306  0.195488\n", "alpha054   -0.990609   -5.775505  0.029921  0.133557  0.224033\n", "alpha055   -1.482609    1.238357  0.017235  0.096317  0.178936\n", "alpha057    5.942789   -3.536304  0.042475  0.141939  0.299251\n", "alpha060    3.230729   -7.514503  0.024616  0.149210  0.164972\n", "alpha066   -0.011971    0.851389  0.005126  0.093563  0.054782\n", "alpha071   -1.706734   -2.407137  0.010167  0.070374  0.144472\n", "alpha072    1.685045   -3.082494  0.010511  0.105080  0.100030\n", "alpha073    0.206322    1.870781 -0.020223  0.151331 -0.133634\n", "alpha077    1.084672    5.030474  0.000457  0.115786  0.003949\n", "alpha078   -6.586652    3.816550 -0.014407  0.103000 -0.139872\n", "alpha083   12.061090  -10.234036  0.064571  0.164162  0.393335\n", "alpha084    5.919258    1.110813 -0.017604  0.152951 -0.115099\n", "alpha085    4.311610   -2.809672  0.005360  0.115900  0.046250\n", "alpha088   -1.682565    3.232971  0.028030  0.137288  0.204169\n", "alpha094   -4.445460    3.866342  0.005932  0.154907  0.038292\n", "alpha098    1.594112    2.138905 -0.002656  0.089615 -0.029636\n", "alpha101    4.355523    0.495693 -0.025742  0.166307 -0.154787\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 99.2% entries from factor data: 4.8% in forward returns computation and 94.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 99.2%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.17202363136403775, 'return_min': 0.0818248587064474, 'ic_mean': 0.016307972383699167, 'ic_std': 0.0971842510175134, 'ir': 0.16780468247638536}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.7458000147829722, 'return_min': -0.12530054577575278, 'ic_mean': 0.012740318280616579, 'ic_std': 0.07849821352072088, 'ir': 0.16230074175195802}\n", "Dropped 95.1% entries from factor data: 4.5% in forward returns computation and 90.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 95.1%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.63331344590301, 'return_min': -13.824688743385716, 'ic_mean': 0.04121022734549092, 'ic_std': 0.16986599655728707, 'ir': 0.24260433624567604}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.4264142648909868, 'return_min': -2.165208031211785, 'ic_mean': 0.00950345682632219, 'ic_std': 0.11858356722444006, 'ir': 0.08014143147115184}\n", "Dropped 93.1% entries from factor data: 5.1% in forward returns computation and 88.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 93.1%, consider increasing it.\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.192145681036873, 'return_min': -4.903863626946059, 'ic_mean': 0.01898699734890663, 'ic_std': 0.1588921611565093, 'ir': 0.11949612372761659}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.425391555795578, 'return_min': -5.713111339519239, 'ic_mean': 0.02846863835889668, 'ic_std': 0.1514229139439889, 'ir': 0.1880074660921344}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.925116441847564, 'return_min': -4.2975963709224185, 'ic_mean': 0.020462307857059398, 'ic_std': 0.14147561464973296, 'ir': 0.14463487511766765}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.801764633945883, 'return_min': -6.068450694232119, 'ic_mean': 0.01795548580511415, 'ic_std': 0.10850549417828026, 'ir': 0.16547996892777003}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.4677807083919294, 'return_min': -0.3641294763689906, 'ic_mean': 0.01426861524392835, 'ic_std': 0.095536431065735, 'ir': 0.1493526091016594}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.451442076662035, 'return_min': -1.2577686859138737, 'ic_mean': 0.021268126149637474, 'ic_std': 0.09386016666029545, 'ir': 0.22659373945725453}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.8519373491110436, 'return_min': -0.929666205031765, 'ic_mean': 0.008779536661958956, 'ic_std': 0.1157497737654872, 'ir': 0.07584927707717669}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6987001348846, 'return_min': -2.6480821715102465, 'ic_mean': 0.020699905854909696, 'ic_std': 0.08000504602214827, 'ir': 0.25873250356207805}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6744266801139709, 'return_min': -1.4931233495130147, 'ic_mean': 0.02329790500567538, 'ic_std': 0.0886202159564156, 'ir': 0.2628960531661709}\n", "Dropped 4.8% entries from factor data: 4.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.830110730084213, 'return_min': -3.1024146157321297, 'ic_mean': 0.020567063735742984, 'ic_std': 0.14701709100058125, 'ir': 0.1398957331815365}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.369410415154817, 'return_min': -7.310557014799102, 'ic_mean': 0.037851954762259075, 'ic_std': 0.16806307358455028, 'ir': 0.22522469662686648}\n", "Dropped 13.3% entries from factor data: 13.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.944217958962049, 'return_min': -3.788022935609092, 'ic_mean': 0.023022930755818564, 'ic_std': 0.16276607745042895, 'ir': 0.14144796702390453}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.0732452009654114, 'return_min': -2.6166785829928596, 'ic_mean': 0.014979683030004501, 'ic_std': 0.14203621689769308, 'ir': 0.10546382716454762}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.160968342417302, 'return_min': -0.7686502340042978, 'ic_mean': 0.00018981249619752383, 'ic_std': 0.09457823424896879, 'ir': 0.002006936349624157}\n", "Dropped 94.7% entries from factor data: 4.1% in forward returns computation and 90.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 94.7%, consider increasing it.\n", "Dropped 4.8% entries from factor data: 4.4% in forward returns computation and 0.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.8614172988428166, 'return_min': -6.209147040958651, 'ic_mean': 0.0366813522489812, 'ic_std': 0.15630811205930623, 'ir': 0.2346733753336078}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.714575111250731, 'return_min': -9.305190598144497, 'ic_mean': 0.038281505439449706, 'ic_std': 0.16308412728794916, 'ir': 0.2347347107046049}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.9238847125291336, 'return_min': -0.39741537893100976, 'ic_mean': 0.010869935582387247, 'ic_std': 0.12396174301158296, 'ir': 0.08768782463289147}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.21754564301978974, 'return_min': -6.688361017137545, 'ic_mean': 0.013794224823299004, 'ic_std': 0.1195373402158681, 'ir': 0.1153967856268888}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.616039740911182, 'return_min': 4.603803907083925, 'ic_mean': -0.013614670405361561, 'ic_std': 0.14431231265787794, 'ir': -0.09434171038224536}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.2264600121092428, 'return_min': -5.631268177616056, 'ic_mean': 0.017190654368437447, 'ic_std': 0.13089166285998952, 'ir': 0.13133498339635064}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7645315249060758, 'return_min': -5.84557079543635, 'ic_mean': 0.014959903756103723, 'ic_std': 0.11852261092195943, 'ir': 0.1262198296150765}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.6952483212781, 'return_min': -2.6125684393984994, 'ic_mean': 0.014246692704114314, 'ic_std': 0.10127706005503645, 'ir': 0.1406704805251289}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.835155826245941, 'return_min': -6.035431188436968, 'ic_mean': 0.039820479783002696, 'ic_std': 0.1717469051172455, 'ir': 0.23185558863968275}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.3866197586383, 'return_min': -0.9074171371792339, 'ic_mean': 0.027074202347971817, 'ic_std': 0.1369639835115361, 'ir': 0.19767388224139545}\n", "Dropped 44.1% entries from factor data: 5.0% in forward returns computation and 39.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 44.1%, consider increasing it.\n", "Dropped 9.0% entries from factor data: 9.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.897593543367492, 'return_min': 1.1216385383883143, 'ic_mean': 0.004019831358559741, 'ic_std': 0.11798831916897845, 'ir': 0.03406974001216755}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.690359877517203, 'return_min': -5.4106434846323825, 'ic_mean': 0.026887196468520405, 'ic_std': 0.1352360772402631, 'ir': 0.1988167434105034}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6839657547230704, 'return_min': -4.831501915520953, 'ic_mean': 0.03138658380222299, 'ic_std': 0.16792295843790156, 'ir': 0.1869106171913345}\n", "Dropped 13.3% entries from factor data: 13.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.124097036579677, 'return_min': -5.14264302357792, 'ic_mean': 0.02213425685902193, 'ic_std': 0.17246927329522055, 'ir': 0.1283373927200012}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.1223100844004463, 'return_min': -4.32269438461419, 'ic_mean': 0.01761760699955628, 'ic_std': 0.13043943244735737, 'ir': 0.13506350548302468}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.7590562367698617, 'return_min': 0.019429276156834874, 'ic_mean': 0.014998313254546192, 'ic_std': 0.1473441235702216, 'ir': 0.10179105139132516}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.921486625031225, 'return_min': -14.456724105207375, 'ic_mean': 0.041796834827145427, 'ic_std': 0.21459909471538646, 'ir': 0.19476706032976873}\n", "Dropped 5.1% entries from factor data: 5.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.152161888750003, 'return_min': 0.33693090650599444, 'ic_mean': 0.008237100405562092, 'ic_std': 0.1157157785734056, 'ir': 0.07118389995826536}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.315269379495998, 'return_min': 0.2538349533764972, 'ic_mean': 0.012710286615286235, 'ic_std': 0.10850172228019761, 'ir': 0.11714363927295889}\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.832630946315584, 'return_min': -3.006468677559937, 'ic_mean': 0.01229825162277458, 'ic_std': 0.08265174014174231, 'ir': 0.14879603988595866}\n", "Dropped 73.3% entries from factor data: 4.3% in forward returns computation and 69.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 73.3%, consider increasing it.\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6240103069354852, 'return_min': -1.2599148303693575, 'ic_mean': 0.004469896034602398, 'ic_std': 0.12793752558147103, 'ir': 0.03493811541443291}\n", "Dropped 24.5% entries from factor data: 4.3% in forward returns computation and 20.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8294268848918485, 'return_min': -4.913544393354563, 'ic_mean': 0.024680613154946754, 'ic_std': 0.16525859147405889, 'ir': 0.14934541638533172}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.8422576242649313, 'return_min': -0.9775204682471728, 'ic_mean': 0.017382039542406965, 'ic_std': 0.08746346991809553, 'ir': 0.19873484963132879}\n", "Dropped 42.1% entries from factor data: 4.3% in forward returns computation and 37.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 42.1%, consider increasing it.\n", "Dropped 10.4% entries from factor data: 10.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.1630592280837995, 'return_min': -4.836245242958448, 'ic_mean': 0.02065492532042867, 'ic_std': 0.170601554013554, 'ir': 0.1210711440458958}\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.032019817066246, 'return_min': 0.6178529500733276, 'ic_mean': 0.026237136298848433, 'ic_std': 0.12809220779113267, 'ir': 0.20483007320500513}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.837168240720892, 'return_min': -11.562315722325467, 'ic_mean': 0.04588084336419724, 'ic_std': 0.13987607649272424, 'ir': 0.32801065424925446}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.3692523951303048, 'return_min': -3.0420513765838653, 'ic_mean': 0.019917570936079473, 'ic_std': 0.10426344375575167, 'ir': 0.19103120152772363}\n", "Dropped 4.9% entries from factor data: 4.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 10.948457697483871, 'return_min': -12.451336657344214, 'ic_mean': 0.042782739889080615, 'ic_std': 0.14986164390268936, 'ir': 0.2854815867151505}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.991155607583231, 'return_min': -6.390715213486775, 'ic_mean': 0.02791406649771421, 'ic_std': 0.14483392470030199, 'ir': 0.19273154791237945}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 24.8% entries from factor data: 24.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.012707626966037, 'return_min': -5.1923226418026935, 'ic_mean': 0.011228238156540803, 'ic_std': 0.09082456096600149, 'ir': 0.12362557040869031}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 24.3% entries from factor data: 4.7% in forward returns computation and 19.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6700419937877289, 'return_min': -0.5589865418886841, 'ic_mean': 0.001024572694461956, 'ic_std': 0.08449089944952348, 'ir': 0.012126426646387587}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.5610811580411017, 'return_min': 1.893399157184561, 'ic_mean': -0.000585162981743943, 'ic_std': 0.09032661549311245, 'ir': -0.006478300759409751}\n", "Dropped 21.5% entries from factor data: 4.7% in forward returns computation and 16.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.266209845315446, 'return_min': -4.802380600746892, 'ic_mean': -0.009829758218365047, 'ic_std': 0.158121589675049, 'ir': -0.06216581959848679}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.7% entries from factor data: 4.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.4278173477978431, 'return_min': -1.8427328752934624, 'ic_mean': 0.013143055956896688, 'ic_std': 0.11547815769773487, 'ir': 0.11381421576969349}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.47187500518242054, 'return_min': -0.5186312767757606, 'ic_mean': 0.009956928506818875, 'ic_std': 0.09805631462971648, 'ir': 0.10154296074065766}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.534332187233208, 'return_min': -15.22027048665442, 'ic_mean': 0.05118700574702874, 'ic_std': 0.15771890193628124, 'ir': 0.32454579076202544}\n", "Dropped 13.1% entries from factor data: 5.0% in forward returns computation and 8.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.1286307100009125, 'return_min': -4.618370953647366, 'ic_mean': -0.020319465030188163, 'ic_std': 0.16437862406170153, 'ir': -0.12361379191591847}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.357647572735823, 'return_min': 2.3165213982068167, 'ic_mean': -0.0064817409516119496, 'ic_std': 0.08449467597625086, 'ir': -0.0767118268307673}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.0329620146154461, 'return_min': -2.1888001796055434, 'ic_mean': 0.03157558419180377, 'ic_std': 0.1303617932356142, 'ir': 0.24221501874199042}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 9.1% entries from factor data: 4.6% in forward returns computation and 4.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.3316915343896127, 'return_min': -4.375691011090366, 'ic_mean': 0.02112943606723804, 'ic_std': 0.17596866870569283, 'ir': 0.12007498961407141}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 69.1% entries from factor data: 4.1% in forward returns computation and 65.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 69.1%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.52257125058264, 'return_min': 2.7101394116124666, 'ic_mean': -0.00040742591299500376, 'ic_std': 0.08265362597535977, 'ir': -0.004929316871306568}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.508027096231659, 'return_min': 2.814865080429385, 'ic_mean': -0.03644119014882869, 'ic_std': 0.16766213325659368, 'ir': -0.21734895912995644}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    0.172024    0.081825  0.016308  0.097184  0.167805\n", "alpha003    1.745800   -0.125301  0.012740  0.078498  0.162301\n", "alpha005    5.633313  -13.824689  0.041210  0.169866  0.242604\n", "alpha006    3.426414   -2.165208  0.009503  0.118584  0.080141\n", "alpha008    3.192146   -4.903864  0.018987  0.158892  0.119496\n", "alpha009    5.425392   -5.713111  0.028469  0.151423  0.188007\n", "alpha010    2.925116   -4.297596  0.020462  0.141476  0.144635\n", "alpha011    5.801765   -6.068451  0.017955  0.108505  0.165480\n", "alpha012   -0.467781   -0.364129  0.014269  0.095536  0.149353\n", "alpha013   -1.451442   -1.257769  0.021268  0.093860  0.226594\n", "alpha014    1.851937   -0.929666  0.008780  0.115750  0.075849\n", "alpha015    0.698700   -2.648082  0.020700  0.080005  0.258733\n", "alpha016    0.674427   -1.493123  0.023298  0.088620  0.262896\n", "alpha017   -3.830111   -3.102415  0.020567  0.147017  0.139896\n", "alpha018    4.369410   -7.310557  0.037852  0.168063  0.225225\n", "alpha019    4.944218   -3.788023  0.023023  0.162766  0.141448\n", "alpha020   -1.073245   -2.616679  0.014980  0.142036  0.105464\n", "alpha022   -2.160968   -0.768650  0.000190  0.094578  0.002007\n", "alpha024    2.861417   -6.209147  0.036681  0.156308  0.234673\n", "alpha025    6.714575   -9.305191  0.038282  0.163084  0.234735\n", "alpha026    1.923885   -0.397415  0.010870  0.123962  0.087688\n", "alpha028    0.217546   -6.688361  0.013794  0.119537  0.115397\n", "alpha029   -4.616040    4.603804 -0.013615  0.144312 -0.094342\n", "alpha030    1.226460   -5.631268  0.017191  0.130892  0.131335\n", "alpha031    0.764532   -5.845571  0.014960  0.118523  0.126220\n", "alpha032    4.695248   -2.612568  0.014247  0.101277  0.140670\n", "alpha033    5.835156   -6.035431  0.039820  0.171747  0.231856\n", "alpha034    5.386620   -0.907417  0.027074  0.136964  0.197674\n", "alpha036    0.897594    1.121639  0.004020  0.117988  0.034070\n", "alpha037    4.690360   -5.410643  0.026887  0.135236  0.198817\n", "alpha038   -0.683966   -4.831502  0.031387  0.167923  0.186911\n", "alpha039    4.124097   -5.142643  0.022134  0.172469  0.128337\n", "alpha040    1.122310   -4.322694  0.017618  0.130439  0.135064\n", "alpha041    0.759056    0.019429  0.014998  0.147344  0.101791\n", "alpha042    5.921487  -14.456724  0.041797  0.214599  0.194767\n", "alpha043   -1.152162    0.336931  0.008237  0.115716  0.071184\n", "alpha044    1.315269    0.253835  0.012710  0.108502  0.117144\n", "alpha045   -2.832631   -3.006469  0.012298  0.082652  0.148796\n", "alpha047    0.624010   -1.259915  0.004470  0.127938  0.034938\n", "alpha049    0.829427   -4.913544  0.024681  0.165259  0.149345\n", "alpha050    1.842258   -0.977520  0.017382  0.087463  0.198735\n", "alpha052    5.163059   -4.836245  0.020655  0.170602  0.121071\n", "alpha053    4.032020    0.617853  0.026237  0.128092  0.204830\n", "alpha054    6.837168  -11.562316  0.045881  0.139876  0.328011\n", "alpha055   -1.369252   -3.042051  0.019918  0.104263  0.191031\n", "alpha057   10.948458  -12.451337  0.042783  0.149862  0.285482\n", "alpha060    4.991156   -6.390715  0.027914  0.144834  0.192732\n", "alpha066    2.012708   -5.192323  0.011228  0.090825  0.123626\n", "alpha071   -0.670042   -0.558987  0.001025  0.084491  0.012126\n", "alpha072   -3.561081    1.893399 -0.000585  0.090327 -0.006478\n", "alpha073   -0.266210   -4.802381 -0.009830  0.158122 -0.062166\n", "alpha077   -0.427817   -1.842733  0.013143  0.115478  0.113814\n", "alpha078   -0.471875   -0.518631  0.009957  0.098056  0.101543\n", "alpha083    9.534332  -15.220270  0.051187  0.157719  0.324546\n", "alpha084   -3.128631   -4.618371 -0.020319  0.164379 -0.123614\n", "alpha085   -4.357648    2.316521 -0.006482  0.084495 -0.076712\n", "alpha088    1.032962   -2.188800  0.031576  0.130362  0.242215\n", "alpha094   -2.331692   -4.375691  0.021129  0.175969  0.120075\n", "alpha098    3.522571    2.710139 -0.000407  0.082654 -0.004929\n", "alpha101   -4.508027    2.814865 -0.036441  0.167662 -0.217349\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 4.5% in forward returns computation and 95.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.0412156754525661, 'return_min': 0.511832897631681, 'ic_mean': 0.005520313589583972, 'ic_std': 0.09754360452865153, 'ir': 0.05659329093136482}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.801292361296337, 'return_min': -4.449060265354632, 'ic_mean': 0.013513636807356164, 'ic_std': 0.0819282469519926, 'ir': 0.16494478168530485}\n", "Dropped 96.7% entries from factor data: 4.3% in forward returns computation and 92.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 96.7%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5604740757181315, 'return_min': -10.201234191318242, 'ic_mean': 0.013015302492938006, 'ic_std': 0.19240018809303658, 'ir': 0.06764703622142176}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.6463724144737455, 'return_min': -7.430420435321494, 'ic_mean': 0.024426500265388205, 'ic_std': 0.12016554616227704, 'ir': 0.2032737423121395}\n", "Dropped 91.8% entries from factor data: 4.7% in forward returns computation and 87.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 91.8%, consider increasing it.\n", "Dropped 4.4% entries from factor data: 4.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.8523801679192218, 'return_min': -0.9316810962223254, 'ic_mean': 0.009826011116324003, 'ic_std': 0.173389232954649, 'ir': 0.05667024963939979}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.808102398592638, 'return_min': -2.0192618191972045, 'ic_mean': 0.014720690509042381, 'ic_std': 0.1537689303505893, 'ir': 0.09573254151849515}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.387098769970121, 'return_min': -1.4901957525426113, 'ic_mean': 0.010020805196461207, 'ic_std': 0.14426637564368508, 'ir': 0.06946043491943678}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.279098541824176, 'return_min': -2.2482105056820245, 'ic_mean': -0.005269499454123663, 'ic_std': 0.13393463627913485, 'ir': -0.03934381427027907}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7170037769209667, 'return_min': -2.009931968144363, 'ic_mean': 0.00639274702835825, 'ic_std': 0.09693104099539664, 'ir': 0.0659514946162793}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.8880906687269707, 'return_min': -1.7409274593549462, 'ic_mean': 0.018076992039685682, 'ic_std': 0.0951677574091085, 'ir': 0.18994870249990292}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.700801659638046, 'return_min': -7.408396175828669, 'ic_mean': 0.018415419846265373, 'ic_std': 0.11615450353720837, 'ir': 0.15854245238426135}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.4752732955481953, 'return_min': -2.6677476959879876, 'ic_mean': 0.015342691823741036, 'ic_std': 0.07724071644529867, 'ir': 0.1986347684204434}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.34621034393444994, 'return_min': -2.4387667694436566, 'ic_mean': 0.01995871642621837, 'ic_std': 0.08762765998587475, 'ir': 0.22776731033826123}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.2051529231892353, 'return_min': -1.3990002916408617, 'ic_mean': 0.014825639768970804, 'ic_std': 0.14174430848020916, 'ir': 0.10459425092924145}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.3577355659122325, 'return_min': -5.05430927521755, 'ic_mean': 0.03189019517325839, 'ic_std': 0.15137026159807418, 'ir': 0.21067675272924358}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.019467244038676, 'return_min': -4.296553656235602, 'ic_mean': 0.022680989512857724, 'ic_std': 0.15285126994925327, 'ir': 0.1483860063471362}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.5060816910072994, 'return_min': -0.6278665480641266, 'ic_mean': 0.0017424080454145618, 'ic_std': 0.15369438631649196, 'ir': 0.01133683595851409}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -3.00098720505404, 'return_min': -0.9838506584203266, 'ic_mean': 0.004096977997442842, 'ic_std': 0.1031104986227054, 'ir': 0.039733858842388226}\n", "Dropped 89.3% entries from factor data: 4.1% in forward returns computation and 85.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 89.3%, consider increasing it.\n", "Dropped 8.8% entries from factor data: 4.2% in forward returns computation and 4.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5810203420764104, 'return_min': -5.024298470700517, 'ic_mean': 0.027160776632399367, 'ic_std': 0.182116460283364, 'ir': 0.1491396032524384}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.3722767786682475, 'return_min': -8.860852912119244, 'ic_mean': 0.0332919911235417, 'ic_std': 0.15478040873912843, 'ir': 0.215091763839783}\n", "Dropped 5.0% entries from factor data: 4.1% in forward returns computation and 0.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7509798072024019, 'return_min': -7.104857165177725, 'ic_mean': 0.013069798600613466, 'ic_std': 0.11988360882152435, 'ir': 0.10902073043255656}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.576240609674766, 'return_min': -3.9241495342778876, 'ic_mean': 0.018340335358541716, 'ic_std': 0.11767664988254933, 'ir': 0.1558536496139789}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.43216437765037, 'return_min': -3.554850099092288, 'ic_mean': 0.011521309029538124, 'ic_std': 0.15730654053830648, 'ir': 0.07324113155188557}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6643760341595861, 'return_min': -5.754006302097769, 'ic_mean': 0.004835117289363495, 'ic_std': 0.13959543184165366, 'ir': 0.03463664409053214}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.5027720629956498, 'return_min': -4.0045906370378415, 'ic_mean': 0.0038715139376878324, 'ic_std': 0.13497438740619003, 'ir': 0.028683322903603575}\n", "Dropped 5.2% entries from factor data: 5.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 10.307834057372567, 'return_min': -8.114865913938507, 'ic_mean': 0.023414429224366613, 'ic_std': 0.09993121714701829, 'ir': 0.23430545421977025}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.07355367618755437, 'return_min': -1.6132457173778647, 'ic_mean': 0.02190247733130282, 'ic_std': 0.1697499243935597, 'ir': 0.1290279062541591}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.687519837858531, 'return_min': 0.5619034112624988, 'ic_mean': 0.01190415476325013, 'ic_std': 0.13084592858170718, 'ir': 0.09097841172655624}\n", "Dropped 42.5% entries from factor data: 4.7% in forward returns computation and 37.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 42.5%, consider increasing it.\n", "Dropped 7.2% entries from factor data: 7.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.002245744605503, 'return_min': -4.323462753534901, 'ic_mean': 0.017249772744489765, 'ic_std': 0.12120403209945392, 'ir': 0.14232012290098955}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.677143343439049, 'return_min': -2.5951604472185252, 'ic_mean': 0.014752279711123561, 'ic_std': 0.13657305923158114, 'ir': 0.10801749476892618}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.9815145935642153, 'return_min': -6.595920193870342, 'ic_mean': 0.01797961120006331, 'ic_std': 0.1741120751409703, 'ir': 0.10326458509845494}\n", "Dropped 10.6% entries from factor data: 10.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.038435019945494, 'return_min': -4.80293988088909, 'ic_mean': 0.008842730083032968, 'ic_std': 0.18838851059213954, 'ir': 0.046938797144468365}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.577308123736469, 'return_min': -11.609178030199674, 'ic_mean': 0.043389631960710315, 'ic_std': 0.12942650153846302, 'ir': 0.3352453434570799}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.887591135379621, 'return_min': -1.411709740336553, 'ic_mean': 0.001738967531632804, 'ic_std': 0.14831731979036475, 'ir': 0.011724642368744948}\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.5747445404462646, 'return_min': -10.530854387160726, 'ic_mean': 0.0525472582446205, 'ic_std': 0.1945074639262502, 'ir': 0.27015548495632236}\n", "Dropped 6.4% entries from factor data: 4.8% in forward returns computation and 1.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.5384370746064349, 'return_min': -2.659344409483566, 'ic_mean': 0.004617519575575652, 'ic_std': 0.12322210640909917, 'ir': 0.037473142686308414}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.8752705573565898, 'return_min': -0.2067611657352586, 'ic_mean': 0.014456585446851227, 'ic_std': 0.09961160581355409, 'ir': 0.14512952912244015}\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.84669421603634, 'return_min': -3.9751963275080904, 'ic_mean': 0.0067002955794787135, 'ic_std': 0.0910683727674486, 'ir': 0.07357434173759236}\n", "Dropped 75.6% entries from factor data: 4.2% in forward returns computation and 71.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 75.6%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5987128410467335, 'return_min': -2.5758180408630427, 'ic_mean': -0.004741643872569683, 'ic_std': 0.1329726209366835, 'ir': -0.03565879832381039}\n", "Dropped 24.4% entries from factor data: 4.2% in forward returns computation and 20.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -13.94109622924189, 'return_min': -3.020091080765397, 'ic_mean': -0.0029211546268647233, 'ic_std': 0.16270980604084762, 'ir': -0.017953156591750712}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.3326794253388776, 'return_min': -3.967638577707655, 'ic_mean': 0.01610139102552725, 'ic_std': 0.0867556313373832, 'ir': 0.18559476517334872}\n", "Dropped 43.4% entries from factor data: 4.2% in forward returns computation and 39.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 43.4%, consider increasing it.\n", "Dropped 7.7% entries from factor data: 7.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.15016215111574027, 'return_min': -7.144119499026269, 'ic_mean': 0.012448958743642215, 'ic_std': 0.1657354100166598, 'ir': 0.07511345187121352}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.6429385750195138, 'return_min': -3.795279709191135, 'ic_mean': 0.019915503836117933, 'ic_std': 0.14042233052356468, 'ir': 0.14182576063125413}\n", "Dropped 7.1% entries from factor data: 4.2% in forward returns computation and 2.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.9624220081237986, 'return_min': -6.4955881917161395, 'ic_mean': 0.026695789625228106, 'ic_std': 0.16083336852278132, 'ir': 0.1659841478818916}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.3341678458312902, 'return_min': 0.45024607318477905, 'ic_mean': 0.0071630234264277795, 'ic_std': 0.09860259211584471, 'ir': 0.07264538662444285}\n", "Dropped 4.6% entries from factor data: 4.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.1474633951744657, 'return_min': -5.464833426989113, 'ic_mean': 0.026087333952396402, 'ic_std': 0.17007363368956935, 'ir': 0.15338846702136608}\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.931192000916518, 'return_min': -2.5846218116931663, 'ic_mean': 0.02292189134679965, 'ic_std': 0.16143758914994955, 'ir': 0.14198608556715314}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 24.1% entries from factor data: 24.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.7464235413290332, 'return_min': 0.8749719682699109, 'ic_mean': 0.004306097079108374, 'ic_std': 0.08900175661075622, 'ir': 0.048382158320097304}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 27.1% entries from factor data: 4.4% in forward returns computation and 22.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.519447511705186, 'return_min': -0.05356428262270896, 'ic_mean': 0.0011360961092162596, 'ic_std': 0.07533121615512363, 'ir': 0.015081345651937791}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.32583680449560504, 'return_min': -1.1665789823200878, 'ic_mean': 0.0017921690399205251, 'ic_std': 0.10756280992338135, 'ir': 0.016661604891106088}\n", "Dropped 22.2% entries from factor data: 4.5% in forward returns computation and 17.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7566965781168342, 'return_min': 0.7493457700102901, 'ic_mean': -0.007976225581141487, 'ic_std': 0.17688541203890223, 'ir': -0.045092613852109435}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.5% entries from factor data: 4.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.7199246959509473, 'return_min': -3.9410016785135227, 'ic_mean': 0.006287087249420141, 'ic_std': 0.12107836267800275, 'ir': 0.051925770305798535}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.3054440468752695, 'return_min': -0.11047055603730094, 'ic_mean': 0.007472935673431939, 'ic_std': 0.09145116641462822, 'ir': 0.08171503947309515}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.070443590533657, 'return_min': -13.299213531047238, 'ic_mean': 0.04122354725581315, 'ic_std': 0.1626903368042332, 'ir': 0.25338657516836927}\n", "Dropped 12.9% entries from factor data: 4.7% in forward returns computation and 8.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.0139221255133233, 'return_min': -2.7093675277589657, 'ic_mean': -0.009925522417565566, 'ic_std': 0.1824996831653666, 'ir': -0.054386518625195925}\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.5290611551942455, 'return_min': -0.290501792910014, 'ic_mean': -0.0048553479510056894, 'ic_std': 0.10623354230608403, 'ir': -0.045704471917318544}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.3% entries from factor data: 4.3% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.28818494590465704, 'return_min': -1.1234349916744435, 'ic_mean': 0.03223936291114111, 'ic_std': 0.11244260093102765, 'ir': 0.28671840249334635}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 14.2% entries from factor data: 4.3% in forward returns computation and 9.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -2.4619657560054353, 'return_min': -4.5519925153347796, 'ic_mean': 0.027641585428720084, 'ic_std': 0.16784653247829043, 'ir': 0.16468368467662742}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 67.4% entries from factor data: 4.1% in forward returns computation and 63.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 67.4%, consider increasing it.\n", "Dropped 4.1% entries from factor data: 4.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.10577122174337816, 'return_min': -1.9114150981214273, 'ic_mean': -0.0018741153438684415, 'ic_std': 0.08652533852211078, 'ir': -0.021659728535930872}\n", "Dropped 100.0% entries from factor data: 4.1% in forward returns computation and 95.9% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 4.2% entries from factor data: 4.2% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.601802716237998, 'return_min': -0.3742666796047178, 'ic_mean': -0.01598994698081562, 'ic_std': 0.1702926580522814, 'ir': -0.09389686651027881}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    0.041216    0.511833  0.005520  0.097544  0.056593\n", "alpha003    2.801292   -4.449060  0.013514  0.081928  0.164945\n", "alpha005   -0.560474  -10.201234  0.013015  0.192400  0.067647\n", "alpha006    7.646372   -7.430420  0.024427  0.120166  0.203274\n", "alpha008   -2.852380   -0.931681  0.009826  0.173389  0.056670\n", "alpha009   -2.808102   -2.019262  0.014721  0.153769  0.095733\n", "alpha010   -4.387099   -1.490196  0.010021  0.144266  0.069460\n", "alpha011   -2.279099   -2.248211 -0.005269  0.133935 -0.039344\n", "alpha012   -1.717004   -2.009932  0.006393  0.096931  0.065951\n", "alpha013    0.888091   -1.740927  0.018077  0.095168  0.189949\n", "alpha014    7.700802   -7.408396  0.018415  0.116155  0.158542\n", "alpha015    0.475273   -2.667748  0.015343  0.077241  0.198635\n", "alpha016   -0.346210   -2.438767  0.019959  0.087628  0.227767\n", "alpha017   -2.205153   -1.399000  0.014826  0.141744  0.104594\n", "alpha018    1.357736   -5.054309  0.031890  0.151370  0.210677\n", "alpha019    3.019467   -4.296554  0.022681  0.152851  0.148386\n", "alpha020   -1.506082   -0.627867  0.001742  0.153694  0.011337\n", "alpha022   -3.000987   -0.983851  0.004097  0.103110  0.039734\n", "alpha024   -0.581020   -5.024298  0.027161  0.182116  0.149140\n", "alpha025    2.372277   -8.860853  0.033292  0.154780  0.215092\n", "alpha026   -1.750980   -7.104857  0.013070  0.119884  0.109021\n", "alpha028    6.576241   -3.924150  0.018340  0.117677  0.155854\n", "alpha029    2.432164   -3.554850  0.011521  0.157307  0.073241\n", "alpha030   -0.664376   -5.754006  0.004835  0.139595  0.034637\n", "alpha031   -1.502772   -4.004591  0.003872  0.134974  0.028683\n", "alpha032   10.307834   -8.114866  0.023414  0.099931  0.234305\n", "alpha033    0.073554   -1.613246  0.021902  0.169750  0.129028\n", "alpha034    1.687520    0.561903  0.011904  0.130846  0.090978\n", "alpha036    3.002246   -4.323463  0.017250  0.121204  0.142320\n", "alpha037    3.677143   -2.595160  0.014752  0.136573  0.108017\n", "alpha038   -1.981515   -6.595920  0.017980  0.174112  0.103265\n", "alpha039   -1.038435   -4.802940  0.008843  0.188389  0.046939\n", "alpha040    5.577308  -11.609178  0.043390  0.129427  0.335245\n", "alpha041   -4.887591   -1.411710  0.001739  0.148317  0.011725\n", "alpha042    6.574745  -10.530854  0.052547  0.194507  0.270155\n", "alpha043   -0.538437   -2.659344  0.004618  0.123222  0.037473\n", "alpha044   -1.875271   -0.206761  0.014457  0.099612  0.145130\n", "alpha045   -4.846694   -3.975196  0.006700  0.091068  0.073574\n", "alpha047    0.598713   -2.575818 -0.004742  0.132973 -0.035659\n", "alpha049  -13.941096   -3.020091 -0.002921  0.162710 -0.017953\n", "alpha050    0.332679   -3.967639  0.016101  0.086756  0.185595\n", "alpha052    0.150162   -7.144119  0.012449  0.165735  0.075113\n", "alpha053    0.642939   -3.795280  0.019916  0.140422  0.141826\n", "alpha054    1.962422   -6.495588  0.026696  0.160833  0.165984\n", "alpha055   -0.334168    0.450246  0.007163  0.098603  0.072645\n", "alpha057    3.147463   -5.464833  0.026087  0.170074  0.153388\n", "alpha060    4.931192   -2.584622  0.022922  0.161438  0.141986\n", "alpha066   -0.746424    0.874972  0.004306  0.089002  0.048382\n", "alpha071   -2.519448   -0.053564  0.001136  0.075331  0.015081\n", "alpha072    0.325837   -1.166579  0.001792  0.107563  0.016662\n", "alpha073   -1.756697    0.749346 -0.007976  0.176885 -0.045093\n", "alpha077   -1.719925   -3.941002  0.006287  0.121078  0.051926\n", "alpha078    0.305444   -0.110471  0.007473  0.091451  0.081715\n", "alpha083    4.070444  -13.299214  0.041224  0.162690  0.253387\n", "alpha084   -2.013922   -2.709368 -0.009926  0.182500 -0.054387\n", "alpha085   -2.529061   -0.290502 -0.004855  0.106234 -0.045704\n", "alpha088   -0.288185   -1.123435  0.032239  0.112443  0.286718\n", "alpha094   -2.461966   -4.551993  0.027642  0.167847  0.164684\n", "alpha098   -0.105771   -1.911415 -0.001874  0.086525 -0.021660\n", "alpha101    0.601803   -0.374267 -0.015990  0.170293 -0.093897\n", "login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\code\\StockProject\\alphas\\alphas\\analy_alphas.py:52: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Dropped 100.0% entries from factor data: 25.8% in forward returns computation and 74.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.7393295590051316, 'return_min': -2.0197437874036606, 'ic_mean': 0.01587473362084304, 'ic_std': 0.091125629250307, 'ir': 0.1742071220955608}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.1382930091086152, 'return_min': 9.630295669109668, 'ic_mean': -0.006409761871546377, 'ic_std': 0.07880300618128823, 'ir': -0.08133905268538315}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.711291243388761, 'return_min': -7.154775203540575, 'ic_mean': 0.02777060714434915, 'ic_std': 0.15943344713040633, 'ir': 0.1741830691375228}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.4044131688727433, 'return_min': 0.127423313902586, 'ic_mean': 0.006100548861808641, 'ic_std': 0.11455051478145926, 'ir': 0.053256407214296114}\n", "Dropped 94.9% entries from factor data: 25.9% in forward returns computation and 69.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 94.9%, consider increasing it.\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -1.9492460105874354, 'return_min': -2.0585566408415357, 'ic_mean': -0.01247677059085159, 'ic_std': 0.14329183313345303, 'ir': -0.08707244731269163}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.090740019992879, 'return_min': -7.682473592300898, 'ic_mean': 0.03386034583742496, 'ic_std': 0.12435764079212325, 'ir': 0.27228198944386584}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.573731764685451, 'return_min': -8.540840485672385, 'ic_mean': 0.025375021621445625, 'ic_std': 0.1271697212710031, 'ir': 0.19953666146181584}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.227439626962262, 'return_min': -1.1794570472889454, 'ic_mean': 0.016287717872895836, 'ic_std': 0.1163219021895211, 'ir': 0.14002279507395402}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 8.995680822172503, 'return_min': -9.800769181561586, 'ic_mean': 0.030727807585009, 'ic_std': 0.07540695900365489, 'ir': 0.4074929952223595}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.7670557222815297, 'return_min': 0.09877112516321063, 'ic_mean': 0.014957639524121972, 'ic_std': 0.10351446228537328, 'ir': 0.14449806523542658}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.98534084268254, 'return_min': -11.720140400278689, 'ic_mean': 0.02223916226728026, 'ic_std': 0.10907963220632148, 'ir': 0.20388006282616927}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.2294112122929235, 'return_min': 11.163866268784073, 'ic_mean': -0.005416441216508675, 'ic_std': 0.0922717400591193, 'ir': -0.058700976193126024}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6158937713174328, 'return_min': 5.764745621956724, 'ic_mean': 0.007441205262632865, 'ic_std': 0.10809285046387741, 'ir': 0.06884086441146794}\n", "Dropped 25.8% entries from factor data: 25.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.502210842742716, 'return_min': -8.625125922254728, 'ic_mean': 0.03166323914340283, 'ic_std': 0.11211999701244003, 'ir': 0.28240492318145266}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.323589498687163, 'return_min': -6.5155196939736815, 'ic_mean': 0.03792884375608365, 'ic_std': 0.15322879825689875, 'ir': 0.24753077872798626}\n", "Dropped 47.5% entries from factor data: 47.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.5%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.2322818781006504, 'return_min': 3.744672431591489, 'ic_mean': -0.016344585779052487, 'ic_std': 0.12842866253409765, 'ir': -0.12726587240377918}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.34811152758596, 'return_min': -3.1495704098727373, 'ic_mean': 0.0015496572078206575, 'ic_std': 0.10910985618187205, 'ir': 0.014202724318851442}\n", "Dropped 71.8% entries from factor data: 25.6% in forward returns computation and 46.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 71.8%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.680063175249046, 'return_min': -3.9970918054876847, 'ic_mean': 0.0449118617786812, 'ic_std': 0.17443807478744894, 'ir': 0.2574659335893603}\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 2.6788109780673253, 'return_min': -3.2396320908356646, 'ic_mean': 0.03278476021702533, 'ic_std': 0.12701422360376666, 'ir': 0.2581188097429199}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 3.6757207269255687, 'return_min': -2.846768727622351, 'ic_mean': 0.02163259230383407, 'ic_std': 0.10590162063771162, 'ir': 0.20427064452430763}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 13.95952116599597, 'return_min': -10.131989085535587, 'ic_mean': 0.027776131691389892, 'ic_std': 0.1009778764013589, 'ir': 0.275071458038863}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -16.79142467322481, 'return_min': 8.847656759378708, 'ic_mean': -0.04952050032867032, 'ic_std': 0.1452237834204983, 'ir': -0.34099442365637}\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.364111354838947, 'return_min': -6.574972850806882, 'ic_mean': 0.04609399047771349, 'ic_std': 0.12089097871573373, 'ir': 0.3812856092934786}\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.034009446171279, 'return_min': -5.3855013011594455, 'ic_mean': 0.031853278190337855, 'ic_std': 0.10477737582780512, 'ir': 0.3040091235219201}\n", "Dropped 27.1% entries from factor data: 27.1% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.490387553188537, 'return_min': -6.412406137498161, 'ic_mean': 0.02318848761272485, 'ic_std': 0.08752412545475446, 'ir': 0.2649382383684842}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.999137672955463, 'return_min': -14.601005372046894, 'ic_mean': 0.05978748935301311, 'ic_std': 0.11535565475746981, 'ir': 0.51828832733613}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.7039993390599584, 'return_min': -5.3680925060439755, 'ic_mean': 0.025093463795218218, 'ic_std': 0.1301514936058508, 'ir': 0.19280196561716734}\n", "Dropped 38.6% entries from factor data: 25.8% in forward returns computation and 12.8% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 38.6%, consider increasing it.\n", "Dropped 27.0% entries from factor data: 27.0% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.6938751302609791, 'return_min': 5.5610197262701, 'ic_mean': -0.02431038849556415, 'ic_std': 0.07700238137413645, 'ir': -0.31570956718137966}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.800890418168116, 'return_min': -3.3443737807326013, 'ic_mean': 0.03410270369272159, 'ic_std': 0.0816944301439138, 'ir': 0.41744221280013705}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 6.274803953065788, 'return_min': -1.4933874945999293, 'ic_mean': 0.03265304374750718, 'ic_std': 0.1354649475951163, 'ir': 0.24104422824642477}\n", "Dropped 47.5% entries from factor data: 47.5% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 47.5%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 5.82341020219701, 'return_min': -1.332612123655652, 'ic_mean': 0.040778739282964334, 'ic_std': 0.1481518088821844, 'ir': 0.27524968875265665}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5710594492258636, 'return_min': -1.9820334354914149, 'ic_mean': 0.022737545033544387, 'ic_std': 0.12439321004920872, 'ir': 0.18278767003881996}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.158457641378899, 'return_min': -16.651585457377884, 'ic_mean': 0.06755940493020969, 'ic_std': 0.2097097326488001, 'ir': 0.322156745311154}\n", "Dropped 25.9% entries from factor data: 25.9% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.49684702064123343, 'return_min': -5.0270469762236925, 'ic_mean': 0.019037100294100877, 'ic_std': 0.1408280360896643, 'ir': 0.13517976123717354}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -6.200700547410731, 'return_min': 7.352227785188958, 'ic_mean': -0.012198356171197813, 'ic_std': 0.10794212992823564, 'ir': -0.11300829601294492}\n", "Dropped 25.8% entries from factor data: 25.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.830718498187174, 'return_min': -2.3083125698541984, 'ic_mean': 0.02740696256930427, 'ic_std': 0.07289891968210331, 'ir': 0.3759584187093609}\n", "Dropped 74.4% entries from factor data: 25.6% in forward returns computation and 48.7% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 74.4%, consider increasing it.\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 17.4247492627444, 'return_min': -10.280416657802993, 'ic_mean': 0.042435545177126235, 'ic_std': 0.12442787591699822, 'ir': 0.34104532336012555}\n", "Dropped 48.7% entries from factor data: 25.6% in forward returns computation and 23.1% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 48.7%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -5.448900337642337, 'return_min': 7.055373303195278, 'ic_mean': 0.000321476077306744, 'ic_std': 0.08762790360846301, 'ir': 0.0036686496431907814}\n", "Dropped 53.8% entries from factor data: 25.6% in forward returns computation and 28.2% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 53.8%, consider increasing it.\n", "Dropped 27.4% entries from factor data: 27.4% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 7.989000457422346, 'return_min': -8.576014833709955, 'ic_mean': 0.04411798062697764, 'ic_std': 0.12184710734908992, 'ir': 0.3620765530410202}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 9.55954489162858, 'return_min': -7.243138533639115, 'ic_mean': 0.04514124072310028, 'ic_std': 0.12784810485858783, 'ir': 0.3530849422682549}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.666784933608287, 'return_min': -20.172271407485898, 'ic_mean': 0.08031663075696943, 'ic_std': 0.11641035984407405, 'ir': 0.68994401241049}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 1.5738288855793314, 'return_min': -4.201114307085252, 'ic_mean': 0.013110273063529802, 'ic_std': 0.07944957831568729, 'ir': 0.16501375263990775}\n", "Dropped 25.8% entries from factor data: 25.8% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 10.669170188719601, 'return_min': -10.353373375138197, 'ic_mean': 0.05200059151043978, 'ic_std': 0.16206923846409363, 'ir': 0.3208541732116579}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 12.217788845685362, 'return_min': -20.9422863619535, 'ic_mean': 0.07244636029053061, 'ic_std': 0.1259920874133311, 'ir': 0.5750072228969606}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 41.6% entries from factor data: 41.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 41.6%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 46.2% entries from factor data: 25.7% in forward returns computation and 20.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 46.2%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 4.8721778692484286, 'return_min': -0.7985102842533731, 'ic_mean': 0.013114188670722789, 'ic_std': 0.11005145506861544, 'ir': 0.11916415519037243}\n", "Dropped 46.2% entries from factor data: 25.7% in forward returns computation and 20.5% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 46.2%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 0.5397914861893227, 'return_min': -0.7891149714489565, 'ic_mean': 0.016986764864074615, 'ic_std': 0.08141216454212342, 'ir': 0.20865143384420767}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.1573315612468651, 'return_min': -6.253219883531891, 'ic_mean': 0.0173683679869004, 'ic_std': 0.10383509413539868, 'ir': 0.16726876526208403}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.7% entries from factor data: 25.7% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 11.459710271619628, 'return_min': -14.32046023714717, 'ic_mean': 0.0764458545372505, 'ic_std': 0.16425727481946903, 'ir': 0.4654031586806136}\n", "Dropped 28.4% entries from factor data: 25.8% in forward returns computation and 2.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -0.8930251104877218, 'return_min': 2.870075835819108, 'ic_mean': -0.019765966801208457, 'ic_std': 0.16112363168292965, 'ir': -0.12267577756753466}\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 14.404678324260178, 'return_min': -9.515394693316859, 'ic_mean': 0.027123023292733996, 'ic_std': 0.10822874197434744, 'ir': 0.25060832083923457}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.926642009319693, 'return_min': 4.438605282743868, 'ic_mean': 0.019587532687999405, 'ic_std': 0.1426732699279341, 'ir': 0.1372894354905673}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 28.2% entries from factor data: 25.6% in forward returns computation and 2.6% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -4.593920525242101, 'return_min': -0.12234881518358165, 'ic_mean': 0.03447231624009057, 'ic_std': 0.15855283449545007, 'ir': 0.2174184797754581}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 82.1% entries from factor data: 25.6% in forward returns computation and 56.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 82.1%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': 10.141070878753045, 'return_min': -6.19443721924906, 'ic_mean': 0.00863913677592391, 'ic_std': 0.08357864149994258, 'ir': 0.10336536489325261}\n", "Dropped 100.0% entries from factor data: 25.6% in forward returns computation and 74.4% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss (35.0%) exceeded 100.0%, consider increasing it.\n", "Dropped 25.6% entries from factor data: 25.6% in forward returns computation and 0.0% in binning phase (set max_loss=0 to see potentially suppressed Exceptions).\n", "max_loss is 35.0%, not exceeded: OK!\n", "{'return_max': -8.932320483477918, 'return_min': 8.791992790435632, 'ic_mean': -0.06283665863527638, 'ic_std': 0.11483102322417126, 'ir': -0.5472097772098371}\n", "          return_max  return_min   ic_mean    ic_std        ir\n", "name                                                          \n", "alpha002    1.739330   -2.019744  0.015875  0.091126  0.174207\n", "alpha003    1.138293    9.630296 -0.006410  0.078803 -0.081339\n", "alpha005    5.711291   -7.154775  0.027771  0.159433  0.174183\n", "alpha006    2.404413    0.127423  0.006101  0.114551  0.053256\n", "alpha008   -1.949246   -2.058557 -0.012477  0.143292 -0.087072\n", "alpha009    5.090740   -7.682474  0.033860  0.124358  0.272282\n", "alpha010    4.573732   -8.540840  0.025375  0.127170  0.199537\n", "alpha011    7.227440   -1.179457  0.016288  0.116322  0.140023\n", "alpha012    8.995681   -9.800769  0.030728  0.075407  0.407493\n", "alpha013    1.767056    0.098771  0.014958  0.103514  0.144498\n", "alpha014   -4.985341  -11.720140  0.022239  0.109080  0.203880\n", "alpha015   -6.229411   11.163866 -0.005416  0.092272 -0.058701\n", "alpha016   -0.615894    5.764746  0.007441  0.108093  0.068841\n", "alpha017    5.502211   -8.625126  0.031663  0.112120  0.282405\n", "alpha018    3.323589   -6.515520  0.037929  0.153229  0.247531\n", "alpha020    2.232282    3.744672 -0.016345  0.128429 -0.127266\n", "alpha022    1.348112   -3.149570  0.001550  0.109110  0.014203\n", "alpha024    0.680063   -3.997092  0.044912  0.174438  0.257466\n", "alpha025    2.678811   -3.239632  0.032785  0.127014  0.258119\n", "alpha026    3.675721   -2.846769  0.021633  0.105902  0.204271\n", "alpha028   13.959521  -10.131989  0.027776  0.100978  0.275071\n", "alpha029  -16.791425    8.847657 -0.049521  0.145224 -0.340994\n", "alpha030   12.364111   -6.574973  0.046094  0.120891  0.381286\n", "alpha031   12.034009   -5.385501  0.031853  0.104777  0.304009\n", "alpha032    6.490388   -6.412406  0.023188  0.087524  0.264938\n", "alpha033   12.999138  -14.601005  0.059787  0.115356  0.518288\n", "alpha034    1.703999   -5.368093  0.025093  0.130151  0.192802\n", "alpha036   -0.693875    5.561020 -0.024310  0.077002 -0.315710\n", "alpha037    7.800890   -3.344374  0.034103  0.081694  0.417442\n", "alpha038    6.274804   -1.493387  0.032653  0.135465  0.241044\n", "alpha040    5.823410   -1.332612  0.040779  0.148152  0.275250\n", "alpha041    0.571059   -1.982033  0.022738  0.124393  0.182788\n", "alpha042    4.158458  -16.651585  0.067559  0.209710  0.322157\n", "alpha043    0.496847   -5.027047  0.019037  0.140828  0.135180\n", "alpha044   -6.200701    7.352228 -0.012198  0.107942 -0.113008\n", "alpha045    9.830718   -2.308313  0.027407  0.072899  0.375958\n", "alpha047   17.424749  -10.280417  0.042436  0.124428  0.341045\n", "alpha050   -5.448900    7.055373  0.000321  0.087628  0.003669\n", "alpha052    7.989000   -8.576015  0.044118  0.121847  0.362077\n", "alpha053    9.559545   -7.243139  0.045141  0.127848  0.353085\n", "alpha054   11.666785  -20.172271  0.080317  0.116410  0.689944\n", "alpha055    1.573829   -4.201114  0.013110  0.079450  0.165014\n", "alpha057   10.669170  -10.353373  0.052001  0.162069  0.320854\n", "alpha060   12.217789  -20.942286  0.072446  0.125992  0.575007\n", "alpha072    4.872178   -0.798510  0.013114  0.110051  0.119164\n", "alpha077    0.539791   -0.789115  0.016987  0.081412  0.208651\n", "alpha078   -0.157332   -6.253220  0.017368  0.103835  0.167269\n", "alpha083   11.459710  -14.320460  0.076446  0.164257  0.465403\n", "alpha084   -0.893025    2.870076 -0.019766  0.161124 -0.122676\n", "alpha085   14.404678   -9.515395  0.027123  0.108229  0.250608\n", "alpha088   -4.926642    4.438605  0.019588  0.142673  0.137289\n", "alpha094   -4.593921   -0.122349  0.034472  0.158553  0.217418\n", "alpha098   10.141071   -6.194437  0.008639  0.083579  0.103365\n", "alpha101   -8.932320    8.791993 -0.062837  0.114831 -0.547210\n"]}], "source": ["from analy_alphas import *\n", "\n", "start = 2013\n", "end = 2023\n", "alpha_name = 'Alphas101'\n", "for year in range(start, end + 1):  # 2013-2023, including 2022 and 2023.\n", "    list_assets, df_assets = get_hs300_stocks(f'{"]}], "metadata": {"date": "2025-07-03 13:43:05", "excerpt": "this is description for 3.factorycompare", "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "price": 20, "title": "from analy_alphas import *", "vscode": {"interpreter": {"hash": "e42634819b8c191a5d07eaf23810ff32516dd8d3875f28ec3e488928fbd3c187"}}}, "nbformat": 4, "nbformat_minor": 5}