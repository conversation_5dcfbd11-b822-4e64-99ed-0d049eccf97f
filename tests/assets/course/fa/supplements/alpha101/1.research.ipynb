{"cells": [{"cell_type": "code", "execution_count": 2, "id": "5995a446", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/akshare/__init__.py:2888: UserWarning: 为了支持更多特性，请将 Pandas 升级到 2.2.0 及以上版本！\n", "  warnings.warn(\n", "/home/<USER>/miniconda3/envs/coursea/lib/python3.8/site-packages/akshare/__init__.py:2893: UserWarning: 为了支持更多特性，请将 Python 升级到 3.9.0 及以上版本！\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n", "query_hs300 error_code:0\n", "query_hs300  error_msg:success\n", "logout success!\n", "300\n", "asset       000001  000002  000063  000066  000069  000100  000157  000166  \\\n", "date                                                                         \n", "2023-01-03   13.77   18.23   25.93   10.73    5.29    3.78    5.48    3.98   \n", "2023-01-04   14.32   19.07   26.44   10.68    5.59    3.85    5.53    3.99   \n", "2023-01-05   14.48   19.33   26.44   10.79    5.57    3.84    5.55    4.02   \n", "2023-01-06   14.62   19.22   26.00   10.65    5.44    3.86    5.54    4.01   \n", "2023-01-09   14.80   18.78   25.64   10.59    5.37    3.86    5.61    4.03   \n", "...            ...     ...     ...     ...     ...     ...     ...     ...   \n", "2023-12-25    9.19   10.30   25.32   10.33    3.05    3.97    6.52    4.41   \n", "2023-12-26    9.10   10.17   24.64    9.82    3.00    3.94    6.46    4.36   \n", "2023-12-27    9.12   10.17   24.64    9.81    3.02    4.13    6.54    4.40   \n", "2023-12-28    9.45   10.52   26.30    9.97    3.16    4.16    6.57    4.46   \n", "2023-12-29    9.39   10.46   26.48   10.12    3.11    4.30    6.53    4.44   \n", "\n", "asset       000301  000333  ...  688036  688065  688111  688126  688169  \\\n", "date                        ...                                           \n", "2023-01-03   13.13   51.88  ...   79.00   62.60  283.94   17.83  253.83   \n", "2023-01-04   12.94   53.75  ...   79.23   61.51  281.58   17.77  267.60   \n", "2023-01-05   13.22   54.91  ...   79.47   62.86  275.80   17.89  275.51   \n", "2023-01-06   14.54   54.01  ...   77.44   62.08  278.49   17.97  270.96   \n", "2023-01-09   14.80   54.11  ...   76.43   62.15  273.27   18.16  270.65   \n", "...            ...     ...  ...     ...     ...     ...     ...     ...   \n", "2023-12-25    9.00   52.67  ...  130.31   55.02  312.03   17.03  280.35   \n", "2023-12-26    9.36   52.80  ...  130.10   53.49  307.70   16.67  280.00   \n", "2023-12-27    9.36   53.60  ...  132.30   53.37  304.67   16.47  279.10   \n", "2023-12-28    9.64   54.05  ...  133.37   54.85  313.55   17.11  282.40   \n", "2023-12-29    9.60   54.63  ...  138.40   54.98  316.20   17.32  282.95   \n", "\n", "asset       688363  688396  688561  688599  688981  \n", "date                                                \n", "2023-01-03  130.50   53.31   69.92   66.40   41.54  \n", "2023-01-04  129.87   53.18   70.30   64.82   41.92  \n", "2023-01-05  133.83   53.46   70.52   65.66   41.87  \n", "2023-01-06  131.48   53.61   68.25   70.48   41.68  \n", "2023-01-09  132.87   53.74   70.26   71.68   41.53  \n", "...            ...     ...     ...     ...     ...  \n", "2023-12-25   64.27   44.20   39.25   26.35   53.01  \n", "2023-12-26   64.02   42.90   38.30   26.36   51.85  \n", "2023-12-27   63.46   43.35   38.12   26.12   51.91  \n", "2023-12-28   67.99   44.63   39.99   29.10   53.13  \n", "2023-12-29   66.93   44.69   40.09   28.53   53.02  \n", "\n", "[242 rows x 300 columns]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2190878/3169267420.py:10: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_all['date'] = pd.to_datetime(df_all['date'])\n"]}], "source": ["import pandas as pd\n", "from datas import *\n", "from alphas101 import Alphas101\n", "from alphas191 import Alphas191\n", "\n", "year = 2023\n", "list_assets, df_assets = get_hs300_stocks(f'{year}-01-01')\n", "dfs= get_all_date_data(f"]}], "metadata": {"date": "2025-05-10 13:43:05", "excerpt": "this is description for 1.research", "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}, "price": 95, "title": "import pandas as pd", "vscode": {"interpreter": {"hash": "e42634819b8c191a5d07eaf23810ff32516dd8d3875f28ec3e488928fbd3c187"}}}, "nbformat": 4, "nbformat_minor": 5}