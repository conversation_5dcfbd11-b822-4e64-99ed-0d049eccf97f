{"cells": [{"cell_type": "markdown", "id": "b991827f", "metadata": {}, "source": ["本文件夹所有代码来自于 https://github.com/popbo/alphas，为适配本课程环境，做了数据加载路径修改。\n", "\n", "本项目在开源的Alpha101,191实现的基础上，完成了数据获取、因子计算、Alphalens回测和backtrader回测。为感谢[popbo](https://github.com/popbo)的工作，您可以访问原项目主页，为该项目点赞。\n", "\n", "此项目为补充资料，我"]}], "metadata": {"date": "2025-07-27 13:43:05", "excerpt": "this is description for README", "kernelspec": {"display_name": "coursea", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.10"}, "price": 48, "title": "本文件夹所有代码来自于 https://github.com/popbo/alphas，为适配本课程环境，做了数据加载路径修改。"}, "nbformat": 4, "nbformat_minor": 5}