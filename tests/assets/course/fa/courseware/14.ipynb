{"cells": [{"cell_type": "markdown", "id": "a1597350", "metadata": {}, "source": ["# 14. 机器学习核心概念\n", "\n", "在上一章中，我们简单地接触了一个决策树模型。我们看到仅仅通过定义一个决策树对象（DecisionTreeClassifier），然后传入训练数据和标签进行训练（fit），就神奇地生成了一个预测模型。\n", "\n", "但在内部，这一切究竟是怎么发生的呢？\n", "\n", "机器学习涉及到线性代数、梯度优化、反向传播、激活函数等等基础概念，它的内容已超出了本课的范围。但是，在这些底层概念之上，仍然有"]}], "metadata": {"date": "2025-08-08 13:43:05", "excerpt": "this is description for 14", "price": 17, "title": " 14. 机器学习核心概念"}, "nbformat": 4, "nbformat_minor": 5}