{"cells": [{"cell_type": "markdown", "id": "985c5855", "metadata": {}, "source": ["# 15. SKLearn 通用工具包\n", "\n", "在前面的课程中，我们已经或多或少接触了 Sklearn。\n", "\n", "Scikit learn 是一个非常全面的机器学习库，涵盖了广泛的传统机器学习算法。除了常见的监督学习算法如线性回归、逻辑回归、支持向量机（SVM）、朴素贝叶斯等用于分类和回归任务，还包括无监督学习算法如聚类算法（K-Means、DBSCAN 等）、降维算法（PCA、t-SNE 等）。这使得它能够"]}], "metadata": {"date": "2025-05-26 13:43:05", "excerpt": "this is description for 15", "price": 75, "title": " 15. SKLearn 通用工具包"}, "nbformat": 4, "nbformat_minor": 5}