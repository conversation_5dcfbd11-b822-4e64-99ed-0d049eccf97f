{"cells": [{"cell_type": "markdown", "id": "56413ca9", "metadata": {}, "source": ["# 解读 ALPHALENS 报表\n", "<br>\n", "解读 Alphalens 报表是件很考经验的活儿。一方面，即使我们输入的因子数据或者价格数据是错误的，Alphalens 也会照常生成报表，但这些结论无疑是似是而非的。另一方面，即使这个过程完全正确，要作出因子的质量好坏这一结论也不容易：因为我们看到的报表是种类繁多，你要了解每一个指标的实现方式才能读懂它，了解它的单位和经验值，并且不同的指标之间，也可"]}], "metadata": {"date": "2025-05-15 13:43:05", "excerpt": "this is description for 05", "kernelspec": {"display_name": "course", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "price": 8, "title": " 解读 ALPHALENS 报表"}, "nbformat": 4, "nbformat_minor": 5}