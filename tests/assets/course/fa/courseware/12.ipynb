{"cells": [{"cell_type": "markdown", "id": "dcb160a4", "metadata": {}, "source": ["# 12. 因子挖掘创新方法\n", "\n", "<br>\n", "\n", "挖掘新因子就是一种创新。在此过程中，我们要运用新技术、开拓新思路、跨界融合，从不同的维度上进行尝试。\n", "\n", "传统的因子挖掘主要依赖于经济理论和交易经验（这仍然很重要），但随着计算机和大数据技术的兴起，一部分人转向了数据驱动方法，他们通过聚类、主成分分析等方法，从大量特征中提取代表性的因子。\n", "\n", "由于计算技术的限制，传统的因子模型假设因子与资产收益之间存在线性关"]}], "metadata": {"date": "2025-08-09 13:43:05", "excerpt": "this is description for 12", "price": 73, "title": " 12. 因子挖掘创新方法"}, "nbformat": 4, "nbformat_minor": 5}