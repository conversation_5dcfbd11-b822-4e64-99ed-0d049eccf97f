{"cells": [{"cell_type": "markdown", "id": "6edfe3ee", "metadata": {}, "source": ["# 驭浪者 -- 顶底预测模型\n", "\n", "<br>\n", "\n", "在上一章中，我们介绍了一个短时的资产定价模型。它是由lightGBM构建的、基于回归任务的模型。它的原理是，我们找出某些特殊场景下的定价规律，根据这些规律来预测未来一个时期的股价（或者涨跌幅），再通过训练，让lightGBM知道在何种情况下可以运用这些规律来预测股价（或者涨跌幅），而何时不能。\n", "\n", "基于这样的模型，我们可以买入预测涨幅较高的资产，卖出持仓"]}], "metadata": {"date": "2025-06-27 13:43:05", "excerpt": "this is description for 20", "price": 77, "title": " 驭浪者 -- 顶底预测模型"}, "nbformat": 4, "nbformat_minor": 5}