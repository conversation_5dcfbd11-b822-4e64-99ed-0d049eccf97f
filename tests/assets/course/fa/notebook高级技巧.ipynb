{"cells": [{"cell_type": "markdown", "id": "d677f1b4", "metadata": {}, "source": ["# notebook高级技巧\n", "\n", "网上有很多jupyter的使用技巧。但我相信，这篇文章会让你全面涨姿势。很多用法，你应该没见过。\n", "\n", "## 1. 环境变量\n", "默认情况下，jupyter notebook/lab不会读取你主机的环境变量。如果有一些secret（比如账号、密码）需要通过环境变量传递给notebook的话，我们需要修改它的kernel.json文件。\n", "\n", "首先，我们通过`jupyter ke"]}], "metadata": {"date": "2025-07-04 13:43:05", "excerpt": "this is description for notebook高级技巧", "price": 99, "title": " notebook高级技巧"}, "nbformat": 4, "nbformat_minor": 5}