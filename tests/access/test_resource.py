import datetime
from zoneinfo import ZoneInfo

from provision.service.resources import ResourceManager
from tests import add_course_fa, add_customer_aaron, add_whitelist, setup_test_env


def test_update_resource(setup_test_env, add_customer_aaron):
    from provision.service.resources import rm

    rm.update_resources()
    rm.build_path_map()

    start_time = datetime.datetime.now(tz=ZoneInfo("Asia/Shanghai"))
    add_course_fa(start_time - datetime.timedelta(days=3))
    rm.build_cache()

    assert rm.customer_resource_id_cache[1], set([1, 2, 3, 4])
    assert rm.controlled_resources == set([*range(1, 19), 21, 22])


def test_update_resource_platinum(setup_test_env, add_customer_aaron):
    from provision.service.resources import rm

    # test platinum
    start_time = datetime.datetime.now(tz=ZoneInfo("Asia/Shanghai"))
    add_course_fa(start_time - datetime.timedelta(days=7), plan="platinum")
    rm.update_resources()
    rm.build_path_map()
    rm.build_cache()

    assert rm.customer_resource_id_cache[1], set(range(1, 22))
    assert rm.controlled_resources == set(set(range(1, 55)))


def test_update_resource_platinum_refund(setup_test_env, add_customer_aaron):
    from provision.service.resources import rm

    # test platinum
    start_time = datetime.datetime.now(tz=ZoneInfo("Asia/Shanghai"))
    add_course_fa(start_time - datetime.timedelta(days=5), plan="platinum")
    rm.update_resources()
    rm.build_path_map()
    rm.build_cache()

    # 21, 22 是习题
    assert rm.customer_resource_id_cache[1], set([*range(1, 6), 21, 22])
    assert rm.controlled_resources == set(set(range(1, 55)))


def test_update_resource_bronze_whitelist(setup_test_env, add_customer_aaron):
    from provision.service.resources import rm

    rm.update_resources()
    rm.build_path_map()

    start_time = datetime.datetime.now(tz=ZoneInfo("Asia/Shanghai"))
    add_course_fa(start_time - datetime.timedelta(days=3))
    add_whitelist(1, 20, "fa")

    rm.build_cache()

    assert rm.customer_resource_id_cache[1], set([1, 2, 3, 4, 20])
    assert rm.controlled_resources == set(set(range(1, 55)))


def test_parse_url_path():
    paths = [
        "/course/fa/preview/lab/tree/courseware/02.ipynb",
        "/course/l24/preview/api/contents/assignments/answers/03.ipynb",
    ]

    expected = [
        ("fa", "preview", "courseware/02.ipynb", "courseware", "lab/tree"),
        (
            "l24",
            "preview",
            "assignments/answers/03.ipynb",
            "assignments",
            "api/contents",
        ),
    ]
    rm = ResourceManager()
    for i, path in enumerate(paths):
        result = rm.parse_url_path(path)
        assert result == expected[i]


def test_count_chapters(setup_test_env):
    from provision.service.resources import rm
    from provision.store.resource import ResourceModel

    rm.update_resources()
    chapters = ResourceModel.count("fa", "courseware")
    assert chapters == 20
