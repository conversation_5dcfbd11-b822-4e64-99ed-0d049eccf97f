"""
测试博客文章发布 API 的单元测试

这个测试文件验证 sprint#19 中实现的博客文章发布功能。
"""

import pytest


class TestPublishResourceAPI:
    """测试发布资源 API 的核心逻辑"""

    def test_required_fields_validation(self):
        """测试必需字段验证逻辑"""
        # seq 字段会自动生成，不需要客户端提供
        required_fields = ["course", "resource", "title", "rel_path", "division"]

        # 测试所有字段都存在
        complete_meta = {field: f"test_{field}" for field in required_fields}
        for field in required_fields:
            assert field in complete_meta

        # 测试缺少字段
        for missing_field in required_fields:
            incomplete_meta = {field: f"test_{field}" for field in required_fields if field != missing_field}
            assert missing_field not in incomplete_meta

    def test_api_token_auth_decorator_exists(self):
        """测试 API token 认证装饰器是否存在"""
        try:
            from provision.api.admin_token_auth import api_token_required
            assert callable(api_token_required)
        except ImportError:
            pytest.fail("api_token_required decorator not found")

    def test_publish_resource_endpoint_exists(self):
        """测试发布资源端点是否存在"""
        try:
            from provision.api.admin_resource import publish_resource
            assert callable(publish_resource)
        except ImportError:
            pytest.fail("publish_resource endpoint not found")

    def test_whitelist_for_all_model_exists(self):
        """测试公开白名单模型是否存在"""
        try:
            from provision.store.whitelist_for_all import WhitelistForAllModel
            assert hasattr(WhitelistForAllModel, 'add_resource')
        except ImportError:
            pytest.fail("WhitelistForAllModel not found")

    def test_resource_manager_build_cache_exists(self):
        """测试资源管理器的缓存构建方法是否存在"""
        try:
            from provision.service.resources import rm
            assert hasattr(rm, 'build_cache')
            assert callable(rm.build_cache)
        except ImportError:
            pytest.fail("ResourceManager build_cache method not found")

    def test_api_token_configuration(self):
        """测试 API token 配置是否存在"""
        try:
            from provision.conf import cfg

            # 尝试加载配置
            cfg.load_config()
            # 检查配置中是否有 api_token 字段
            assert hasattr(cfg.provision, 'api_token')
        except (ImportError, AttributeError, FileNotFoundError):
            # 在测试环境中配置文件可能不存在，这是正常的
            pytest.skip("API token configuration not available in test environment")

    def test_blog_meta_structure(self):
        """测试博客 meta 数据结构"""
        # 这是一个有效的博客文章 meta 结构
        valid_blog_meta = {
            "course": "blog",
            "resource": "articles",
            "seq": 1,
            "title": "测试博客文章",
            "description": "这是一个测试博客文章",
            "rel_path": "test_article.ipynb",
            "division": "blog",
            "price": 0.0,
            "price_tier_id": None
        }

        # 验证所有必需字段都存在（seq 会自动生成）
        required_fields = ["course", "resource", "title", "rel_path", "division"]
        for field in required_fields:
            assert field in valid_blog_meta

        # 验证博客特定字段
        assert valid_blog_meta["course"] == "blog"
        assert valid_blog_meta["division"] == "blog"
        assert valid_blog_meta["resource"] == "articles"
