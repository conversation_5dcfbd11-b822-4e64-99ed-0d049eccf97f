"""Tests for the scheduler module."""

from unittest.mock import MagicMock, patch

import pytest
from freezegun import freeze_time

from provision.tasks.scheduler import (
    init_scheduler,
    scheduler,
    start_scheduler,
    stop_scheduler,
    update_resource_access,
)


@pytest.fixture
def mock_cm():
    """Mock ResourceManager for testing."""
    with patch("provision.tasks.scheduler.cm") as mock:
        yield mock


def test_update_resource_access(mock_cm):
    """Test update_resource_access function."""
    # 调用函数
    update_resource_access()

    # 验证调用
    mock_cm.build_cache.assert_called_once()


def test_init_scheduler():
    """Test init_scheduler function."""
    # 确保调度器没有运行
    if scheduler.running:
        scheduler.shutdown()

    # 调用函数
    init_scheduler()

    # 验证调度器已初始化但未运行
    assert not scheduler.running
    assert len(scheduler.get_jobs()) == 1
    assert scheduler.get_jobs()[0].id == "update_resource_access"


def test_start_scheduler():
    """Test start_scheduler function."""
    # 确保调度器没有运行
    if scheduler.running:
        scheduler.shutdown()

    # 初始化调度器
    init_scheduler()

    # 调用函数
    start_scheduler()

    # 验证调度器已启动
    assert scheduler.running

    # 清理
    scheduler.shutdown()


def test_stop_scheduler():
    """Test stop_scheduler function."""
    # 确保调度器没有运行
    if scheduler.running:
        scheduler.shutdown()

    # 初始化并启动调度器
    init_scheduler()
    start_scheduler()

    # 验证调度器已启动
    assert scheduler.running

    # 调用函数
    stop_scheduler()

    # 验证调度器已停止
    assert not scheduler.running


@freeze_time("2023-01-01")
def test_scheduler_job_execution():
    """Test that the scheduler job executes correctly."""
    # 确保调度器没有运行
    if scheduler.running:
        scheduler.shutdown()

    # 创建一个模拟的更新函数
    mock_update = MagicMock()

    # 添加任务
    scheduler.add_job(
        mock_update,
        "interval",
        minutes=15,
        id="test_job",
        replace_existing=True,
    )

    # 启动调度器
    scheduler.start()

    # 验证任务已添加
    assert len(scheduler.get_jobs()) == 1
    assert scheduler.get_jobs()[0].id == "test_job"

    # 清理
    scheduler.shutdown()
