"""Unit test package for provision."""

import datetime
import os
from pathlib import Path

import pytest

from provision.conf import cfg
from provision.store.db import pg


@pytest.fixture(scope="function")
def setup_test_env():
    global cfg
    test_assets = Path(__file__).parent.parent / "assets"
    conf_dir = Path(__file__).parent / "assets/conf"

    print(f"test assets dir:{test_assets}")
    print(f"using config files  from:{conf_dir}")

    os.environ["PROVISION_CONFIG_DIR"] = str(conf_dir)

    # 设置环境变量后，重新加载配置文件
    cfg.load_config()

    for course in ("fa", "blog", "l24", "pandas"):
        obj = getattr(cfg.resources, course)
        setattr(obj, "home", test_assets / "course" / course)

    # 使用测试数据库连接
    pg.connect("postgresql://postgres:postgres@localhost:5432/provision_test")
    pg.init_db()


@pytest.fixture(scope="function")
def add_customer_aaron():
    sql = """INSERT INTO customers (account, password, nickname, salt, phone, wx) VALUES (%s, %s, %s, %s, %s, %s)"""
    args = ("aaron", "aaron", "aa", "", "186", "aaron")
    cursor = pg.conn.cursor()
    cursor.execute(sql, args)
    pg.conn.commit()


def add_course_fa(start_time: datetime.datetime, plan="bronze"):
    sql = """INSERT INTO registry (course, customer_id, plan, start_time, refund_end, prime_end, retention_end, expire_time, is_active, division)
             VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""

    args = (
        "fa",
        1,
        plan,
        start_time,
        start_time + datetime.timedelta(days=7),
        start_time + datetime.timedelta(days=15),
        start_time + datetime.timedelta(days=3),
        start_time + datetime.timedelta(days=30),
        True,
        "course",
    )

    cursor = pg.conn.cursor()
    cursor.execute(sql, args)
    pg.conn.commit()


def add_whitelist(customer_id: int, resource_id: int, course: str):
    sql = "INSERT INTO resource_whitelist (customer_id, resource_id, course) VALUES (%s, %s, %s)"
    args = (customer_id, resource_id, course)

    cursor = pg.conn.cursor()
    cursor.execute(sql, args)
    pg.conn.commit()
