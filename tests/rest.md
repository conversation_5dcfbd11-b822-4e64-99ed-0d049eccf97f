## Usage

选中一段文本，然后运行restclient > send request

### Register

POST http://localhost:8403/api/course/register HTTP/1.1
content-type: application/json

{
    "uid": "aaron",
    "course": "fa",
    "plan": "silver"
}

POST http://localhost:8403/api/customer/register HTTP/1.1
content-type: application/json

{
    "nickname": "hero",
    "account": "aaron",
    "phone": "***********",
}


POST http://localhost:8403/api/customer/register HTTP/1.1
content-type: application/json
Referer: http://your-domain.com/registration-page

{
    "nickname": "hero",
    "account": "aaron",
    "phone": "***********"
}

GET http://localhost:8080/course/fa/aaron/courseware/lesson01.ipynb
