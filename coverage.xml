<?xml version="1.0" ?>
<coverage version="7.7.0" timestamp="1742361769294" lines-valid="11" lines-covered="0" line-rate="0" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.7.0 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/workspace/provision/backend/provision</source>
	</sources>
	<packages>
		<package name="." line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
					</lines>
				</class>
				<class name="app.py" filename="app.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="cli.py" filename="cli.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="3" hits="0"/>
						<line number="6" hits="0"/>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="10" hits="0"/>
						<line number="13" hits="0"/>
						<line number="15" hits="0"/>
						<line number="18" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
