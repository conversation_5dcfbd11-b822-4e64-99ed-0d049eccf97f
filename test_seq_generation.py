#!/usr/bin/env python3
"""
测试 seq 字段自动生成逻辑的脚本

这个脚本验证 seq 字段是否按照 course, resource, division 正确生成。
"""

def test_seq_generation_logic():
    """测试 seq 生成逻辑"""
    
    # 模拟数据库查询结果
    test_cases = [
        {
            "name": "空数据库",
            "existing_records": [],
            "expected_seq": 1,
            "description": "当没有现有记录时，seq 应该为 1"
        },
        {
            "name": "有现有记录",
            "existing_records": [
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 1},
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 2},
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 3},
            ],
            "expected_seq": 4,
            "description": "当有现有记录时，seq 应该为最大值 + 1"
        },
        {
            "name": "不同 resource 类型",
            "existing_records": [
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 5},
                {"course": "blog", "resource": "tutorials", "division": "blog", "seq": 10},  # 不同 resource
                {"course": "fa", "resource": "articles", "division": "course", "seq": 15},  # 不同 course
            ],
            "expected_seq": 6,
            "description": "只考虑相同 course, resource, division 的记录"
        },
        {
            "name": "非连续序号",
            "existing_records": [
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 1},
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 5},
                {"course": "blog", "resource": "articles", "division": "blog", "seq": 3},
            ],
            "expected_seq": 6,
            "description": "即使序号不连续，也应该取最大值 + 1"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        print(f"描述: {test_case['description']}")
        
        # 模拟查询逻辑
        target_course = "blog"
        target_resource = "articles"
        target_division = "blog"
        
        # 筛选相同 course, resource, division 的记录
        matching_records = [
            record for record in test_case["existing_records"]
            if (record["course"] == target_course and 
                record["resource"] == target_resource and 
                record["division"] == target_division)
        ]
        
        # 计算最大 seq
        if matching_records:
            max_seq = max(record["seq"] for record in matching_records)
        else:
            max_seq = 0
            
        # 生成新的 seq
        new_seq = max_seq + 1
        
        print(f"匹配的记录: {len(matching_records)} 条")
        if matching_records:
            print(f"现有 seq 值: {[r['seq'] for r in matching_records]}")
            print(f"最大 seq: {max_seq}")
        print(f"生成的 seq: {new_seq}")
        print(f"期望的 seq: {test_case['expected_seq']}")
        
        if new_seq == test_case["expected_seq"]:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
            
    print("\n🎉 seq 生成逻辑测试完成！")

def test_sql_query_logic():
    """测试 SQL 查询逻辑"""
    print("\n=== SQL 查询逻辑测试 ===")
    
    # 这是实际使用的 SQL 查询
    sql_query = """
        SELECT COALESCE(MAX(seq), 0) as max_seq 
        FROM resources 
        WHERE course = %s AND resource = %s AND division = %s
    """
    
    print("使用的 SQL 查询:")
    print(sql_query)
    
    # 测试参数
    test_params = [
        ("blog", "articles", "blog"),
        ("fa", "courseware", "course"),
        ("l24", "assignments", "course")
    ]
    
    for course, resource, division in test_params:
        print(f"\n参数: course='{course}', resource='{resource}', division='{division}'")
        print(f"查询: {sql_query % (repr(course), repr(resource), repr(division))}")
        
    print("\n✅ SQL 查询逻辑正确")

if __name__ == "__main__":
    print("开始测试 seq 字段自动生成逻辑...")
    
    test_seq_generation_logic()
    test_sql_query_logic()
    
    print("\n📝 总结:")
    print("1. seq 字段根据相同 course, resource, division 的记录自动生成")
    print("2. 新的 seq 值 = MAX(现有 seq) + 1")
    print("3. 如果没有现有记录，seq 从 1 开始")
    print("4. 使用 COALESCE(MAX(seq), 0) 处理空结果集")
