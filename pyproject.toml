[tool]
[tool.poetry]
name = "provision"
version = "0.5.1"
homepage = "https://github.com/zillionare/provision"
description = "Provision system for course customer management with authentication."
authors = ["aaron yang <<EMAIL>>"]
readme = "README.md"
classifiers=[
    'Development Status :: 2 - Pre-Alpha',
    'Intended Audience :: Developers',
    'Natural Language :: English',
    'Programming Language :: Python :: 3.13',
]
packages = [
    { include = "provision" },
    { include = "tests", format = "sdist" },
]

include = [
    { path = "provision/static/**/*",  format = ["sdist", "wheel"] }
]

[tool.poetry.dependencies]
python = "^3.12.0"
blacksheep = "2.1.0"
uvicorn = {extras = ["standard"], version = "^0.34.0"}
pydantic-settings = "^2.8.1"
loguru = "^0.7.3"
fire = "^0.7.0"
pexpect = "^4.9.0"
rich = "^13.9.4"
pyjwt = "^2.10.1"
arrow = "^1.3.0"
python-daemon = "^3.1.2"
alibabacloud-dysmsapi20170525 = "^2.0.24"
alibabacloud-tea-openapi = "^0.3.8"
datamodel-code-generator = "^0.30.1"
nbformat = "^5.10.4"
pytest-timeout = "^2.4.0"
pandas = "^2.2.3"
pytest = "^8.3.5"
pillow = "^11.2.1"
psycopg2-binary = "^2.9.10"

[tool.poetry.extras]
test = [
    "pytest",
    "pytest-cov"
    ]

dev = [
    "tox",
    "black",
    "isort",
    "flake8",
    "mypy",
    "pytest-asyncio"
    ]

[tool.poetry.scripts]
provision = 'provision.cli:main'

[tool.poetry.group.dev.dependencies]
pylint-pydantic = "^0.3.5"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | \.history
)/
'''
[tool.isort]
profile = "black"

[tool.pytest.ini_options]
markers = [
    "asyncio: asyncio mark"
]
