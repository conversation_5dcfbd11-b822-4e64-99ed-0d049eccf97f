用来记录augment的闪光点。

当我们把前端的一个目录，由course重命名为academy,要求它更新引用时，它不仅更新了vite.config.ts,而且更新了architecture.md！
checkpoint

## next edit

“Next Edit” 在编程工具 “Augment Code” 中有特定含义。它是 “Augment Code” 的一个功能，能够根据用户最近的操作，智能预测用户下一步可能要进行的编辑点，用户可一键接受并应用更改，有助于减少编码中的重复性劳动。例如，当用户在进行代码编写或修改时，“Next Edit” 会给出相关的编辑建议，用户可以通过快捷键快速跳转到建议的编辑位置，并选择接受或拒绝该建议。

## checkpoint
当我们使用sprint来标记功能点时，augment就会自动用sprint来给checkpoint命名，这样一来，相当于git 的tag功能。

![checkpoint-with-sprint](https://images.jieyu.ai/images/2025/04/20250416185810.png)

## sprint
最初augment把sprint.md创建在项目根目录下。但自从我有一次让它移动到.dev/tasks目录下之后，它就记住了这个位置（但是后来它自创建了一个名为sprints的目录，这也合理）。然后，当我要求它结束某个sprint之后，它对文档进行了总结，然后创建了一个新的sprint_plan.md。

## 最新文档问题

例子： blacksheep支持SPA，它也应该通过app.serve_files(
        admin_path,
        root_path="admin",
        fallback_document="index.html"
    )
但需要指定fallback_document。这个部分在我们最初开发的时候（2025年4月初），文档并没有明确地指出来，所以，augment也不知道这一知识。这样导致augment在实现spa的路由时，只要不用urlhash方法，就无论如何也做不对。最终，blacksheep在2025年4月17日更新了文档，明确地指出来spa模式下的用法：

![](https://images.jieyu.ai/images/2025/04/blacksheep-update-their-docs.png)

当我把这个文档提交给augment时，它很快调整了方案，做出了正确的实施。

## 使用技巧

如果窗口闪烁，可以切换另一个窗口，让augment先运行一会。

## 问题

augment在消除overflow时，使用了作弊的方法，即将overflow都设置为hidden。这样会导致一些内容无法显示。

## user guidlines

1. 不要过度设计，代码要简洁。函数的复杂度满足编辑器规则的要求。
2. 代码提交之前，先进行lint，确保风格统一。Python端使用PEP8，vue端使用它官方的风格指南
3. 如果一个方案被否决，那么，之前产生的垃圾代码要也清理干净。
4. 变量定义不要使用python关键字、内置函数，比如input, in, global, id等。
5. 数据库表字段定义不要使用数据库保留关键字，比如restricted, desc等。
6. 如果你的结论是正确、可通过测试的，就应该坚持自己的意见，不要迎合我的意见

