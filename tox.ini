[tox]
isolated_build = true
envlist = py313

[gh-actions]
python =
    3.13: py313

[testenv]
passenv = *
setenv =
    PYTHONPATH = {toxinidir}
    PYTHONWARNINGS = ignore
deps =
    poetry
    docker
    pytest
    pytest-asyncio
    requests
extras =
    test
allowlist_externals =
    docker
commands =
    pip install --upgrade setuptools
    poetry run pytest -s --cov=provision --cov-append --cov-report=xml --cov-report term-missing tests
[testenv:docker]
description = Run tests that require docker container
passenv = *
setenv =
    PYTHONPATH = {toxinidir}
    PYTHONWARNINGS = ignore
deps =
    poetry
    docker
    pytest
    pytest-asyncio
    requests
extras =
    test
allowlist_externals =
    docker
commands =
    docker --version
    pip install --upgrade setuptools
    poetry run pytest -s --cov=provision tests/test_auth_check.py::test_nginx_auth_simulation

